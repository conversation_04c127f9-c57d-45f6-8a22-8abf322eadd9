<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Composed" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Status bar color. -->
        <item name="android:statusBarColor" >?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.Composed.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="StartingWindowTheme" parent="Theme.Composed.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
    </style>

</resources>