package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.common.UserImageView
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth


@Composable
fun TopItemsLeft(modifier: Modifier = Modifier) {
    Column(modifier.padding(start = padding4)) {
        UserImageView(
            modifier = Modifier.size(userHeadWidth, userHeadHeight),
            headRes = GameApp.instance.getAvatarUrl(),
            name = GameApp.instance.getUserName() ?: stringResource(R.string.not_login),
            onClick = {
                if (!GameApp.instance.hasLogin()) {
                    GameApp.instance.login(GameApp.instance.activity)
                } else {
                    Dialogs.editUserInfoDialog.value = true
                }
            },
            showEditIcon = GameApp.instance.hasLogin()
        )
    }
}