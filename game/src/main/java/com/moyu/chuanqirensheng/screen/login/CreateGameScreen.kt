package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.difficult.ui.SelectDifficultLayout
import com.moyu.chuanqirensheng.feature.difficult.ui.SelectMapLayout
import com.moyu.chuanqirensheng.feature.router.popTop
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.story.ui.SelectHeroLayout
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.InitAllyCardsView
import com.moyu.chuanqirensheng.screen.ally.MAX_ALLY_SKILL_SIZE
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.ui.theme.createCountryPanelHeight
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun CreateGameScreen() {
    Column(
        Modifier
            .fillMaxSize()
            .padding(top = WindowInsets.systemBars.asPaddingValues().calculateTopPadding())
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly
    ) {
        SelectHeroLayout()
        InitAllyCardLayout()
        SelectDifficultLayout()
        SelectMapLayout()
        Row(
            modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            GameButton(
                text = stringResource(R.string.return_text),
                buttonStyle = ButtonStyle.Blue,
                buttonSize = ButtonSize.Big
            ) {
                popTop()
            }
            GameButton(text = stringResource(R.string.start),
                buttonStyle = ButtonStyle.Orange,
                buttonSize = ButtonSize.Big,
                onClick = {
                    if (StoryManager.selectedEndless() && BattleManager.getGameAllies().isEmpty()) {
                        GameApp.instance.getWrapString(R.string.endless_no_master_tips).toast()
                    } else {
                        if (repo.allyManager.data.filter { it.isHero() }.none { it.selected }) {
                            GameApp.instance.getWrapString(R.string.need_master).toast()
                        } else {
                            repo.startGame()
                            GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                        }
                    }
                })
        }
    }
}

@Composable
fun InitAllyCardLayout() {
    Box {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .height(createCountryPanelHeight)
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding8, horizontal = padding19)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding14))
            InitAllyCardsView(modifier = Modifier,
                allies = if (StoryManager.selectedEndless()) BattleManager.getGameAllies() else BattleManager.getGameAlliesNoMaster(),
                capacity = MAX_ALLY_SKILL_SIZE,
                allyClick = {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        BattleManager.selectToGame(it)
                    }
                }) {
                Dialogs.selectAllyToGameDialog.value = false
            }
        }
        TextLabel2(modifier = Modifier.graphicsLayer {
            translationY = -padding16.toPx()
        }, text = stringResource(R.string.select_ally), labelSize = LabelSize.Medium)
    }
}

