package com.moyu.chuanqirensheng.screen.award

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.draw.canShowEffect
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.logic.indexToResourceIcon
import com.moyu.chuanqirensheng.logic.indexToResourceName
import com.moyu.chuanqirensheng.logic.indexToResourceTips
import com.moyu.chuanqirensheng.logic.levelFrame
import com.moyu.chuanqirensheng.logic.skill.getSkillFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.MaskView
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.awardGif
import com.moyu.chuanqirensheng.screen.effect.orangeItemGif
import com.moyu.chuanqirensheng.screen.equip.DropBattlePropertyLine
import com.moyu.chuanqirensheng.screen.equip.DropPropertyLine
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.skill.isAdvBuilding
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.model.skill.isMagic
import kotlin.math.roundToInt

@Composable
fun AwardDialog(show: MutableState<Award?>) {
    show.value?.let {
        val button = stringResource(id = R.string.confirm)
        PanelDialog(onDismissRequest = {
            show.value = null
        }, contentBelow = {
            GameButton(text = button, buttonStyle = ButtonStyle.Orange, onClick = {
                show.value = null
            })
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    stringResource(R.string.awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                AwardList(
                    award = it,
                    param = defaultParam.copy(textColor = Color.Black),
                    mainAxisAlignment = Arrangement.spacedBy(
                        padding10
                    )
                )
            }
            ForeverGif(
                Modifier
                    .width(dialogWidth), awardGif.gif, awardGif.count, needGap = true
            )
        }
    }
}

data class AwardUIParam(
    val showName: Boolean = true,
    val itemSize: ItemSize = ItemSize.Large,
    val frameDrawable: Int? = R.drawable.item_frame_new,
    val noFrameForItem: Boolean = false,
    val showNum: Boolean = true,
    val textFrameDrawable: Int? = null,
    val solidFrameDrawable: Int? = null,
    val textFrameDrawableYPadding: Dp = padding0,
    val numInFrame: Boolean = true,
    val frameZIndex: Float = 0f,
    val peek: Boolean = false,
    val checkAffordable: Boolean = false,
    val showColumn: Boolean = true,
    val textColor: Color = Color.White,
    val showEffect: Boolean = false,
    val replaceIcon: Int? = null,
    val replaceText: String? = null,
    val minLine: Int = 1,
    val maxLine: Int = 2,
    val softWrap: Boolean = true,
    val showReputationLevel: Boolean = false,
    val callback: (() -> Unit)? = null,
)

val defaultParam = AwardUIParam()

@Composable
fun AwardList(
    modifier: Modifier = Modifier,
    award: Award,
    mainAxisAlignment: Arrangement.Horizontal = Arrangement.Start,
    param: AwardUIParam = defaultParam,
    paddingHorizontalInDp: Dp = padding10,
    paddingVerticalInDp: Dp = padding10,
    merge: Boolean = true
) {
    FlowRow(
        modifier = modifier.padding(
            horizontal = paddingHorizontalInDp,
            vertical = paddingVerticalInDp,
        ),
        horizontalArrangement = mainAxisAlignment,
        overflow = FlowRowOverflow.Visible,
    ) {
        if (merge) {
            award.outAllies.groupBy { it.id }.forEach {
                val ally = it.value.first()
                val size = it.value.sumOf { it.num }
                val race = repo.gameCore.getRaceById(ally.id)
                LaunchedEffect(ally.mainId) {
                    GameIllustrationManager.unlockAlly(ally)
                }
                Box {
                    SingleAwardItem(
                        name = ally.name,
                        drawable = getImageResourceDrawable(race.getHeadIcon()),
                        num = if (size > 1) "+$size" else "",
                        stars = 0,
                        contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                        alignment = Alignment.TopCenter,
                        param = param.copy(showEffect = (ally.canShowEffect()),
                            frameDrawable = if (ally.isHero()) R.drawable.item_frame_new else ally.quality.getQualityFrame(),
                            callback = {
                                param.callback?.invoke() ?: run {
                                    Dialogs.allyDetailDialog.value = ally.copy(peek = true)
                                }
                            }),
                    )
                    Box(
                        Modifier
                            .size(param.itemSize.itemSize / 2.4f)
                            .align(Alignment.TopEnd)
                            .graphicsLayer {
//                            translationX = (param.itemSize.itemSize / 6).toPx()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = ally.getQualityFrame()),
                            contentDescription = null
                        )
                        Text(
                            text = ally.star.toString(),
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
        } else {
            award.outAllies.forEach { ally ->
                LaunchedEffect(ally.mainId) {
                    GameIllustrationManager.unlockAlly(ally)
                }
                Box {
                    SingleAwardItem(
                        name = ally.name,
                        drawable = getImageResourceDrawable(ally.getRace().getHeadIcon()),
                        num = if (ally.num > 1) "+${ally.num}" else "",
                        stars = 0,
                        contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                        alignment = Alignment.TopCenter,
                        param = param.copy(showEffect = ally.canShowEffect(),
                            frameDrawable = if (ally.isHero()) R.drawable.item_frame_new else ally.quality.getQualityFrame(),
                            callback = {
                                param.callback?.invoke() ?: run {
                                    Dialogs.allyDetailDialog.value = ally.copy(peek = true)
                                }
                            }),
                    )
                    Box(
                        Modifier
                            .size(param.itemSize.itemSize / 2.4f)
                            .align(Alignment.TopEnd)
                            .graphicsLayer {
//                            translationX = (param.itemSize.itemSize / 6).toPx()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = ally.getQualityFrame()),
                            contentDescription = null
                        )
                        Text(
                            text = ally.star.toString(),
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
        }
        award.couponAlly.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.ally_coupon),
                drawable = R.drawable.coupon_ally_icon,
                num = "+${it}",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.ally_coupon_tips).toast()
                }),
            )
        }
        award.couponHero.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.hero_coupon),
                drawable = R.drawable.hero_coupon_icon,
                num = "+${it}",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.hero_coupon_tips).toast()
                }),
            )
        }
        award.realMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.real_money_title),
                drawable = R.drawable.real_money_icon,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame()) {
                    GameApp.instance.getWrapString(R.string.real_money_tips).toast()
                },
            )
        }
        award.key.takeIf { it > 0 }?.let {
            if (award.extraKey > 0) {
                SingleAwardItem(
                    name = stringResource(id = R.string.key_title),
                    drawable = R.drawable.common_key,
                    num = "+${it - award.extraKey}(${award.extraKey})",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.key_tips).toast()
                    }),
                )
            } else {
                SingleAwardItem(
                    name = stringResource(id = R.string.key_title),
                    drawable = R.drawable.common_key,
                    num = "+$it",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.key_tips).toast()
                    }),
                ) {
                    AwardManager.isAffordable(Award(key = it))
                }
            }
        }
        award.diamond.takeIf { it > 0 }?.let {
            if (award.extraDiamond > 0) {
                SingleAwardItem(
                    name = stringResource(id = R.string.diamond_title),
                    drawable = R.drawable.common_medal,
                    num = "+${it - award.extraDiamond}(${award.extraDiamond})",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.diamond_tips).toast()
                    }),
                )
            } else SingleAwardItem(
                name = stringResource(id = R.string.diamond_title),
                drawable = R.drawable.common_medal,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.diamond_tips).toast()
                }),
            ) {
                AwardManager.isAffordable(Award(diamond = it))
            }
        }
        award.pvpDiamond.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.pvp_diamond),
                drawable = R.drawable.pvp_diamond_icon,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.pvp_diamond_tips).toast()
                }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.pvpScore.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.pvp_score),
                drawable = R.drawable.pvp_icon,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.pvp_score_tips).toast()
                }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.pvp2Score.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.pvp2_score),
                drawable = R.drawable.pvp_icon,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.pvp2_score_tips).toast()
                }),
            ) {
                AwardManager.isAffordable(Award(pvpDiamond = it))
            }
        }
        award.lotteryMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.lottery_money),
                drawable = R.drawable.lottery_money,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.lottery_money_tips).toast()
                }),
            )
        }
        award.holidayMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.holiday_money),
                drawable = R.drawable.holiday_money,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.holiday_money_tips).toast()
                }),
            )
        }
        award.titleId.takeIf { it > 0 }?.let { titleId ->
            val title = repo.gameCore.getTitlePool().first { it.id == titleId }
            SingleAwardItem(
                name = title.name,
                drawable = getImageResourceDrawable("title_$titleId"),
                num = "",
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            )
        }
        award.titleLevel.takeIf { it > 0 }?.let { level ->
            SingleAwardItem(
                name = stringResource(R.string.title_level),
                drawable = null,
                num = stringResource(R.string.title_level) + "$level",
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            ) {
                BattleManager.yourTitle.value.level + 1 >= level
            }
        }
        award.allHeal.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.heal_award),
                drawable = R.drawable.heal_icon,
                num = "+${it}%",
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            )
        }
        award.battleHeal.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.heal_award),
                drawable = R.drawable.heal_icon,
                num = "+${it}%",
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            )
        }
        award.resources.forEachIndexed { index, i ->
            if (i != 0) {
                SingleAwardItem(
                    name = if (award.showQuestion) stringResource(R.string.random_resource) else index.indexToResourceName(),
                    drawable = if (award.showQuestion) R.drawable.random_resource else index.indexToResourceIcon(),
                    num = if (award.showQuestion) "+?" else "+$i",
                    param = param.copy(frameDrawable = 3.getQualityFrame()).copy(callback = {
                        param.callback?.invoke() ?: run {
                            if (award.showQuestion) {
                                GameApp.instance.getWrapString(R.string.random_resource).toast()
                            } else {
                                index.indexToResourceTips().toast()
                            }
                        }
                    }),
                    affordable = {
                        AwardManager.isAffordable(award.copy(resources = EMPTY_RESOURCES.toMutableList().apply {
                            set(index, i)
                        }))
                    }
                )
            }
        }
        award.warPass.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.war_pass_exp),
                drawable = R.drawable.item_battlepass,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.war_pass_tips).toast()
                }),
            )
        }
        award.warPass2.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.war_pass2_exp),
                drawable = R.drawable.item_battlepass2,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.war_pass2_tips).toast()
                }),
            )
        }
        award.exp.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.exp),
                drawable = R.drawable.icon_exp,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.exp_tips).toast()
                }),
            )
        }
        award.advProperty.takeIf { it.isNotEmpty() }?.DropPropertyLine(param = param)
        award.battleProperty.takeIf { it.isNotEmpty() }?.DropBattlePropertyLine(param = param)
        award.skills.forEach {
            LaunchedEffect(it.mainId) {
                GameIllustrationManager.unlockSkill(it)
            }
            Box {
                // todo 魔法行会的奖励是随机的，所以要显示问号，仅魔法要显示问号
                SingleAwardItem(
                    name = if (award.showQuestion && it.isMagic()) stringResource(R.string.random_skill) else it.name,
                    drawable = if (award.showQuestion && it.isMagic()) R.drawable.random_magic else getImageResourceDrawable(
                        it.icon
                    ),
                    num = if (it.isAdvBuilding()) stringResource(id = R.string.skill_sheet3)
                    else if (it.isBattleTree()) stringResource(
                        id = R.string.skill_sheet1
                    ) else "",
                    stars = if (it.isAdvBuilding()) it.level else -1,
                    contentScale = if (award.showQuestion && it.isMagic()) ContentScale.Fit else ContentScale.Crop,
                    param = param.copy(frameDrawable = getSkillFrame(), frameZIndex = 999f, callback = {
                        param.callback?.invoke() ?: run {
                            if (award.showQuestion && it.isMagic()) {
                                GameApp.instance.getWrapString(R.string.random_skill).toast()
                            } else {
                                Dialogs.skillDetailDialog.value = it.copy(peek = true)
                            }
                        }
                    }),
                )
                if (it.isMagic()) {
                    Box(
                        Modifier
                            .size(param.itemSize.itemSize / 2.4f)
                            .align(Alignment.TopEnd)
                            .graphicsLayer {
//                            translationX = -(param.itemSize.itemSize / 6).toPx()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = it.quality().levelFrame()),
                            contentDescription = null
                        )
                        Text(
                            text = it.level.toString(),
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
        }
        award.equips.forEach {
            SingleAwardItem(
                name = it.name,
                drawable = getImageResourceDrawable(it.pic),
                stars = it.star,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                param = param.copy(frameDrawable = it.quality.getQualityFrame(), callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.equipDetailDialog.value = it.copy(peek = true)
                    }
                }),
            )
        }
        // todo 只显示第一个升级兵种，原因是pool14需要把15个等级的升级都塞进去，显示就太多了
        award.upgradeAllies.getOrNull(0)?.let {
            LaunchedEffect(it.mainId) {
                GameIllustrationManager.unlockAlly(it)
            }
            val upgraded = it.upgrade()
            val race = repo.gameCore.getRaceById(upgraded.id)
            SingleAwardItem(
                name = upgraded.name,
                drawable = getImageResourceDrawable(race.getHeadIcon()),
                stars = 0,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = upgraded.quality.getQualityFrame(), callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.allyDetailDialog.value = upgraded.copy(peek = true)
                    }
                }),
            )
        }
        award.allies.forEach {
            LaunchedEffect(it.mainId) {
                GameIllustrationManager.unlockAlly(it)
            }
            val race = repo.gameCore.getRaceById(it.id)
            Box {
                SingleAwardItem(
                    name = if (it.num == 1) it.name else it.name + "+${it.num}",
                    drawable = getImageResourceDrawable(race.getHeadIcon()),
                    stars = 0,
                    contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                    alignment = Alignment.TopCenter,
                    param = param.copy(frameDrawable = if (it.isHero()) R.drawable.item_frame_new else it.quality.getQualityFrame(), callback = {
                        param.callback?.invoke() ?: run {
                            Dialogs.allyDetailDialog.value = it.copy(peek = true)
                        }
                    }),
                )
                Box(
                    Modifier
                        .size(param.itemSize.itemSize / 2.4f)
                        .align(Alignment.TopEnd)
                        .graphicsLayer {
//                            translationX = -(param.itemSize.itemSize / 6).toPx()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = it.getQualityFrame()),
                        contentDescription = null
                    )
                    Text(
                        text = it.star.toString(),
                        style = MaterialTheme.typography.h4
                    )
                }
            }
        }
        award.loseAllies.forEach {
            val race = repo.gameCore.getRaceById(it.id)
            SingleAwardItem(
                name = it.name,
                num = "-1",
                drawable = getImageResourceDrawable(race.pic),
                stars = 0,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = it.quality.getQualityFrame(), callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.allyDetailDialog.value = it.copy(peek = true)
                    }
                }),
            )
        }
        award.unlockList.forEach {
            val unlock = repo.gameCore.getUnlockById(it)
            SingleAwardItem(
                name = unlock.name,
                drawable = getImageResourceDrawable(unlock.icon),
                contentScale = ContentScale.Crop,
                // if (it.unlockIdIsStory()) R.drawable.hero_frame2 else
                param = param.copy(frameDrawable = 3.getQualityFrame(),
                    callback = {
                        unlock.name.toast()
                    }),
            )
        }
        award.electric.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.electric_title),
                drawable = R.drawable.common_charge,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.electric_tips).toast()
                }),
            )
        }
    }
}

@Composable
fun SingleAwardItem(
    name: String,
    drawable: Int?,
    num: String = "",
    stars: Int = -1,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    if (param.showColumn) {
        Column(
            modifier = Modifier
                .width(param.itemSize.frameSize + padding2)
                .padding(vertical = padding4),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            InnerAwardItem(
                name = name,
                drawable = drawable,
                num = num,
                stars = stars,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    } else {
        Row(
            modifier = Modifier.padding(horizontal = padding2),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            InnerAwardItem(
                name = name,
                drawable = drawable,
                num = num,
                stars = stars,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    }
}

@Composable
fun InnerAwardItem(
    name: String,
    drawable: Int?,
    num: String = "",
    stars: Int = -1,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    val realNum = if (param.checkAffordable) {
        // 如果是条件展示，不需要+号
        num.replace("+", " ")
    } else if (num.contains("-")) {
        // 如果是扣减，去掉+号
        num.replace("+", "")
    } else num
    EffectButton(onClick = {
        param.callback?.invoke() ?: run { name.toast() }
    }) {
        param.solidFrameDrawable?.let {
            Image(
                modifier = Modifier
                    .size(param.itemSize.frameSize)
                    .zIndex(param.frameZIndex),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(it),
                contentDescription = null
            )
        }?: param.frameDrawable?.let {
            if (!param.noFrameForItem) {
                Image(
                    modifier = Modifier
                        .size(param.itemSize.frameSize)
                        .zIndex(param.frameZIndex),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(it),
                    contentDescription = null
                )
            }
        }
        param.replaceIcon?.let {
            Image(
                modifier = Modifier
                    .size(param.itemSize.itemSize)
                    .clip(RoundedCornerShape(padding4)),
                alignment = alignment,
                contentScale = contentScale,
                painter = painterResource(it),
                contentDescription = name
            )
        } ?: drawable?.let {
            Image(
                modifier = Modifier
                    .size(param.itemSize.itemSize)
                    .clip(RoundedCornerShape(padding4)),
                alignment = alignment,
                contentScale = contentScale,
                painter = painterResource(drawable),
                contentDescription = name
            )
        }
        if (realNum.isNotEmpty() && param.numInFrame) {
            if (param.showNum) {
                MaskView(
                    modifier = Modifier.align(Alignment.BottomCenter),
                    text = realNum,
                    itemSize = param.itemSize
                )
            }
        } else if (stars != -1) {
            Stars(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = padding4),
                stars = stars,
                starWidth = param.itemSize.itemSize / 4
            )
        }
        if (param.showEffect) {
            val infiniteTransition = rememberInfiniteTransition(label = "")
            val index = infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = orangeItemGif.count.toFloat(),
                animationSpec = infiniteRepeatable(
                    animation = tween(3800, easing = LinearEasing),
                    repeatMode = RepeatMode.Restart,
                ),
                label = ""
            )
            if (index.value.roundToInt() <= orangeItemGif.count) { // 做一个间歇的效果
                Image(
                    modifier = Modifier
                        .size(param.itemSize.frameSize)
                        .scale(1.8f)
                        .graphicsLayer {
                            translationX = padding2.toPx()
                            translationY = -padding2.toPx()
                        }, painter = painterResource(
                        getImageResourceDrawable(
                            "${orangeItemGif.gif}${index.value.roundToInt()}"
                        )
                    ), contentDescription = null
                )
            }
        }
    }
    Spacer(modifier = Modifier.height(padding2))
    Box(contentAlignment = Alignment.Center) {
        param.textFrameDrawable?.let {
            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .scale(1.8f)
                    .graphicsLayer {
                        translationY = param.textFrameDrawableYPadding.toPx()
                        scaleY = 1.3f
                    },
                painter = painterResource(id = it),
                contentScale = ContentScale.FillWidth,
                contentDescription = null
            )
        }
        if (param.showName) {
            Text(
                text = param.replaceText ?: if (param.numInFrame) name else "$name$realNum",
                style = param.itemSize.getTextStyle(),
                maxLines = param.maxLine,
                minLines = param.minLine,
                softWrap = param.softWrap,
                overflow = TextOverflow.Visible,
                color = param.textColor,
                textAlign = TextAlign.Center,
            )
        } else {
            if (!param.numInFrame) {
                if (param.showNum) {
                    Text(
                        text = realNum,
                        style = param.itemSize.getTextStyle(),
                        maxLines = param.maxLine,
                        minLines = param.minLine,
                        softWrap = param.softWrap,
                        color = if (param.checkAffordable && !affordable()) DARK_RED else param.textColor,
                        overflow = TextOverflow.Visible,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}