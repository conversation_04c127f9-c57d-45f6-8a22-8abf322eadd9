package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.screen.common.EmptyIconView
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.equipGif
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.oneRoleWidth
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Ally

const val MAX_ALLY_SKILL_SIZE = 26

@Composable
fun InitAllyCardsView(
    modifier: Modifier = Modifier,
    allies: List<Ally>,
    capacity: Int,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: () -> Unit
) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(padding6),
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 5,
    ) {
        var skillCount = 0
        repeat(capacity) { index ->
            val unlock = UnlockManager.getInitAllyUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = padding2)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                allies.getOrNull(skillCount)?.takeIf { unlocked }?.let { ally ->
                    skillCount += 1
                    Box(contentAlignment = Alignment.Center) {
                        SingleAllyView(
                            ally = ally,
                            extraInfo = ally.extraInfo,
                            showName = false,
                            showNum = false,
                            itemSize = ItemSize.Large,
                            selectCallBack = allyClick,
                            textColor = Color.White
                        )
                        GifView(
                            modifier = Modifier.size(ItemSize.Large.frameSize),
                            gif.value,
                            equipGif.count,
                            equipGif.gif
                        )
                    }
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.Large, frame = R.drawable.ally_frame) {
                            if (unlocked) {
                                emptyClick()
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
                if (StoryManager.selectedEndless() && index == 0) {
                    Text(
                        text = stringResource(R.string.main_role),
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        modifier = Modifier.align(Alignment.Center).padding(top = padding2).background(
                            B65
                        )
                    )
                }
            }
        }
    }
}


@Composable
fun AllyCardsRow(
    modifier: Modifier = Modifier,
    allies: Map<Int, Ally>,
    capacity: Int,
    showName: Boolean = false,
    showHp: Boolean = false,
    textColor: Color = Color.Black,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: () -> Unit
) {
    FlowRow(
        modifier = modifier, horizontalArrangement = Arrangement.SpaceEvenly,
        verticalArrangement = Arrangement.spacedBy(padding16),
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 4
    ) {
        if (capacity == 1) {
            Spacer(modifier = Modifier.size(oneRoleWidth))
            AllyCard(allies, showName, showHp, textColor, allyClick, emptyClick, 1)
            Spacer(modifier = Modifier.size(oneRoleWidth))
        } else {
            repeat(capacity) { index ->
                AllyCard(allies, showName, showHp, textColor, allyClick, emptyClick, index)
            }
        }
    }
}

@Composable
fun AllyCard(
    allies: Map<Int, Ally>,
    showName: Boolean,
    showHp: Boolean,
    textColor: Color,
    allyClick: (Ally) -> Unit,
    emptyClick: () -> Unit,
    index: Int
) {
    Box(
        modifier = Modifier.width(oneRoleWidth),
        contentAlignment = Alignment.Center
    ) {
        allies[index]?.let { ally ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(contentAlignment = Alignment.Center) {
                    SingleAllyView(
                        ally = ally,
                        extraInfo = ally.extraInfo,
                        showName = false,
                        textColor = textColor,
                        itemSize = ItemSize.LargePlus,
                        showEffect = false,
                        frame = null,
                        showNum = false,
                        showHp = showHp
                    )
                    IconView(
                        Modifier
                            .align(Alignment.TopEnd)
                            .graphicsLayer {
                                translationY = -padding6.toPx()
                                translationX = padding6.toPx()
                            },
                        res = R.drawable.common_exit,
                        itemSize = ItemSize.Small,
                        frame = null,
                    ) {
                        allyClick(ally)
                    }
                    GifView(
                        modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                        true,
                        equipGif.count,
                        equipGif.gif
                    )
                }
                if (showName) {
                    Text(text = ally.name, style = MaterialTheme.typography.h5)
                }
            }
        } ?: run {
            Box(contentAlignment = Alignment.Center) {
                EmptyIconView(itemSize = ItemSize.LargePlus, showPlus = false, frame = R.drawable.battle_choose) {
                    emptyClick()
                }
            }
        }
    }
}
