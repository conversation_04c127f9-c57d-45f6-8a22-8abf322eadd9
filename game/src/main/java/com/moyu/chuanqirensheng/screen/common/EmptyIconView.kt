package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton


@Composable
fun EmptyIconView(
    modifier: Modifier = Modifier,
    frame: Int = R.drawable.item_frame_new,
    itemSize: ItemSize = ItemSize.LargePlus,
    showPlus: Boolean = true,
    callback: () -> Unit
) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        Image(
            modifier = Modifier.size(itemSize.frameSize),
            painter = painterResource(id = frame),
            contentDescription = null
        )
        if (showPlus) {
            Image(
                modifier = Modifier.size(itemSize.frameSize / 2),
                painter = painterResource(id = R.drawable.common_plus),
                contentDescription = stringResource(R.string.empty_slot)
            )
        }
    }
}
