package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.core.model.Ally


@Composable
fun SingleHeroCard(modifier: Modifier = Modifier, ally: Ally, showButton: Boolean = false) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        Box(Modifier.graphicsLayer {
            translationY = padding12.toPx()
        }) {
            SingleAllyView(ally = ally, showNum = false, frame = null, itemSize = ItemSize.Huge, textColor = Color.White)
        }
        if (showButton) {
            GameButton(
                text = stringResource(id = R.string.do_select),
                textColor = Color.White,
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Blue
            ) {
                repo.allyManager.selectToGame(ally)
            }
        }
    }
}
