package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.detail.EventAward3To1Layout
import com.moyu.chuanqirensheng.logic.getAwardDesc
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.dialog.EMPTY_DISMISS
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.cheatBarHeight
import com.moyu.chuanqirensheng.ui.theme.dialogHeight
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.property.Property
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun FatalEnemyDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val awards = remember {
            mutableStateListOf<Award>()
        }
        val finished = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            if (awards.isEmpty()) {
                event.playPara2.first().let {
                    if (it.toInt() == 0) {
                        awards.add(Award())
                    } else {
                        awards.addAll(
                            repo.gameCore.getPoolById(it.toInt()).toAwards()
                                .shuffled(RANDOM)
                        )
                    }
                }
            }
        }
        EmptyDialog(onDismissRequest = EMPTY_DISMISS) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dialogHeight)
                    .paint(
                        painterResource(id = R.drawable.common_window2),
                        contentScale = ContentScale.FillBounds
                    )
                    .scale(0.9f),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(padding4))
                TextLabel2(
                    labelSize = LabelSize.Huge,
                    text = stringResource(R.string.battle_win_reward)
                )
                Spacer(modifier = Modifier.height(padding6))
                val buttonText = listOf(0, 1, 2).map { index ->
                    awards.getOrNull(index)?.equips?.firstOrNull()?.let { equip ->
                        if (BattleManager.getGameEquips()
                                .any { it.mainId == awards[index].equips.first().mainId }
                        ) {
                            stringResource(id = R.string.do_star_up)
                        } else if (equip.isEquipReplaceable() && BattleManager.getGameEquips()
                                .any { it.type == awards[index].equips.first().type }
                        ) {
                            stringResource(id = R.string.replace)
                        } else {
                            stringResource(id = R.string.gain_title)
                        }
                    } ?: stringResource(id = R.string.gain_title)
                }
                if (awards.isNotEmpty() && awards.any { !it.isEmpty() }) {
                    EventAward3To1Layout(
                        awards = awards,
                        buttonTexts = buttonText,
                        maxLine = LanguageManager.getLine(),
                        minLin = LanguageManager.getLine(),
                        softWrap = LanguageManager.canSoftWrap(),
                        content = { index ->
                            FlowRow(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                verticalArrangement = Arrangement.Center,
                                overflow = FlowRowOverflow.Visible,
                                maxItemsInEachRow = 2
                            ) {
                                if (awards.getOrNull(index)?.equips?.firstOrNull()
                                        ?.isEquipReplaceable() == true
                                ) {
                                    val currentEquip = BattleManager.getGameEquips()
                                        .firstOrNull { it.type == awards[index].equips.first().type }
                                    val targetEquip = awards[index].equips.first()
                                    val compare = currentEquip?.getProperty() ?: Property()
                                    val showStarUp = currentEquip?.mainId == targetEquip.mainId
                                    val realTargetEquip =
                                        if (currentEquip?.mainId == targetEquip.mainId) {
                                            currentEquip.getNextLevelEquip()
                                        } else targetEquip
                                    realTargetEquip.getProperty().MainPropertyLine(
                                        originProperty = compare,
                                        showZero = false,
                                        showBoost = true,
                                        showNegative = true,
                                        showIcon = false,
                                        textColor = Color.White,
                                        textStyle = MaterialTheme.typography.h4
                                    )
                                    if (showStarUp && !finished.value) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Image(
                                                modifier = Modifier
                                                    .size(cheatBarHeight),
                                                painter = painterResource(R.drawable.hero_starup),
                                                contentDescription = null
                                            )
                                            Text(
                                                text = stringResource(id = R.string.do_star_up),
                                                style = MaterialTheme.typography.h4
                                            )
                                        }
                                    }
                                } else {
                                    Text(
                                        text = awards[index].getAwardDesc(),
                                        style = MaterialTheme.typography.h4
                                    )
                                }
                            }
                        },
                    ) {
                        if (!finished.value) {
                            finished.value = true
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                // 需要标记事件结束，否则对话无法正常进行
                                EventManager.getOrCreateHandler(event).eventResult.value = true
                                EventManager.getOrCreateHandler(event).eventFinished.value = true
                                EventManager.getOrCreateHandler(event).setEventAward(
                                    awards[it]
                                )
                                // 战斗胜利
                                BattleManager.onBattleEnd()
                                EventManager.doEventResult(event, true)
                            }
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    GameButton(
                        buttonStyle = ButtonStyle.Orange,
                        text = stringResource(id = R.string.quit),
                    ) {
                        finished.value = true
                        show.value = null
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            // 需要标记事件结束，否则对话无法正常进行
                            EventManager.getOrCreateHandler(event).eventResult.value = true
                            EventManager.getOrCreateHandler(event).eventFinished.value = true
                            // 战斗胜利
                            BattleManager.onBattleEnd()
                            EventManager.doEventResult(event, true)
                        }
                    }
                } else {
                    Spacer(modifier = Modifier.height(padding26))
                    Column(Modifier.padding(horizontal = padding22)) {
                        if (repo.gameCore.getPoolById(
                                event.playPara2.first().toInt()
                            ).type.first() == 4
                        ) {
                            Text(
                                text = stringResource(R.string.skill_not_able_to_learn1),
                                style = MaterialTheme.typography.h2,
                                color = Color.Black
                            )
                        } else {
                            Text(
                                text = stringResource(R.string.skill_not_able_to_learn2),
                                style = MaterialTheme.typography.h2,
                                color = Color.Black
                            )
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    GameButton(text = stringResource(id = R.string.quit)) {
                        finished.value = true
                        show.value = null
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            // 需要标记事件结束，否则对话无法正常进行
                            EventManager.getOrCreateHandler(event).eventResult.value = true
                            EventManager.getOrCreateHandler(event).eventFinished.value = true
                            // 战斗胜利
                            BattleManager.onBattleEnd()
                            EventManager.doEventResult(event, true)
                        }
                    }
                    Spacer(modifier = Modifier.height(padding26))
                }
            }
        }
    }
}


