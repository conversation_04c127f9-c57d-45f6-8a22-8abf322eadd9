package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.core.model.Ally

@Composable
fun AllyInGameStarUpDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        val nextAlly = repo.gameCore.getAllyPool()
            .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }
        val role = BattleManager.getRoleByAlly(ally)
        val nextRole = nextAlly?.let { BattleManager.getRoleByAlly(it) }
        EmptyDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                nextRole?.let {
                    AllySkillPanel(Modifier, ally, nextAlly, role, nextRole) {
                        Dialogs.allyInGameStarUpDialog.value = null
                    }
                    Spacer(modifier = Modifier.size(padding14))
                    DoAllyInGameStarUpView(ally = ally)
                }
            }
        }
    }
}


@Composable
fun DoAllyInGameStarUpView(ally: Ally) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceAround
    ) {
        val maxStar = ally.star >= ally.starLimit
        val buttonText =
            if (maxStar) GameApp.instance.getWrapString(R.string.star_max) else GameApp.instance.getWrapString(
                R.string.do_star_up
            )
        val enabled =
            !ally.peek && ally.starUpNum != 0 && ally.num >= ally.starUpNum && !maxStar
        GameButton(text = buttonText,
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                BattleManager.upgrade(ally)?.let {
                    Dialogs.allyDetailDialog.value = it
                    if (it.star == it.starLimit) {
                        Dialogs.allyInGameStarUpDialog.value = null
                    } else {
                        Dialogs.allyInGameStarUpDialog.value = it
                    }
                }
            })
    }
}