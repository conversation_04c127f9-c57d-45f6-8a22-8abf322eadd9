package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.toBuildingElementName
import com.moyu.chuanqirensheng.logic.toMagicElementName
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.dialog.PanelSheetDialog
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isProfession
import com.moyu.core.model.skill.isTalentAdv


@Composable
fun GameSkillDialog(show: MutableState<Boolean>) {
    show.value.takeIf { show.value }?.let {
        // todo role是master，也就是英雄，you是冒险技能的承载者
        val rawList = BattleManager.getGameSkills().filter { it.isMagic() || (it.isAdventure() && !it.isTalentAdv()) || it.isProfession() }
        val initList = BattleManager.getGameMasterRole().getSkills().filter {
            // 比如自带箭术，学习了中级箭术，这里箭术就不要显示了
            it.mainId !in rawList.map { it.mainId }
        }.map { it.copy(new = false) }
        val list = rawList + initList
        PanelSheetDialog(titles = listOf(
            stringResource(id = R.string.skill_sheet1),
            stringResource(id = R.string.skill_sheet2),
            stringResource(id = R.string.skill_sheet3),
            stringResource(id = R.string.skill_sheet4),
        ), reds = List(4) { index ->
            BattleManager.skillGameData.filter { it.isSkillSheet(index + 1) }.any { it.new }
        }, onDismissRequest = { show.value = false }) { index ->
            val filteredList = list.filter { it.isSkillSheet(index + 1) }
            // 这段代码是用来消红点的 todo
            when (index) {
                0 -> {
                    Box {
                        DisposableEffect(Unit) {
                            onDispose {
                                BattleManager.setSkillUnNew { it.isSkillSheet(1) }
                            }
                        }
                    }
                }

                1 -> {
                    Box {
                        DisposableEffect(Unit) {
                            onDispose {
                                BattleManager.setSkillUnNew { it.isSkillSheet(2) }
                            }
                        }
                    }
                }

                2 -> {
                    Box {
                        DisposableEffect(Unit) {
                            onDispose {
                                BattleManager.setSkillUnNew { it.isSkillSheet(3) }
                            }
                        }
                    }
                }

                else -> {
                    Box {
                        DisposableEffect(Unit) {
                            onDispose {
                                BattleManager.setSkillUnNew { it.isSkillSheet(4) }
                            }
                        }
                    }
                }
            }
            if (filteredList.isEmpty()) {
                Text(
                    modifier = Modifier.fillMaxSize(),
                    textAlign = TextAlign.Center,
                    text = stringResource(R.string.empty_dialog),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    filteredList.groupBy { it.elementType }.forEach {
                        val realList = it.value
                        val title = if (index == 0) {
                            // 魔法
                            it.key.toMagicElementName()
                        } else if (index == 2) {
                            // 建筑
                            it.key.toBuildingElementName()
                        } else {
                            ""
                        }
                        if (title.isNotEmpty()) {
                            TextLabel2(
                                labelSize = LabelSize.Medium,
                                text = title,
                                frame = R.drawable.shop_name_frame,
                                translateY = -padding2
                            )
                        }
                        FlowRow(
                            modifier = Modifier
                                .padding(horizontal = padding16),
                            overflow = FlowRowOverflow.Visible,
                            maxItemsInEachRow = 3,
                            horizontalArrangement = Arrangement.spacedBy(padding36)
                        ) {
                            realList.sortedBy { skill ->
                                BattleManager.you.value.getGraveSkills()
                                    .any {
                                        it.uuid == skill.uuid
                                    }
                            }.forEach { skill ->
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    Spacer(modifier = Modifier.size(padding4))
                                    val triggered = BattleManager.you.value.getGraveSkills()
                                        .any {
                                            it.uuid == skill.uuid
                                        }
                                    SingleSkillView(
                                        // 魔法为战斗技能，不显示多次生效等文案，专业也不显示
                                        skill = if (index in listOf(0, 1)) skill else skill.copy(
                                            extraInfo = BattleManager.getAdventureSkillTips(
                                                skill
                                            )
                                        ),
                                        showName = true,
                                        showRed = true,
                                        showStars = false,
                                        colorFilter = if (triggered && index !in listOf(
                                                0,
                                                1
                                            )
                                        ) ColorFilter.tint(
                                            B65, BlendMode.SrcAtop
                                        ) else null,
                                        itemSize = ItemSize.Large
                                    )
                                    Spacer(modifier = Modifier.size(padding4))
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
