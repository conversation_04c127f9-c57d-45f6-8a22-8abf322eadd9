package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.property.PropertyItem
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import java.lang.Double.max

@Composable
fun Property.MainPropertyLine(
    originProperty: Property = Property(),
    showBoost: Boolean = false,
    countStart: Int = 0,
    countEnd: Int = 100,
    textStyle: TextStyle = MaterialTheme.typography.h3,
    showZero: Boolean = true,
    showNegative: Boolean = false,
    textColor: Color = Color.Black,
    showIcon: Boolean = true,
) {
    val minValue = if (showNegative) -999999.0 else 0.0
    var count = 1
    if (count in countStart until countEnd) {
        if (showZero || attack.toDouble() - originProperty.attack.toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_1,
            getProperty = { max(minValue, attack.toDouble() - originProperty.attack.toDouble()) },
            name = stringResource(com.moyu.core.R.string.attack),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.attack_tips) },
            isBoost = { (attack.toDouble() - originProperty.attack) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses.first().toDouble() - originProperty.defenses.first().toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_2,
            getProperty = {
                max(
                    minValue,
                    defenses.first().toDouble() - originProperty.defenses.first().toDouble()
                )
            },
            name = DamageType.DamageType1.defenseName,
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.defense1_tips) },
            isBoost = { (defenses.first().toDouble() - originProperty.defenses.first()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses[1].toDouble() - originProperty.defenses[1].toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_3,
            getProperty = {
                max(
                    minValue,
                    defenses[1].toDouble() - originProperty.defenses[1].toDouble()
                )
            },
            name = DamageType.DamageType2.defenseName,
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.defense2_tips) },
            isBoost = { (defenses[1].toDouble() - originProperty.defenses[1]) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses[2].toDouble() - originProperty.defenses[2].toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_4,
            getProperty = {
                max(
                    minValue,
                    defenses[2].toDouble() - originProperty.defenses[2].toDouble()
                )
            },
            name = DamageType.DamageType3.defenseName,
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.defense3_tips) },
            isBoost = { (defenses[2].toDouble() - originProperty.defenses[2]) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses[3].toDouble() - originProperty.defenses[3].toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_5,
            getProperty = {
                max(
                    minValue,
                    defenses[3].toDouble() - originProperty.defenses[3].toDouble()
                )
            },
            name = DamageType.DamageType4.defenseName,
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.defense4_tips) },
            isBoost = { (defenses[3].toDouble() - originProperty.defenses[3]) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses[4].toDouble() - originProperty.defenses[4].toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_6,
            getProperty = {
                max(
                    minValue,
                    defenses[4].toDouble() - originProperty.defenses[4].toDouble()
                )
            },
            name = DamageType.DamageType5.defenseName,
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.defense5_tips) },
            isBoost = { (defenses[4].toDouble() - originProperty.defenses[4]) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || hp.toDouble() - originProperty.hp.toDouble() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_7,
            getProperty = { hp.toDouble() - originProperty.hp },
            name = stringResource(com.moyu.core.R.string.hp),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.hp_tips) },
            isBoost = { (hp.toDouble() - originProperty.hp) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalRate() - originProperty.getRealFatalRate() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_8,
            getProperty = { max(minValue, getRealFatalRate() - originProperty.getRealFatalRate()) },
            name = stringResource(com.moyu.core.R.string.fatal_rate),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.fatal_rate_tips) },
            showPercent = true,
            isBoost = { (getRealFatalRate() - originProperty.getRealFatalRate()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalDamage() - originProperty.getRealFatalDamage() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_9,
            getProperty = { max(minValue, getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            name = stringResource(com.moyu.core.R.string.fatal_damage),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.fatal_damage_tips) },
            showPercent = true,
            isBoost = { (getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealDodgeRate() - originProperty.getRealDodgeRate() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_10,
            getProperty = { max(minValue, getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            name = stringResource(com.moyu.core.R.string.dodge),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.dodge_tips) },
            showPercent = true,
            isBoost = { (getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || speed.toDouble() - originProperty.speed != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_11,
            getProperty = { max(minValue, speed.toDouble() - originProperty.speed) },
            name = stringResource(com.moyu.core.R.string.speed),
            getTips = { GameApp.instance.getWrapString(com.moyu.core.R.string.speed_tips) },
            isBoost = { (speed.toDouble() - originProperty.speed) },
            showBoost = showBoost,
            textStyle = textStyle, textColor = textColor, showIcon = showIcon
        )
    }
}