package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding212
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding500
import com.moyu.chuanqirensheng.ui.theme.padding8


enum class PanelSize(val width: Dp, val height: Dp, val background: Int) {
    Small(padding380, padding165, R.drawable.common_window3),
    SmallPlus(padding400, padding212, R.drawable.common_window3),
    Normal(padding400, padding500, R.drawable.common_window2),
}

@Composable
fun PanelLayout(
    modifier: Modifier = Modifier,
    size: PanelSize,
    showClose: Boolean = false,
    onClose: () -> Unit = {},
    content: @Composable (BoxScope.() -> Unit),
) {
    EffectButton(
        modifier = modifier.size(size.width, size.height)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = size.background),
            contentDescription = null
        )
        Box(
            modifier = Modifier.fillMaxSize().padding(
                horizontal = size.width / 18,
                vertical = size.height / 18
            )
        ) {
            content()
        }
        if (showClose) {
            CloseButton(
                Modifier
                    .align(Alignment.TopEnd)
                    .graphicsLayer {
                        translationX = padding8.toPx()
                        translationY = -padding8.toPx()
                    }) {
                onClose()
            }
        }
    }
}

@Composable
fun CloseButton(modifier: Modifier, onClick: () -> Unit) {
    EffectButton(modifier = modifier
        .semantics {
            contentDescription = GameApp.instance.getWrapString(R.string.close)
        }
        .size(imageLarge),
        onClick = {
            onClick()
        }) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .scale(1.2f),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_exit),
            contentDescription = stringResource(R.string.quit_page)
        )
    }
}
