package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Ally
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun GameAllyDialog(show: MutableState<Boolean>) {
    show.value.takeIf { show.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setAllyUnNew()
            }
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(allyOrderList.first())
        }
        val rawList = BattleManager.getGameAlliesNoMaster().filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }
        val list = rawList.filter { it.battlePosition >= 0 }
            .sortedByDescending { order.value.order?.invoke(it) } + rawList.filter { it.battlePosition < 0 }
            .sortedByDescending { order.value.order?.invoke(it) }
        PanelDialog(onDismissRequest = { show.value = false }, contentBelow = {
            GameButton(
                Modifier.padding(bottom = padding6),
                buttonSize = ButtonSize.Big,
                text = stringResource(R.string.star_up_all)
            ) {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BattleManager.starUpAll()
                }
            }
        }) {
            Box {
                LazyVerticalGrid(modifier = Modifier
                    .fillMaxSize(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(padding4))
                                val ally = list[index]
                                Box {
                                    SingleAllyView(
                                        ally = ally,
                                        showName = true,
                                        showHp = true,
                                        showRed = true,
                                        itemSize = ItemSize.Large,
                                        extraInfo = if (ally.isDead()) stringResource(R.string.died) else ""
                                    )
                                    if (ally.battlePosition >= 0) {
                                        Image(
                                            painter = painterResource(id = R.drawable.common_choose),
                                            modifier = Modifier
                                                .size(imageSmallPlus)
                                                .align(Alignment.BottomEnd),
                                            contentDescription = null
                                        )
                                    }
                                }
                                Spacer(modifier = Modifier.size(padding4))
                            }
                        }
                    })
                Row(
                    Modifier
                        .align(Alignment.BottomEnd)
                        .fillMaxWidth()
                        .height(padding28)
                        .graphicsLayer {
                            translationX = padding26.toPx()
                            translationY = padding44.toPx()
                        },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CommonOrderView(
                        Modifier.padding(start = padding10), showOrder
                    )
                    CommonFilterView(
                        Modifier.padding(end = padding10), showFilter
                    )
                }
            }
            OrderLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding6, end = padding10),
                show = showOrder,
                filter = order,
                filterList = allyOrderList
            )
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding6, end = padding10),
                show = showFilter,
                filter = filter,
                filterList = allyFilterList
            )
        }
    }
}