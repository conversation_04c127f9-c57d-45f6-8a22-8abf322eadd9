package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuidePointAndText
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isSelectGroup
import com.moyu.chuanqirensheng.logic.getGroupType
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.awardGif
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.core.GameCore
import com.moyu.core.model.Event
import com.moyu.core.model.eventGainSkillPools
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventPassDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val handler = EventManager.getOrCreateHandler(event)
        val award = remember {
            handler.getEventAward(event).copy(showQuestion = false)
        }
        LaunchedEffect(Unit) {
            GameCore.instance.onBattleEffect(SoundEffect.EventWin)
        }
        PanelDialog(onDismissRequest = {
            if (show.value != null) {
                show.value = null
                if (GuideManager.guideIndex.intValue in listOf(3, 6)) {
                    GuideManager.guideIndex.intValue += 1
                    GuideManager.showGuide.value = true
                }
                GameApp.globalScope.launch(Dispatchers.Main) {
                    AwardManager.gainAward(award)
                    EventManager.gotoNextEvent(event, true)
                }
            }
        }, contentBelow = {
            GameButton(text = stringResource(id = R.string.see_you_again),
                buttonStyle = ButtonStyle.Orange,
                buttonSize = ButtonSize.Medium,
                onClick = {
                    if (show.value != null) {
                        show.value = null
                        if (GuideManager.guideIndex.intValue in listOf(3, 6)) {
                            GuideManager.guideIndex.intValue += 1
                            GuideManager.showGuide.value = true
                        }
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainAward(award)
                            EventManager.gotoNextEvent(event, true)
                        }
                    }
                })
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.year_end, event.name),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = event.winText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                if (!award.isEmpty()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.next_turn_award),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.CenterHorizontally),
                        award = award,
                        param = defaultParam.copy(textColor = Color.Black),
                        paddingHorizontalInDp = padding0,
                        mainAxisAlignment = spacedBy(padding45),
                    )
                }
                if (event.winReward.first() in eventGainSkillPools) {
                    // TODO 这个是魔法行会，如果没有奖励随机技能，提示
                    // 写的很粗暴，如果小于2个奖励技能，就是要提示
                    if (award.skills.size < 2) {
                        Text(
                            text = stringResource(R.string.skill_not_able_to_learn3),
                            style = MaterialTheme.typography.h2,
                            color = Color.Black
                        )
                    }
                }
                if (event.isSelectGroup()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.group_allies),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        // todo 9901..9909是要显示的poolId
                        val poolId = event.getGroupType() + 9900
                        AwardList(
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.CenterHorizontally),
                            award = repo.gameCore.getPoolById(poolId).toAward(),
                            param = defaultParam.copy(textColor = Color.Black),
                            paddingHorizontalInDp = padding0,
                            mainAxisAlignment = spacedBy(padding2),
                        )
                    }
                }
            }
            if (GuideManager.guideIndex.intValue == 3) {
                Box(Modifier.align(Alignment.BottomCenter).graphicsLayer {
                    translationY = padding300.toPx()
                }) {
                    GuidePointAndText(
                        text = stringResource(R.string.guide4),
                        handType = HandType.NO_HAND
                    )
                }
            } else if (GuideManager.guideIndex.intValue == 6) {
                Box(Modifier.align(Alignment.BottomCenter).graphicsLayer {
                    translationY = padding300.toPx()
                }) {
                    GuidePointAndText(
                        text = stringResource(R.string.guide7),
                        handType = HandType.NO_HAND
                    )
                }
            }
            ForeverGif(
                Modifier
                    .width(dialogWidth), awardGif.gif, awardGif.count, needGap = true
            )
        }
    }
}


