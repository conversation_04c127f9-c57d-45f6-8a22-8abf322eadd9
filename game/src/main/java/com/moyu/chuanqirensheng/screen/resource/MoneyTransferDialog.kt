package com.moyu.chuanqirensheng.screen.resource

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemoryF
import com.moyu.chuanqirensheng.ui.theme.SliderColor
import com.moyu.chuanqirensheng.ui.theme.SliderTrackColor
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.chuanqirensheng.ui.theme.slideWidth


@Composable
fun MoneyTransferDialog(switch: MutableState<Boolean>) {
    switch.value.takeIf { it }?.let {
        val key = remember {
            GuardedMemoryF()
        }
        LaunchedEffect(Unit) {
            key.value = 0f
        }
        val rate = repo.gameCore.getKeyToDiamondRate()
        PanelDialog(onDismissRequest = { switch.value = false }, contentBelow = {
            GameButton(text = stringResource(id = R.string.exchange),
                enabled = key.value.toInt() > 0,
                buttonStyle = ButtonStyle.Orange,
                onClick = {
                    if (key.value.toInt() <= 0) {
                        GameApp.instance.getWrapString(R.string.select_exchange_num_tips).toast()
                    } else {
                        if (AwardManager.key.value >= key.value) {
                            AwardManager.gainKey(-key.value.toInt())
                            AwardManager.gainDiamond(key.value.toInt() * rate)
                            (GameApp.instance.getWrapString(R.string.exchanged) + (key.value.toInt() * rate) + GameApp.instance.getWrapString(
                                R.string.diamond_title
                            )).toast()
                            key.value = 0f
                        } else {
                            GiftManager.onKeyNotEnough()
                            GameApp.instance.getWrapString(R.string.key_not_enough).toast()
                        }
                    }
                })
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    CurrentKeyPoint(
                        showFrame = true, showPlus = true
                    )
                    CurrentDiamondPoint(
                        showFrame = true
                    )
                }
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(R.drawable.common_key),
                            contentDescription = stringResource(R.string.key_title)
                        )
                        Text(
                            text = key.value.toInt()
                                .toString() + stringResource(R.string.key_title),
                            color = Color.Black,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    Spacer(modifier = Modifier.size(padding48))
                    Image(
                        modifier = Modifier.size(imageLarge),
                        painter = painterResource(R.drawable.common_arrow_right),
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.size(padding48))
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Image(
                            modifier = Modifier.size(imageHugeLite),
                            painter = painterResource(R.drawable.common_medal),
                            contentDescription = stringResource(R.string.diamond_title)
                        )
                        Text(
                            text = (key.value.toInt() * rate).toString() + stringResource(R.string.diamond_title),
                            color = Color.Black,
                            style = MaterialTheme.typography.h3
                        )
                    }
                }
                Row {
                    Slider(
                        modifier = Modifier.size(slideWidth, slideHeight),
                        value = key.value, onValueChange = { key.value = it.toInt().toFloat() },
                        valueRange = 0f..AwardManager.key.value.toFloat(),
                        colors = SliderDefaults.colors(
                            thumbColor = SliderColor,
                            disabledThumbColor = SliderColor,
                            activeTrackColor = SliderTrackColor,
                            inactiveTrackColor = SliderTrackColor,
                        ),
                    )
                }
            }
        }
    }
}