package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.model.skill.Skill

@Composable
fun SkillLevelInfoDialog(result: MutableState<Skill?>) {
    result.value?.let { skill ->
        val skills = repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
        val currentLevel = skill.level
        PanelDialog(onDismissRequest = { result.value = null }) {
            Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = stringResource(R.string.special_skill_info),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Column(
                    Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    skills.forEachIndexed { index, skill ->
                        if (index != 0) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .size(imageMedium),
                                painter = painterResource(id = R.drawable.common_arrow_down),
                                contentDescription = null
                            )
                        }
                        Text(
                            text = buildAnnotatedString { append("Lv.${skill.level}:") } + skill.getRealDescColorful(
                                MaterialTheme.typography.h3.toSpanStyle()
                            ),
                            style = MaterialTheme.typography.h3,
                            color = if (skill.level == currentLevel) Color.Black else Color.DarkGray
                        )
                    }
                }
            }
        }
    }
}