package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.editUserName
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_AVATAR
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_USER_NAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_USER_NAME_CHANGED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun EditUserInfoDialog(show: MutableState<Boolean>) {
    if (show.value) {
        val scope = rememberCoroutineScope()
        var userName by remember { mutableStateOf("") }
        var selectedAvatar by remember { mutableStateOf("") }
        var userNameChanged by remember { mutableStateOf(false) }
        var isEditingUserName by remember { mutableStateOf(false) }

        // Load saved custom data or use default
        LaunchedEffect(Unit) {
            userNameChanged = getBooleanFlowByKey(KEY_USER_NAME_CHANGED) && !DebugManager.unlockAll
            val customUserName = getStringFlowByKey(KEY_CUSTOM_USER_NAME)
            val customAvatar = getStringFlowByKey(KEY_CUSTOM_AVATAR)

            userName = if (customUserName.isNotEmpty()) {
                customUserName
            } else {
                GameApp.instance.getUserName() ?: ""
            }

            selectedAvatar = if (customAvatar.isNotEmpty()) {
                customAvatar
            } else {
                GameApp.instance.getAvatarUrl() ?: ""
            }
        }
        
        // Get all ally race pics for avatar selection
        val avatarOptions = remember {
            val currentAvatar = GameApp.instance.getAvatarUrl(true)
            val allyRacePics = repo.allyManager.data.map { ally ->
                ally.getRace().pic
            }.distinct()
            
            // Put current avatar as first option, then add all ally race pics
            listOf(currentAvatar) + allyRacePics.filter { it != currentAvatar }
        }
        
        PanelDialog(
            onDismissRequest = { show.value = false },
            contentBelow = {
                // Save button
                GameButton(text = stringResource(R.string.edit)) {
                    scope.launch {
                        var success = true

                        val originalUserName = GameApp.instance.getUserName() ?: ""
                        val originalAvatar = GameApp.instance.getAvatarUrl() ?: ""

                        // Handle username change
                        if (userName != originalUserName && !userNameChanged) {
                            isEditingUserName = true
                            try {
                                // Only call API if has_google_service=false
                                success = if (!GameApp.instance.activity.resources.getBoolean(R.bool.has_google_service)) {
                                    withContext(Dispatchers.IO) {
                                        editUserName(userName)
                                    }
                                } else {
                                    true // Always succeed for Google service builds
                                }

                                if (success) {
                                    // Save custom username to datastore for persistence
                                    setStringValueByKey(KEY_CUSTOM_USER_NAME, userName)
                                    setBooleanValueByKey(KEY_USER_NAME_CHANGED, true)

                                    // Update GameSdkDefaultProcessor userName temporarily
                                    GameApp.instance.dealAfterLogin(
                                        userName,
                                        GameApp.instance.getObjectId() ?: "",
                                        selectedAvatar,
                                        GameApp.instance.activity
                                    )
                                    GameApp.instance.getWrapString(R.string.save_success).toast()
                                } else {
                                    GameApp.instance.getWrapString(R.string.save_failed).toast()
                                }
                            } catch (e: Exception) {
                                GameApp.instance.getWrapString(R.string.network_error).toast()
                                success = false
                            }
                            isEditingUserName = false
                        }

                        // Handle avatar change (always succeeds)
                        if (selectedAvatar != originalAvatar) {
                            // Save custom avatar to datastore for persistence
                            setStringValueByKey(KEY_CUSTOM_AVATAR, selectedAvatar ?: "")

                            // Update GameSdkDefaultProcessor avatar temporarily
                            GameApp.instance.dealAfterLogin(
                                userName,
                                GameApp.instance.getObjectId() ?: "",
                                selectedAvatar ?: "",
                                GameApp.instance.activity
                            )
                        }

                        if (success) {
                            show.value = false
                        }
                    }
                }
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(padding16),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.edit_user_info),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(padding16))
                
                // Username editing section
                Text(
                    text = stringResource(R.string.user_name),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(padding6))
                
                if (userNameChanged) {
                    Text(
                        text = stringResource(R.string.user_name_already_changed),
                        style = MaterialTheme.typography.h5,
                        color = Color.DarkGray
                    )
                    Spacer(Modifier.size(padding2))
                    Text(
                        text = userName,
                        style = MaterialTheme.typography.h4,
                        color = Color.Black
                    )
                } else {
                    DecorateTextField(
                        modifier = Modifier.fillMaxWidth().height(textFieldHeight),
                        text = userName,
                        hintText = stringResource(R.string.enter_user_name)
                    ) { newName ->
                        userName = newName.take(15)
                    }
                }
                
                Spacer(modifier = Modifier.height(padding16))
                
                // Avatar selection section
                Text(
                    text = stringResource(R.string.select_avatar),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(padding10))
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(4),
                    horizontalArrangement = Arrangement.spacedBy(padding4),
                    verticalArrangement = Arrangement.spacedBy(padding4),
                    modifier = Modifier.height(200.dp)
                ) {
                    items(avatarOptions) { avatarPic ->
                        EffectButton(
                            onClick = { selectedAvatar = avatarPic ?: "" }
                        ) {
                            Box(
                                modifier = Modifier.size(ItemSize.LargePlus.itemSize),
                                contentAlignment = Alignment.Center
                            ) {
                                // Selection frame
                                Image(
                                    modifier = Modifier.fillMaxSize(),
                                    painter = painterResource(id = R.drawable.common_frame),
                                    contentScale = ContentScale.FillBounds,
                                    contentDescription = null
                                )

                                // Selection indicator
                                if (selectedAvatar == avatarPic) {
                                    Image(
                                        modifier = Modifier
                                            .size(ItemSize.LargePlus.itemSize * 0.3f)
                                            .align(Alignment.BottomEnd),
                                        painter = painterResource(id = R.drawable.common_choose),
                                        contentDescription = null
                                    )
                                }
                                
                                // Avatar image
                                Image(
                                    modifier = Modifier
                                        .size(ItemSize.LargePlus.itemSize * 0.8f)
                                        .padding(padding4),
                                    painter = if (avatarPic?.startsWith("http") == true) rememberAsyncImagePainter(avatarPic) else painterResource(
                                        id = getImageResourceDrawable(avatarPic?: "")
                                    ),
                                    contentDescription = null
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
