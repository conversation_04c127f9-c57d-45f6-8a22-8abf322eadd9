package com.moyu.chuanqirensheng.screen.about

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context.CLIPBOARD_SERVICE
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.SkillLevel3Color
import com.moyu.chuanqirensheng.ui.theme.SkillStoryColor
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.core.discordLink

@Composable
fun AboutScreen() {
    GameBackground(title = stringResource(id = R.string.about)) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding14)
        ) {
            Text(text = stringResource(R.string.produce), style = MaterialTheme.typography.h3)
            Row(modifier = Modifier.clickable {
                val myClipboard =
                    GameApp.instance.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                val myClip = ClipData.newPlainText("text", discordLink)
                myClipboard.setPrimaryClip(myClip)
                ("Discord Link：$discordLink " + GameApp.instance.getWrapString(R.string.copied)).toast()
            }) {
                Text(
                    text = stringResource(R.string.contact1) + "(Discord)：",
                    style = MaterialTheme.typography.h3
                )
                Text(
                    text = discordLink,
                    style = MaterialTheme.typography.h3,
                    textDecoration = TextDecoration.Underline,
                    color = RaceNameColor
                )
            }
            Row(modifier = Modifier.clickable {
                val myClipboard =
                    GameApp.instance.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                val myClip = ClipData.newPlainText("text", "<EMAIL>")
                myClipboard.setPrimaryClip(myClip)
                ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
            }) {
                Text(
                    text = stringResource(R.string.contact1) + "(email)：",
                    style = MaterialTheme.typography.h3
                )
                Text(
                    text = "<EMAIL>",
                    style = MaterialTheme.typography.h3,
                    textDecoration = TextDecoration.Underline,
                    color = RaceNameColor
                )
            }
            Spacer(modifier = Modifier.size(padding14))
            Text(
                text = stringResource(R.string.about_us).trimIndent(),
                style = MaterialTheme.typography.h4
            )
            Spacer(modifier = Modifier.size(padding14))
            Text(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = SkillStoryColor,
                text = stringResource(id = R.string.about) + "《" + stringResource(id = R.string.app_name) + "》",
                style = MaterialTheme.typography.h3
            )
            Text(
                color = SkillLevel3Color,
                text = stringResource(R.string.about_game).trimIndent(), style = MaterialTheme.typography.h4
            )
        }
    }
}