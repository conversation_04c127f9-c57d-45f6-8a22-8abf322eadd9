package com.moyu.chuanqirensheng.screen.property

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.screen.equip.RolePropertyLine
import com.moyu.core.model.property.AdventureProps


@Composable
fun MasterPropertyViewInGame(modifier: Modifier = Modifier, property: AdventureProps) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        property.RolePropertyLine(
                countStart = 1,
                countEnd = 6
            )
    }
}