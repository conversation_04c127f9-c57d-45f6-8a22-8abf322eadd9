package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton

val EMPTY_DISMISS = {}
@Composable
fun CloseHintDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    content: @Composable BoxScope.() -> Unit
) {
    Dialog(onDismissRequest = {
        onDismissRequest()
    }, properties = DialogProperties(usePlatformDefaultWidth = false)) {
        (LocalView.current.parent as DialogWindowProvider).window.setDimAmount(0.8f)
        Column {
            Box(content = content)
            if (onDismissRequest != EMPTY_DISMISS) {
                EffectButton(modifier = Modifier.fillMaxWidth(), onClick = { onDismissRequest() }) {
                    Text(text = stringResource(R.string.quit_tips), style = MaterialTheme.typography.h4)
                }
            }
        }
    }
}