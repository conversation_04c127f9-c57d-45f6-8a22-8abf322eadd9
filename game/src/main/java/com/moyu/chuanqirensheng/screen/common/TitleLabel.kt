package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.padding4

@Composable
fun TitleLabel(modifier: Modifier, text: String, color: Color = Color.White, frame: Int = R.drawable.resource_frame, onClick: () -> Unit = {}) {
    EffectButton(
        modifier = modifier,
        onClick = onClick
    ) {
        Image(
            modifier = Modifier.fillMaxSize().padding(horizontal = padding4),
            painter = painterResource(id = frame),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
        Text(
            modifier = Modifier.graphicsLayer {
                translationY = -padding4.toPx()
            },
            text = text,
            style = MaterialTheme.typography.h3,
            color = color,
            textAlign = TextAlign.Center
        )
    }
}