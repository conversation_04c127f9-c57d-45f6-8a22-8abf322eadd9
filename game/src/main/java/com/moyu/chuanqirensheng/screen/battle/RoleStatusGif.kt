package com.moyu.chuanqirensheng.screen.battle

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.ui.theme.roleEffectHeight
import com.moyu.chuanqirensheng.ui.theme.roleEffectWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.role.Role

val deathEffect = Pair("dead_", 8)
val environmentEffect = Pair("area_", 23)

@Composable
fun RoleStatusBeingAttackGif(roleGetter: () -> Role) {
    val role = roleGetter()
    val effectPair = when {
        role.hasState(ActionStateType.BeingAttack) -> {
            role.getState(ActionStateType.BeingAttack)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }

        role.hasState(ActionStateType.BeingHeal) -> {
            role.getState(ActionStateType.BeingHeal)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }

        role.hasState(ActionStateType.BeingBuff) -> {
            role.getState(ActionStateType.BeingBuff)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }

        role.hasState(ActionStateType.BeingDispel) -> {
            role.getState(ActionStateType.BeingAttack)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }
        role.hasState(ActionStateType.Death) -> {
            deathEffect
        }
        role.hasState(ActionStateType.Environment) -> {
            environmentEffect
        }
        role.hasState(ActionStateType.Summoning) -> {
            role.getState(ActionStateType.Summoning)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }
        role.hasState(ActionStateType.DoSkill) -> {
            role.getState(ActionStateType.DoSkill)?.let { state ->
                Pair(state.effect, state.effectNum)
            }
        }
        else -> null
    }
    val isAttack = role.hasState(ActionStateType.DoAttack)
    val effectIndex by animateIntAsState(
        targetValue = (effectPair?.second) ?: 1,
        animationSpec = TweenSpec(
            durationMillis = if (effectPair == null) 0 else GameSpeedManager.animDuration().toInt(),
            easing = FastOutLinearInEasing
        ), label = ""
    )
    effectPair?.takeIf { it.first != "0" }?.let {
        // 直接根据伤害类型播放
        Image(
            modifier = Modifier
                .size(roleEffectWidth, roleEffectHeight),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${it.first}${if (isAttack) 1 else effectIndex}"
                )
            ),
            contentDescription = null
        )
    }
}