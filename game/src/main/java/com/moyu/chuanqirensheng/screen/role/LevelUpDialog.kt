package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.detail.EventAward3To1Layout
import com.moyu.chuanqirensheng.logic.getAwardDesc
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.screen.dialog.EMPTY_DISMISS
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.ui.theme.dialogHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun LevelUpDialog(show: MutableState<Boolean>) {
    if (show.value) {
        val awards = remember {
            mutableStateListOf(Award())
        }
        val finished = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            awards.clear()
            awards.addAll(BattleManager.getTwoLevelUpAwards())
            // 这里是升一级，如果一次升级多个等级，会在LevelResultDialog里发现并且处理
            BattleManager.yourTitle.value = repo.gameCore.getTitlePool()
                .first { it.level == BattleManager.yourTitle.value.level + 1 }
        }
        EmptyDialog(onDismissRequest = EMPTY_DISMISS) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dialogHeight)
                    .paint(
                        painterResource(id = R.drawable.common_window2),
                        contentScale = ContentScale.FillBounds
                    )
                    .scale(0.9f),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(padding10))
                TextLabel(
                    labelSize = LabelSize.Huge,
                    text = stringResource(R.string.title_level_up),
                    contentAlignment = Alignment.Center
                )
                Spacer(modifier = Modifier.height(padding26))
                if (awards.isNotEmpty() && awards.any { !it.isEmpty() }) {
                    EventAward3To1Layout(
                        awards,
                        buttonTexts = listOf(
                            stringResource(R.string.do_select),
                            stringResource(R.string.do_select),
                            stringResource(R.string.do_select)
                        ),
                        content = { index ->
                            Text(
                                modifier = Modifier.verticalScroll(rememberScrollState()),
                                text = awards[index].getAwardDesc(),
                                style = MaterialTheme.typography.h3
                            )
                        },
                        maxLine = 1,
                        textColor = Color.Black
                    ) {
                        if (!finished.value) {
                            finished.value = true
                            show.value = false
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                AwardManager.gainAward(awards[it])
                                Dialogs.levelResultDialog.value = awards[it]
                            }
                        }
                    }
                } else {
                    Spacer(modifier = Modifier.height(padding26))
                    Text(
                        text = stringResource(R.string.level_up_no_more_skill),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    GameButton(text = stringResource(id = R.string.quit)) {
                        finished.value = true
                        show.value = false
                    }
                    Spacer(modifier = Modifier.height(padding26))
                }
            }
        }
    }
}


