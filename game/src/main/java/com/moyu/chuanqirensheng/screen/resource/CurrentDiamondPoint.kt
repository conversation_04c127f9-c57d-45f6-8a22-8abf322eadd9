package com.moyu.chuanqirensheng.screen.resource

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.zIndex
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SELL_KEY_INDEX
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.indexToResourceIcon
import com.moyu.chuanqirensheng.logic.indexToResourceName
import com.moyu.chuanqirensheng.logic.indexToResourceTips
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.cheatBarHeight
import com.moyu.chuanqirensheng.ui.theme.cheatBarWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlusFrame
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyShortHeight
import com.moyu.chuanqirensheng.ui.theme.moneyShortWidth
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.addAnimationVertical
import com.moyu.core.aiFaDian
import com.moyu.core.util.shrinkNumber


@Composable
fun CurrentPassPoint(modifier: Modifier = Modifier) {
    val money = BattlePassManager.getCurrentLevelWarPass()
    val level = BattlePassManager.getCurrentWarPass()?.level ?: 0
    val season = BattlePassManager.getPassSeason()
    val nextLevelValue =
        repo.gameCore.getBattlePassPool().filter { it.level == level + 1 && it.season == season }
            .firstOrNull()?.exp ?: 1000
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = padding10.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(
                cheatLevel = level,
            )
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money,
            maxValue = nextLevelValue,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        Spacer(modifier = Modifier.size(padding4))
        InfoIcon {
            GameApp.instance.getWrapString(R.string.battle_pass_content).toast()
        }
    }
}

@Composable
fun CurrentPass2Point(modifier: Modifier = Modifier) {
    val money = BattlePass2Manager.getCurrentLevelWarPass()
    val level = BattlePass2Manager.getCurrentWarPass()?.level ?: 0
    val season = BattlePass2Manager.getPassSeason()
    val nextLevelValue =
        repo.gameCore.getBattlePass2Pool().filter { it.level == level + 1 && it.season == season }
            .firstOrNull()?.exp ?: 1000
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = padding10.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(
                cheatLevel = level,
            )
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money,
            maxValue = nextLevelValue,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        Spacer(modifier = Modifier.size(padding4))
        InfoIcon {
            GameApp.instance.getWrapString(R.string.battle_pass2_content).toast()
        }
    }
}


@Composable
fun InfoIcon(callback: () -> Unit) {
    EffectButton(onClick = callback) {
        Image(
            modifier = Modifier.size(imageLarge),
            painter = painterResource(id = R.drawable.battle_menu_info),
            contentDescription = stringResource(R.string.explain)
        )
    }
}


@Composable
fun VipLevel(
    modifier: Modifier = Modifier,
    cheatLevel: Int,
    frame: Painter = painterResource(id = R.drawable.level_frame)
) {
    Box(modifier = modifier.size(imageLarge), contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.size(imageLarge),
            painter = frame,
            contentDescription = null
        )
        Text(text = "$cheatLevel", style = MaterialTheme.typography.h3)
    }
}


@Composable
fun CurrentElectricPoint(
    modifier: Modifier = Modifier
) {
    val money = AwardManager.electric.value
    val level = VipManager.getVipLevelData()
    val nextLevelValue = if (money == 0) repo.gameCore.getVipPool().first() else
        repo.gameCore.getVipPool().firstOrNull { it.level == level.level + 1 }
            ?: repo.gameCore.getVipPool().last()
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = padding10.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(cheatLevel = if (money == 0) 0 else level.level)
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money - (nextLevelValue.num - nextLevelValue.currentNum),
            maxValue = nextLevelValue.currentNum,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        InfoIcon {
            if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                gotoSellWithTabIndex(SELL_KEY_INDEX)
                Dialogs.moneyTransferDialog.value = false
                GameApp.instance.getWrapString(R.string.electric_content).toast()
            } else {
                GameApp.instance.getWrapString(R.string.electric_content).toast()
            }
        }
    }
}

@Composable
fun CurrentHistoryCouponPoint(
    modifier: Modifier = Modifier
) {
    val money = AwardManager.couponHistory.value
    val level = DrawAwardManager.getDrawLevel()
    val nextLevelValue = (level + 1) * DrawAwardManager.perDraw
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .then(modifier)
    ) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = padding10.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(cheatLevel = if (money == 0) 0 else level)
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money,
            maxValue = nextLevelValue,
            textColor = Color.White,
            style = MaterialTheme.typography.h6,
            smallPadding = true
        )
        InfoIcon {
            GameApp.instance.getWrapString(R.string.coupon_content).toast()
        }
    }
}

@Composable
fun CurrentDiamondPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.diamond.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.diamond_title)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.common_medal),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString().shrinkNumber(!GameApp.instance.resources.getBoolean(R.bool.has_google_service)),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
        if (showPlus) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                Dialogs.moneyTransferDialog.value = true
            }) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(R.drawable.extra_buy),
                    contentDescription = stringResource(id = R.string.gain_diamond)
                )
            }
        }
    }
}

@Composable
fun DiamondPoint(cost: Int, color: Color = Color.Black) {
    val money = AwardManager.diamond.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.common_medal),
            contentDescription = stringResource(id = R.string.diamond_title)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough) color else DARK_RED
        )
    }
}

@Composable
fun AllyCouponPoint(cost: Int = 10, color: Color = Color.White) {
    val money = AwardManager.couponAlly.value
    val enough = money >= cost
    val lack = (cost - money) * repo.gameCore.getAllyCouponRate()
    val keyEnough = AwardManager.key.value >= lack
    val realCost = if (enough) cost else if (keyEnough) money else cost
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.paint(painterResource(id = R.drawable.common_line2))
    ) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.coupon_ally_icon),
            contentDescription = stringResource(id = R.string.ally_coupon)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = realCost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough || keyEnough) color else DARK_RED
        )
        if (!enough) {
            if (keyEnough) {
                Image(
                    modifier = Modifier.size(imageSmallPlusFrame),
                    painter = painterResource(id = R.drawable.common_key),
                    contentDescription = stringResource(id = R.string.key_title)
                )
                Spacer(modifier = Modifier.size(padding4))
                Text(
                    text = lack.toString(),
                    style = MaterialTheme.typography.h2,
                    color = color,
                    maxLines = 1,
                    softWrap = false,
                    overflow = TextOverflow.Visible
                )
            }
        }
    }
}

@Composable
fun HeroCouponPoint(cost: Int = 10, color: Color = Color.White) {
    val money = AwardManager.couponHero.value
    val enough = money >= cost
    val lack = (cost - money) * repo.gameCore.getHeroCouponRate()
    val keyEnough = AwardManager.key.value >= lack
    val realCost = if (enough) cost else if (keyEnough) money else cost
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.paint(painterResource(id = R.drawable.common_line2))
    ) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.hero_coupon_icon),
            contentDescription = stringResource(id = R.string.hero_coupon)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = realCost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough || keyEnough) color else DARK_RED
        )
        if (!enough) {
            if (keyEnough) {
                Image(
                    modifier = Modifier.size(imageSmallPlusFrame),
                    painter = painterResource(id = R.drawable.common_key),
                    contentDescription = stringResource(id = R.string.key_title)
                )
                Spacer(modifier = Modifier.size(padding4))
                Text(
                    text = lack.toString(),
                    style = MaterialTheme.typography.h2,
                    color = color,
                    maxLines = 1,
                    softWrap = false,
                    overflow = TextOverflow.Visible
                )
            }
        }
    }
}

@Composable
fun PvpPoint(cost: Int, color: Color = Color.White) {
    val money = AwardManager.pvpDiamond.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.pvp_diamond_icon),
            contentDescription = stringResource(id = R.string.pvp_diamond)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough) color else DARK_RED
        )
    }
}


@Composable
fun CurrentPvpPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.pvpDiamond.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.pvp_diamond)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.pvp_diamond_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}


@Composable
fun CurrentPvpScore(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = PvpManager.pvpScore.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.pvp_score)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.pvp_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}


@Composable
fun CurrentPvp2Score(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = Pvp2Manager.pvpScore.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.pvp2_score)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.pvp_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}

@Composable
fun CurrentKeyPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.key.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.key_title)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.common_key),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString().shrinkNumber(!GameApp.instance.resources.getBoolean(R.bool.has_google_service)),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
        if (showPlus) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                gotoSellWithTabIndex(SELL_KEY_INDEX)
                Dialogs.moneyTransferDialog.value = false
            }) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(R.drawable.extra_buy),
                    contentDescription = stringResource(id = R.string.get_key)
                )
            }
        }
    }
}


@Composable
fun CurrentAllyCouponPoint(
    modifier: Modifier = Modifier, showFrame: Boolean = false
) {
    val money = AwardManager.couponAlly.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.ally_coupon)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.coupon_ally_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    softWrap = false,
                    overflow = TextOverflow.Visible
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}


@Composable
fun CurrentHeroCouponPoint(
    modifier: Modifier = Modifier, showFrame: Boolean = false
) {
    val money = AwardManager.couponHero.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.hero_coupon)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.hero_coupon_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    softWrap = false,
                    overflow = TextOverflow.Visible
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}

@Composable
fun KeyPoint(cost: Int, forceEnough: Boolean = false) {
    val money = AwardManager.key.value
    val enough = money >= cost || forceEnough
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.common_key),
            contentDescription = stringResource(id = R.string.key_title)
        )
        Spacer(modifier = Modifier.size(padding3))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough) Color.White else DARK_RED
        )
    }
}

@Composable
fun ResourcesPoint(index: Int, cost: Int) {
    val money = BattleManager.resources[index]
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = index.indexToResourceIcon()),
            contentDescription = index.indexToResourceName()
        )
        Spacer(modifier = Modifier.size(padding3))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough) Color.White else DARK_RED
        )
    }
}

@Composable
fun CurrentResourcesPoint(modifier: Modifier = Modifier) {
    Column(modifier = modifier) {
        Row(horizontalArrangement = Arrangement.spacedBy(padding2)) {
            CurrentResourcesPoint(modifier = Modifier, index = 0)
            CurrentResourcesPoint(modifier = Modifier, index = 1)
            CurrentResourcesPoint(modifier = Modifier, index = 2)
        }
        Row(horizontalArrangement = Arrangement.spacedBy(padding2)) {
            CurrentResourcesPoint(modifier = Modifier, index = 3)
            CurrentResourcesPoint(modifier = Modifier, index = 4)
            CurrentResourcesPoint(modifier = Modifier, index = 5)
        }
        Row {
            CurrentResourcesPoint(modifier = Modifier, index = 6)
            CurrentResourcesPoint(modifier = Modifier, index = 7)
        }
    }
}

@Composable
fun CurrentResourcesPoint(modifier: Modifier = Modifier, index: Int) {
    EffectButton(
        modifier = modifier.size(moneyShortWidth, moneyShortHeight), onClick = {
            index.indexToResourceTips().toast()
        }
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.resource_frame),
            contentDescription = null,
        )
        Row(modifier = Modifier.fillMaxSize(), verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(padding8))
            Image(
                modifier = Modifier
                    .size(imageSmallPlus),
                painter = painterResource(id = index.indexToResourceIcon()),
                contentDescription = index.indexToResourceName(),
            )
            AnimatedContent(
                modifier = Modifier.weight(1f),
                targetState = BattleManager.resources[index],
                transitionSpec = {
                    addAnimationVertical(duration = 600).using(
                        SizeTransform(clip = true)
                    )
                },
                label = ""
            ) { target ->
                Text(
                    modifier = Modifier.weight(1f),
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.End,
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                )
            }
            Spacer(modifier = Modifier.size(padding16))
        }
    }
}


@Composable
fun LotteryPoint(cost: Int) {
    val money = AwardManager.lotteryMoney.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.lottery_money),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(padding2))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) Color.White else Color.Red
        )
    }
}

@Composable
fun CurrentLotteryMoney(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.lotteryMoney.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = null
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(padding4))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.lottery_money),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money,
                transitionSpec = {
                    addAnimationVertical(duration = 1200).using(
                        SizeTransform(clip = true)
                    )
                }) { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(padding12))
        }
    }
}



@Composable
fun HolidayPoint(cost: Int) {
    val money = AwardManager.holidayMoney.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.holiday_money),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(padding2))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) Color.White else Color.Red
        )
    }
}

@Composable
fun CurrentHolidayMoney(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.holidayMoney.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = null
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(padding4))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.holiday_money),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money,
                transitionSpec = {
                    addAnimationVertical(duration = 1200).using(
                        SizeTransform(clip = true)
                    )
                }) { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}


@Composable
fun CurrentRealMoneyPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.realMoney.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.resource_frame),
                contentDescription = stringResource(id = R.string.real_money_title)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.real_money_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding2))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
        if (showPlus) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                if (GameApp.instance.canShowAifadian()) {
                    val uri: Uri = Uri.parse(aiFaDian)
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
                } else {
                    GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                }
            }) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(R.drawable.extra_buy),
                    contentDescription = stringResource(id = R.string.real_money_title)
                )
            }
        }
    }
}

@Composable
fun RealMoneyPoint(cost: Int, color: Color = Color.White) {
    val money = AwardManager.realMoney.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.real_money_icon),
            contentDescription = stringResource(id = R.string.real_money_title)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h2,
            color = if (enough) color else DARK_RED
        )
    }
}