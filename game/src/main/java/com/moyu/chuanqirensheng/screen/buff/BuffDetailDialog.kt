package com.moyu.chuanqirensheng.screen.buff

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.core.model.Buff
import com.moyu.core.model.role.Role
import com.moyu.core.util.realValueToDotWithOneDigits
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4

@Composable
fun BuffDetailDialog(switch: MutableState<Pair<Buff, Role>?>) {
    switch.value?.let { pair ->
        val buff = pair.first
        val role = pair.second
        PanelDialog(
            onDismissRequest = { switch.value = null }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = padding26)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.Start,
            ) {
                val realBuffs = if (buff.isCombined()) {
                    role.getBuffList().filter { it.combinedId == buff.combinedId }
                } else listOf(buff)
                Spacer(modifier = Modifier.size(padding26))
                realBuffs.forEach {
                    Text(
                        text = stringResource(R.string.buff_name) + it.name,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    Text(
                        text = stringResource(R.string.buff_source) + (it.skill?.name ?: ""),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    Text(
                        text = stringResource(R.string.buff_effects) + it.buffValue.realValueToDotWithOneDigits(),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    Text(
                        text = stringResource(R.string.buff_layer) + it.getLayerString(),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    Text(
                        text = stringResource(R.string.buff_time) + it.leftTurnString(role) + stringResource(
                            R.string.turn),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
        }
    }
}
