package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import kotlin.math.roundToInt


@Composable
fun ForeverGif(modifier: Modifier,
               resource: String,
               num: Int,
               translateY: Dp = padding0,
               needGap: Boolean = false,
               contentScale: ContentScale = ContentScale.FillWidth) {
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val index = infiniteTransition.animateFloat(
        initialValue = 1f, targetValue = if (needGap) num * 6f else num.toFloat(), animationSpec = infiniteRepeatable(
            animation = tween(if (needGap) num * 6 * 70 else num * 70, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ), label = ""
    )
    if (index.value.roundToInt() <= num) { // 做一个间歇的效果
        Image(
            modifier = modifier
                .graphicsLayer {
                    translationY = translateY.toPx()
                }.scale(1.33f),
            contentScale = contentScale,
            painter = painterResource(
                getImageResourceDrawable(
                    "${resource}${index.value.roundToInt()}"
                )
            ),
            contentDescription = null
        )
    }
}