package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.resource.CurrentResourcesPoint
import com.moyu.chuanqirensheng.ui.theme.expBarHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Title

/**
 * 要处理状态栏
 */
@Composable
fun MasterInfoView(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .paint(
                painterResource(id = R.drawable.common_frame_long),
                contentScale = ContentScale.FillBounds
            )
            .padding(top = WindowInsets.systemBars.asPaddingValues().calculateTopPadding())
            .padding(horizontal = padding14)
    ) {
        MasterView(Modifier)
        Spacer(modifier = Modifier.size(padding6))
        CurrentResourcesPoint(Modifier.weight(1f))
    }
}

@Composable
fun MasterView(modifier: Modifier) {
    Column(modifier = modifier.graphicsLayer {
        translationY = -padding10.toPx()
    }, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier
                    .size(padding90)
                    .padding(padding8)
                    .clip(RoundedCornerShape(50)),
                painter = painterResource(id = getImageResourceDrawable(SkinManager.getGameMasterWithSkinDrawable())),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
        }
        Text(
            text = SkinManager.getGameMasterWithSkinName() + "Lv" + (BattleManager.yourTitle.value.level + 1),
            style = MaterialTheme.typography.h3
        )
        CommonBar(
            Modifier.size(ItemSize.LargePlus.frameSize, expBarHeight),
            currentValue = (BattleManager.yourExp.value - Title.getPreLevelExt(BattleManager.yourTitle.value.level)) % Title.getLevelExp(BattleManager.yourTitle.value.level),
            maxValue = Title.getLevelExp(BattleManager.yourTitle.value.level),
            textColor = Color.White,
            style = MaterialTheme.typography.body1,
        )
    }
}