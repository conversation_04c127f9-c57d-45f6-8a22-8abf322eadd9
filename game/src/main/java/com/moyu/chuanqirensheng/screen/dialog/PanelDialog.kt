package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun PanelDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    showClose: Boolean = true,
    contentBelow: @Composable RowScope.() -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    EmptyDialog(onDismissRequest = onDismissRequest) {
        Column(modifier = Modifier.clickable {
            // 弹窗以内不让点击关闭，用一个空的click占据
        }, horizontalAlignment = Alignment.CenterHorizontally) {
            PanelLayout(size = PanelSize.Normal, showClose = showClose, onClose = onDismissRequest) {
                content()
            }
            Spacer(modifier = Modifier.size(padding6))
            Text(
                modifier = Modifier.alpha(if (showClose) 1f else 0f),
                text = stringResource(id = R.string.quit_tips),
                style = MaterialTheme.typography.h6
            )
            Spacer(modifier = Modifier.size(padding10))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                contentBelow()
            }
        }
    }
}