package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.GameCore
import com.moyu.core.model.Event
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventFailDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val handler = EventManager.getOrCreateHandler(event)
        val award = remember {
            handler.getEventFailAward(event)
        }
        LaunchedEffect(Unit) {
            GameCore.instance.onBattleEffect(SoundEffect.EventFail)
            AwardManager.gainAward(award)
        }
        PanelDialog(
            onDismissRequest = {
                if (show.value != null) {
                    show.value = null
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event, false)
                    }
                }
            }, contentBelow = {
                GameButton(
                    text = stringResource(R.string.see_you_again),
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        if (show.value != null) {
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                EventManager.gotoNextEvent(event, false)
                            }
                        }
                    })
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.year_end, event.name),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    text = event.loseText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                if (!award.isEmpty()) {
                    AwardList(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.CenterHorizontally), award = award,
                        param = defaultParam.copy(textColor = Color.Black)
                    )
                }
            }
        }
    }
}


