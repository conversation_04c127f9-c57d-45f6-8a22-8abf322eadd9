package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.screen.common.GameSnackBar
import com.moyu.chuanqirensheng.screen.effect.DialogUpgradeEffect
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState

// 需要一个全局的关闭弹窗的事件
val dialogClose = mutableIntStateOf(0)


@Composable
fun EmptyDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    content: @Composable BoxScope.() -> Unit
) {
    DisposableEffect(Unit) {
        onDispose {
            dialogEffectState.value = null
            dialogClose.intValue += 1
        }
    }
    CloseHintDialog(onDismissRequest = onDismissRequest) {
        Box(
            modifier = Modifier.fillMaxSize().clickable {
                onDismissRequest()
            },
            contentAlignment = Alignment.Center,
            content = content
        )
        DialogUpgradeEffect(
            modifier = Modifier
                .align(Alignment.Center)
        )
        GameSnackBar()
    }
}