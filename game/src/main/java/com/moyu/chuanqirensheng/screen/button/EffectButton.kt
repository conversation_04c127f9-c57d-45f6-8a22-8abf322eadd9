package com.moyu.chuanqirensheng.screen.button

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect

const val HUGE_GAP = 500
const val MEDIA_GAP = 200
const val QUICK_GAP = 200

@Transient
var globalLastClickTime = 0L


@Composable
fun EffectButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    pressing: MutableState<Boolean> = remember {
        mutableStateOf(false)
    },
    clickGap: Int = HUGE_GAP,
    content: @Composable BoxScope.() -> Unit
) {
    val lastClickTime = remember {
        mutableLongStateOf(0L)
    }
    Box(
        modifier = modifier
            .clickable(
                onClick = {
                    val current = System.currentTimeMillis()
                    if (current - lastClickTime.longValue > clickGap && current - globalLastClickTime > MEDIA_GAP) {
                        lastClickTime.longValue = current
                        globalLastClickTime = current
                        onClick()
                    }
                    GameCore.instance.onBattleEffect(SoundEffect.Click)
                    pressing.value = false
                }), contentAlignment = Alignment.Center, content = content
    )
}