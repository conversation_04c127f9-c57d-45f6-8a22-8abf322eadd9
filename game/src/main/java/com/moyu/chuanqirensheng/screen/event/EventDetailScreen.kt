package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.debug.EventDebugButton
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.role.MasterInfoView
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.B75
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun EventDetailScreen() {
    Box(
        Modifier
            .fillMaxSize()
            .background(B65)) {
        Image(
            modifier = Modifier.fillMaxSize(),
            colorFilter = ColorFilter.tint(
                B75, BlendMode.SrcAtop
            ),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = getImageResourceDrawable(EventManager.selectedEvent.value?.bgPic?.takeIf { it != "0" }
                ?: "0")),
            contentDescription = null)
        Column(Modifier.fillMaxSize()) {
            if (EventManager.selectedEvent.value?.isBattle() == false) {
                Box {
                    MasterInfoView(
                        Modifier
                            .fillMaxWidth()
                            .height(eventTopLayoutHeight)
                    )
                    EventDebugButton(
                        Modifier
                            .align(Alignment.BottomEnd)
                            .padding(top = padding36)
                    )
                }
            }
            EventManager.selectedEvent.value?.let {
                EventLayout(event = it) {
                    EventDetailLayout(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(bottom = gapSmall),
                        event = it
                    )
                }
            }
        }
        if (EventManager.selectedEvent.value?.isBattle() == true) {
            TextLabel2(
                modifier = Modifier.align(Alignment.TopCenter).padding(top = WindowInsets.systemBars.asPaddingValues().calculateTopPadding()),
                text = EventManager.selectedEvent.value?.name?: "",
                labelSize = LabelSize.Huge
            )
        }
    }
}