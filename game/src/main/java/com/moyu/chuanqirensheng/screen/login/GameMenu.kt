package com.moyu.chuanqirensheng.screen.login

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.OUT_HERO
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_ALLY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_HERO
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TALENT
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TASK
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.canStarUp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.Unlock

@Composable
fun BottomItems() {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_hero,
            text = stringResource(R.string.menu1),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_HERO),
            redIcon = repo.allyManager.data.filter { it.isHero() }.any { it.new } || repo.allyManager.data.filter { it.isHero() }.any { it.canStarUp() }
        ) {
            goto(OUT_HERO)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_hero2,
            text = stringResource(R.string.menu2),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_ALLY),
            redIcon = repo.allyManager.data.filter { !it.isHero() }.any { it.new } || repo.allyManager.data.filter { !it.isHero() }.any { it.canStarUp() }
        ) {
            goto(OUT_ALLY)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_talent,
            text = stringResource(R.string.menu3),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_TALENT),
        ) {
            goto(TALENT_SCREEN)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_shop,
            text = stringResource(R.string.menu4),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_SELL),
            redIcon = SellManager.getRedVip() || SellManager.getAllRedFree()
        ) {
            gotoSellWithTabIndex(0)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_quest,
            text = stringResource(id = R.string.quest),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_TASK),
            redIcon = (QuestManager.dailyTasks + QuestManager.oneTimeTasks).any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        ) {
            goto(QUEST_SCREEN)
        }
    }
}


@Composable
fun MenuButton(
    modifier: Modifier,
    text: String = "",
    icon: Int = 0,
    unlock: Unlock?,
    redIcon: Boolean = false,
    upgradeIcon: Boolean = false,
    onclick: () -> Unit
) {
    val locked =
        unlock?.let { !UnlockManager.getUnlockedFlow(unlock) }
            ?: false
    EffectButton(
        modifier = modifier,
        onClick = { if (locked) (unlock?.desc ?: "").toast() else onclick() },
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.menu_button),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding10).graphicsLayer {
                    translationY = -padding6.toPx()
                },
            contentScale = ContentScale.Fit,
            painter = painterResource(id = icon),
            contentDescription = null
        )
        Box(Modifier
            .fillMaxWidth()
            .align(Alignment.BottomCenter)
            .graphicsLayer {
                translationY = padding3.toPx()
            }, contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier
                    .scale(1.2f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.common_name_frame1),
                contentDescription = null
            )
            Text(
                text = text,
                style = MaterialTheme.typography.h2,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
        if (redIcon) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall),
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
        if (upgradeIcon) {
            // 创建一个动画状态，用于处理跳动效果
            val animatedOffsetY = remember { Animatable(0f) }

            // 当组件进入或手指类型改变时，触发跳动动画
            LaunchedEffect(Unit) {
                animatedOffsetY.animateTo(
                    targetValue = -20f, // 设置动画的目标值
                    animationSpec = repeatable( // 使用repeatable重复动画
                        iterations = 999, // 设置动画无限重复
                        animation = tween(
                            durationMillis = 500, // 设置动画持续时间
                            easing = FastOutSlowInEasing // 设置动画的缓动效果
                        ),
                        repeatMode = RepeatMode.Reverse // 设置动画反向重复
                    )
                )
            }
            Image(
                modifier = Modifier.align(Alignment.BottomEnd)
                    .height(padding26)
                    .graphicsLayer {
                        translationY = animatedOffsetY.value
                        translationX = padding8.toPx()
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.hero_starup),
                contentDescription = null
            )
        }
        if (locked) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(imageMedium),
                painter = painterResource(R.drawable.common_lock),
                contentDescription = null
            )
        }
    }
}