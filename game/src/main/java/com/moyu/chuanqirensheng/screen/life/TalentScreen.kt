package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlinx.coroutines.launch
import kotlin.math.max

@Composable
fun TalentScreen() {
    val pagerState = rememberPagerState {
        TalentManager.getUnlockedPageSize()
    }
    val scope = rememberCoroutineScope()
    GameBackground(
        // todo 天赋背景图
        background = getImageResourceDrawable("environment_${pagerState.currentPage + 1}"),
        bgMask = B65,
        title = repo.gameCore.getTalentPool()
            .first { it.type == pagerState.currentPage + 1 }.mainName
    ) {
        HorizontalPager(
            modifier = Modifier
                .fillMaxSize(),
            state = pagerState,
        ) { page ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = padding60)
            ) {
                TalentPage(page + 1)
            }
        }
        if (pagerState.currentPage > 0) {
            EffectButton(modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(bottom = padding120), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage - 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageHugeLite),
                    painter = painterResource(id = R.drawable.common_arrow_left),
                    contentDescription = stringResource(
                        R.string.prev_page
                    )
                )
            }
        }
        if (pagerState.currentPage < pagerState.pageCount - 1) {
            EffectButton(modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = padding120), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage + 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageHugeLite),
                    painter = painterResource(id = R.drawable.common_arrow_right),
                    contentDescription = stringResource(
                        R.string.next_page
                    )
                )
            }
        }
        Row(
            Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            TextLabel2(
                text = stringResource(
                    R.string.talent_total_level,
                    TalentManager.talents.values.sum()
                ),
                labelSize = LabelSize.Medium2
            )
            TextLabel2(
                text = stringResource(
                    R.string.talent_total_page_level,
                    TalentManager.getTotalLevelByType(pagerState.currentPage + 1)
                ),
                labelSize = LabelSize.Medium2
            )
            CurrentDiamondPoint(
                modifier = Modifier
                    .padding(end = padding10),
                showPlus = true,
                showFrame = true
            )
        }
    }
}

@Composable
fun TalentPage(page: Int) {
    val talentTypes = repo.gameCore.getTalentPool().filter { it.level == 1 && it.type == page }
    LazyVerticalGrid(
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(padding10),
        columns = GridCells.Fixed(3)
    ) {
        // 简单改吧，支持position
        items(15) { index ->
            // 一行3个
            val x = index % 3 + 1
            val y = index / 3 + 1
            val talentType =
                talentTypes.firstOrNull { it.position.first() == y && it.position[1] == x }
            if (talentType != null) {
                SingleTalentTypeView(
                    talentMainId = talentType.mainId,
                    talentLevel = TalentManager.talents[talentType.mainId] ?: 0
                )
            } else {
                Spacer(
                    modifier = Modifier.size(
                        ItemSize.LargePlus.frameSize,
                        ItemSize.LargePlus.frameSize + LabelSize.Medium.height
                    )
                )
            }
        }
    }
}


@Composable
fun SingleTalentTypeView(
    talentMainId: Int,
    talentLevel: Int,
    itemSize: ItemSize = ItemSize.LargePlus,
) {
    val talentIcon = repo.gameCore.getTalentPool().first { it.mainId == talentMainId }.icon
    val talentName = repo.gameCore.getTalentPool().first { it.mainId == talentMainId }.name
    val showTalentLevel = max(1, talentLevel)
    val showTalent = repo.gameCore.getTalentPool()
        .firstOrNull { it.level == showTalentLevel && it.mainId == talentMainId }
    showTalent?.let {
        Box(modifier = Modifier, contentAlignment = Alignment.Center) {
            Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
                EffectButton(onClick = {
                    Dialogs.detailTalentDialog.value = talentMainId
                }) {
                    Image(
                        modifier = Modifier
                            .size(itemSize.itemSize)
                            .clip(RoundedCornerShape(itemSize.itemSize / 10)),
                        alignment = Alignment.TopCenter,
                        contentScale = ContentScale.Crop,
                        painter = painterResource(id = getImageResourceDrawable(talentIcon)),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .size(itemSize.itemSize + padding8),
                        alignment = Alignment.TopCenter,
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(id = R.drawable.skill_frame_orange),
                        contentDescription = null
                    )
                }
                Box {
                    TextLabel(
                        text = talentName + if (talentLevel > 0) talentLevel else "",
                        labelSize = LabelSize.Medium,
                        textAlign = TextAlign.Center,
                        contentAlignment = Alignment.Center,
                    ) {
                        Dialogs.detailTalentDialog.value = talentMainId
                    }
                    val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
                    val nextLevel = talentLevel + 1
                    val scroll = repo.gameCore.getTalentPool().first { it.talentSkill == skill.id }
                    val nextScroll = repo.gameCore.getTalentPool()
                        .firstOrNull { it.mainId == scroll.mainId && it.level == nextLevel }
                    val award = nextScroll?.let {
                        (if (nextScroll.costPool == 0) Award() else repo.gameCore.getPoolById(
                            nextScroll.costPool
                        )
                            .toAward()) + Award(diamond = nextScroll.cost)
                    } ?: Award()
                    val (locked, toast) = TalentManager.getLockInfoByTalent(scroll)
                    if (!locked && scroll.level != scroll.levelLimit && AwardManager.isAffordable(
                            award
                        )
                    ) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.CenterStart)
                                .height(padding30)
                                .graphicsLayer {
                                    translationX = padding8.toPx()
                                    translationY = -padding30.toPx()
                                },
                            contentScale = ContentScale.FillHeight,
                            painter = painterResource(R.drawable.hero_starup),
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}