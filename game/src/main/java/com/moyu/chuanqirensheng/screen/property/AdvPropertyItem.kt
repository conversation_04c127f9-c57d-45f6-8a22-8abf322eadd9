package com.moyu.chuanqirensheng.screen.property

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.ui.theme.advPropertyBigHeight
import com.moyu.chuanqirensheng.ui.theme.advPropertyBigWidth
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.music.SoundEffect


@Composable
fun AdvPropertyItem(
    name: String,
    res: Int,
    value: Int,
    tips: String,
) {
    Box(
        Modifier
            .size(advPropertyBigWidth, advPropertyBigHeight)
            .clickable {
                MusicManager.playSound(SoundEffect.Click)
                tips.toast()
            }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_line2),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .graphicsLayer {
                    translationY = -padding8.toPx()
                },
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = res),
            contentDescription = null
        )
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = padding14)
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = name[0].toString(),
                    style = MaterialTheme.typography.h4,
                    modifier = Modifier.width(padding6),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.size(padding2))
                Text(
                    text = name[1].toString(),
                    style = MaterialTheme.typography.h4,
                    modifier = Modifier.width(padding6),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.size(padding2))
                Text(
                    text = value.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            }
        }
    }
}