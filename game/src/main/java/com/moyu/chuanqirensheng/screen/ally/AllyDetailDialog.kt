package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.logic.skillTag
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.screen.common.TitleLabel
import com.moyu.chuanqirensheng.screen.common.VerticalScrollbar
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.effect.MovableImage
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.expBarHeight
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Ally
import com.moyu.core.model.getRaceTreeTypeName
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isAdvForAlly
import com.moyu.core.model.skill.isBattleForAlly
import com.moyu.core.model.skill.isHalo


@Composable
fun AllyDetailDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        LaunchedEffect(Unit) {
            BattleManager.setAllyUnNew(ally)
            GameIllustrationManager.unlockAlly(ally)
        }
        EmptyDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding19),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                val role = BattleManager.getRoleByAlly(ally)
                AllyPanel(ally = ally, role = role) {
                    Dialogs.allyDetailDialog.value = null
                }
                Spacer(modifier = Modifier.size(padding19))
                AllyPropView(newRole = role)
                if (!ally.peek) {
                    Spacer(modifier = Modifier.size(padding14))
                    // 局内英雄没有升星按钮
                    if (!ally.isHero() || !repo.inGame.value) {
                        AllyStarUpView(ally = ally)
                    }
                }
            }
        }
    }
}

@Composable
fun AllyPanel(modifier: Modifier = Modifier, ally: Ally, role: Role, onClose: () -> Unit) {
    PanelLayout(modifier, PanelSize.Normal, showClose = true, onClose = {
        onClose()
    }) {
        AllyDetailLayout(ally, role)
    }
}

@Composable
fun AllyDetailLayout(ally: Ally, role: Role) {
    val race = ally.getRace()
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
            Box(contentAlignment = Alignment.Center) {
                MovableImage(
                    modifier = Modifier
                        .size(padding150),
                    imageResource = getImageResourceDrawable(race.pic),
                )
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = padding6)
                        .size(padding80, padding16)
                ) {
                    RoleHpWithAnim { BattleManager.getRoleByAlly(ally = ally) }
                }
            }
            Column(
                modifier = Modifier.height(padding150),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                Text(text = ally.name, style = MaterialTheme.typography.h1, color = Color.Black)
                Box(contentAlignment = Alignment.Center) {
                    CommonBar(
                        Modifier.size(ItemSize.LargePlus.frameSize, expBarHeight),
                        currentValue = ally.num,
                        maxValue = ally.starUpNum,
                        textColor = Color.Black,
                        style = MaterialTheme.typography.body1,
                        fullRes = R.drawable.common_card_line,
                        emptyRes = R.drawable.common_card_empty,
                    )
                    Box(
                        Modifier
                            .size(imageMediumMinus)
                            .align(Alignment.CenterStart)
                            .graphicsLayer {
                                translationX = -padding16.toPx()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = ally.getQualityFrame()),
                            contentDescription = null
                        )
                        Text(
                            text = ally.star.toString(),
                            style = MaterialTheme.typography.h3
                        )
                    }
                }
                Text(
                    text = ally.getRace().raceType.getRaceTreeTypeName(),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                if (ally.isHero()) {
                    val skill = role.getSkills().firstOrNull { it.isAdvForAlly() }?: role.getSkills().firstOrNull { it.isBattleForAlly() }
                    skill?.skillTagIds?.forEach {
                        TitleLabel(
                            modifier = Modifier.size(padding130, padding48),
                            text = it.skillTag(),
                            frame = R.drawable.common_frame4,
                            color = Color.Black
                        )
                    }
                } else {
                    TitleLabel(
                        modifier = Modifier.size(padding130, padding48),
                        text = stringResource(R.string.ally_quality, ally.quality),
                        frame = R.drawable.common_frame4,
                        color = Color.Black
                    )
                }
            }
        }
        Spacer(modifier = Modifier.size(padding12))
        val scrollState = rememberScrollState()
        Box(
            Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding22)
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(padding10)
            ) {
                // todo role是master，也就是英雄，you是冒险技能的承载者
                // 这个UI，英雄弹窗会用到，战斗弹窗也会用到，所以要区别处理下
                val skills = if (!role.isPlayerSide() || !role.getAlly().isHero()) {
                    role.getSkills().filter { !it.isHalo() }
                } else {
                    role.getRace().skillId.map { repo.gameCore.getSkillById(it) }
                }
                skills.forEach {
                    Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                        SingleSkillView(skill = it, showName = false)
                        Spacer(modifier = Modifier.size(padding8))
                        Column(verticalArrangement = Arrangement.spacedBy(padding6)) {
                            Text(
                                text = it.name,
                                style = MaterialTheme.typography.h2,
                                color = Color.Black
                            )
                            Text(
                                text = it.getRealDescColorful(),
                                style = MaterialTheme.typography.h4,
                                color = Color.Black
                            )
                        }
                    }
                }
            }
            VerticalScrollbar(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxHeight()
                    .width(padding16),
                scrollState = scrollState
            )
        }
    }
}

@Composable
fun AllyPropView(modifier: Modifier = Modifier, newRole: Role, oldRole: Role? = null) {
    PanelLayout(modifier, PanelSize.Small) {
        FlowRow(
            modifier = Modifier.fillMaxSize(),
            horizontalArrangement = Arrangement.spacedBy(padding4),
            verticalArrangement = Arrangement.Center,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 3
        ) {
            newRole.getCurrentProperty().MainPropertyLine(
                originProperty = oldRole?.getCurrentProperty() ?: Property(),
                textStyle = MaterialTheme.typography.h4,
                showBoost = oldRole != null
            )
        }
    }
}

@Composable
fun AllyStarUpView(ally: Ally) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceAround
    ) {
        val maxStar = ally.star >= ally.starLimit
        val buttonText = if (maxStar) GameApp.instance.getWrapString(R.string.star_max) else GameApp.instance.getWrapString(
                R.string.star_up
            )
        val enabled =
            !ally.peek && ally.starUpNum != 0 && ally.num >= ally.starUpNum && !maxStar && AwardManager.diamond.value >= ally.starUpRes
        GameButton(text = buttonText,
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                repo.gameCore.getAllyPool()
                    .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }?.let {
                        if (repo.inGame.value) {
                            Dialogs.allyInGameStarUpDialog.value = ally
                        } else {
                            Dialogs.allyStarUpDialog.value = ally
                        }
                    }
            })
    }
}