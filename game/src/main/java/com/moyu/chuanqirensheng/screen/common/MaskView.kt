package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding4

@Composable
fun MaskView(
    modifier: Modifier,
    text: String,
    itemSize: ItemSize,
    maskPadding: Dp = (itemSize.frameSize - itemSize.itemSize) / 2.4f
) {
    Box(
        modifier = modifier
            .width(itemSize.frameSize)
            .padding(maskPadding),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .height(itemSize.frameSize / 3.5f)
                .clip(
                    RoundedCornerShape(
                        0.dp, 0.dp, padding4, padding4
                    )
                )
                .background(B50)
        )
        Text(
            text = text,
            // todo 皮肤名字显示不下
            style = if (text.length > 7) MaterialTheme.typography.body1 else if (text.length < 3 || text.startsWith(
                    "+"
                ) || text.startsWith("-") || text.toIntOrNull() != null
            ) itemSize.getTextStyle() else MaterialTheme.typography.body1,
            textAlign = TextAlign.Center,
            softWrap = false,
            overflow = TextOverflow.Clip,
            maxLines = 2
        )
    }
}