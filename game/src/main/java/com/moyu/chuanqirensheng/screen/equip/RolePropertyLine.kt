package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.property.AdvPropertyItem
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.property.AdventureProps

@Composable
fun AdventureProps.RolePropertyLine(
    showEmpty: Boolean = true,
    countStart: Int = 0,
    countEnd: Int = 100,
) {
    var count = 1
    if (count in countStart until countEnd) {
        if (showEmpty || science != 0) {
            AdvPropertyItem(
                value = science,
                name = stringResource(R.string.role_prop1),
                res = getImageResourceDrawable("adv_prop1"),
                tips = GameApp.instance.getWrapString(R.string.prop1_tips),
            )
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || politics != 0) {
            AdvPropertyItem(
                value = politics,
                name = stringResource(R.string.role_prop2),
                res = getImageResourceDrawable("adv_prop2"),
                tips = GameApp.instance.getWrapString(R.string.prop2_tips),
            )
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || military != 0) {
            AdvPropertyItem(
                value = military,
                name = stringResource(R.string.role_prop3),
                res = getImageResourceDrawable("adv_prop3"),
                tips = GameApp.instance.getWrapString(R.string.prop3_tips),
            )
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || religion != 0) {
            AdvPropertyItem(
                value = religion,
                name = stringResource(R.string.role_prop4),
                res = getImageResourceDrawable("adv_prop4"),
                tips = GameApp.instance.getWrapString(R.string.prop4_tips),
            )
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || commerce != 0) {
            AdvPropertyItem(
                value = commerce,
                name = stringResource(R.string.role_prop5),
                res = getImageResourceDrawable("adv_prop5"),
                tips = GameApp.instance.getWrapString(R.string.prop5_tips),
            )
        }
    }
}