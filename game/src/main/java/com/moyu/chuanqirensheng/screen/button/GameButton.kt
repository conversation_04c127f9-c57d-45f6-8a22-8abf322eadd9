package com.moyu.chuanqirensheng.screen.button


import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.resource.DiamondPoint
import com.moyu.chuanqirensheng.screen.resource.KeyPoint
import com.moyu.chuanqirensheng.screen.resource.PvpPoint
import com.moyu.chuanqirensheng.screen.resource.RealMoneyPoint
import com.moyu.chuanqirensheng.screen.resource.ResourcesPoint
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonMinusHeight
import com.moyu.chuanqirensheng.ui.theme.buttonMinusWidth
import com.moyu.chuanqirensheng.ui.theme.buttonWidth
import com.moyu.chuanqirensheng.ui.theme.hugeButtonHeight
import com.moyu.chuanqirensheng.ui.theme.hugeButtonWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.smallButtonHeight
import com.moyu.chuanqirensheng.ui.theme.smallButtonWidth


enum class ButtonStyle {
    Blue, Gray, Orange
}

enum class ButtonSize {
    Small, MediumMinus, Medium, Big, Huge
}

enum class ButtonType {
    Normal, Diamond, Key, AiFaDian, Gold, Ad, Pvp, RealMoney
}

@Composable
fun GameButton(
    modifier: Modifier = Modifier,
    text: String = "",
    textColor: Color? = null,
    buttonSize: ButtonSize = ButtonSize.Medium,
    enabled: Boolean = true,
    locked: Boolean = false,
    clickGap: Int = HUGE_GAP,
    redIcon: Boolean = false,
    buttonType: ButtonType = ButtonType.Normal,
    buttonStyle: ButtonStyle = ButtonStyle.Blue,
    onClick: () -> Unit = {},
) {
    val pressing = remember {
        mutableStateOf(false)
    }
    EffectButton(
        modifier = when (buttonSize) {
            ButtonSize.Huge -> modifier.size(hugeButtonWidth, hugeButtonHeight)
            ButtonSize.Big -> modifier.size(bigButtonWidth, bigButtonHeight)
            ButtonSize.MediumMinus -> modifier.size(buttonMinusWidth, buttonMinusHeight)
            ButtonSize.Medium -> modifier.size(buttonWidth, buttonHeight)
            else -> modifier.size(smallButtonWidth, smallButtonHeight)
        }, clickGap = clickGap, pressing = pressing, onClick = onClick
    ) {
        val colorFilter = if (pressing.value) {
            ColorFilter.tint(
                B50, BlendMode.SrcAtop
            )
        } else {
            null
        }
        val buttonSrc =
            if (locked || !enabled) R.drawable.common_button_grey else when (buttonStyle) {
                ButtonStyle.Orange -> R.drawable.common_button2   //更改为新图片
                ButtonStyle.Gray -> R.drawable.common_button_grey      //更改为新图片
                ButtonStyle.Blue -> R.drawable.common_button1 //新增
            }
        Image(
            modifier = Modifier.fillMaxSize(),
            colorFilter = colorFilter,
            contentScale = ContentScale.FillBounds,
            painter = painterResource(buttonSrc),
            contentDescription = null
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (buttonType == ButtonType.Diamond) {
                if (text.toInt() != 0) {
                    DiamondPoint(cost = text.toInt())
                } else {
                    Text(
                        text = stringResource(R.string.free),
                        color = if (pressing.value || locked || !enabled) B50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Pvp) {
                if (text.toInt() != 0) {
                    PvpPoint(cost = text.toInt())
                } else {
                    Text(
                        text = stringResource(R.string.free),
                        color = if (pressing.value || locked || !enabled) B50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Key) {
                if (text.toInt() != 0) {
                    KeyPoint(cost = text.toInt())
                } else {
                    Text(
                        text = stringResource(R.string.free),
                        color = if (pressing.value || locked || !enabled) B50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.Gold) {
                if (text.toInt() != 0) {
                    ResourcesPoint(index = 0, cost = text.toInt())
                } else {
                    Text(
                        text = stringResource(R.string.free),
                        color = if (pressing.value || locked || !enabled) B50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                }
            } else if (buttonType == ButtonType.AiFaDian) {
                if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    Text(
                        text = if (text.toDoubleOrNull() != null) text + stringResource(R.string.real_money_unit) else text,
                        color = if (pressing.value || locked || !enabled) B50 else Color.White,
                        style = buttonSize.fontStyle(),
                        textAlign = TextAlign.Center
                    )
                } else {
                    if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        Text(
                            text = text + stringResource(R.string.real_money_dollar),
                            color = if (pressing.value || locked || !enabled) B50 else Color.White,
                            style = buttonSize.fontStyle(),
                            textAlign = TextAlign.Center
                        )
                    } else {
                        Text(
                            text = text + stringResource(R.string.real_money_unit),
                            color = if (pressing.value || locked || !enabled) B50 else Color.White,
                            style = buttonSize.fontStyle(),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            } else if (buttonType == ButtonType.RealMoney) {
                if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    RealMoneyPoint(cost = text.toInt())
                } else {
                    if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        Text(
                            text = text + stringResource(R.string.real_money_dollar),
                            color = if (pressing.value || locked || !enabled) B50 else Color.White,
                            style = buttonSize.fontStyle(),
                            textAlign = TextAlign.Center
                        )
                    } else {
                        Text(
                            text = text + stringResource(R.string.real_money_unit),
                            color = if (pressing.value || locked || !enabled) B50 else Color.White,
                            style = buttonSize.fontStyle(),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            } else if (buttonType == ButtonType.Ad) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(R.drawable.new_ad_icon),
                    contentDescription = stringResource(R.string.touch_ad_tips)
                )
            } else {
                Text(
                    text = text,
                    color = textColor
                        ?: if (pressing.value || locked || !enabled) B50 else Color.White,
                    style = buttonSize.fontStyle(),
                    textAlign = TextAlign.Center
                )
            }
        }
        if (locked) {
            Image(
                modifier = Modifier.size(imageLarge),
                painter = painterResource(R.drawable.common_lock),
                contentDescription = stringResource(id = R.string.locked),
            )
        }
        if (redIcon) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall)
                    .graphicsLayer {
                        translationX = padding6.toPx()
                        translationY = -padding4.toPx()
                    },
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}


@Composable
fun ButtonSize.fontStyle(): TextStyle {
    return when (this) {
        ButtonSize.Big, ButtonSize.Huge -> MaterialTheme.typography.h1
        ButtonSize.Medium -> MaterialTheme.typography.h2
        ButtonSize.MediumMinus -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h4
    }
}