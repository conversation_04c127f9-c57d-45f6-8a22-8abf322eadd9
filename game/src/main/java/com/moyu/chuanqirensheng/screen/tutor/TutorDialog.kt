package com.moyu.chuanqirensheng.screen.tutor

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.text.tutorText
import com.moyu.chuanqirensheng.text.tutorTitle
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.tutorTitleHeight
import com.moyu.chuanqirensheng.ui.theme.tutorTitleWidth

@Composable
fun TutorDialog(tutorDialog: MutableState<Boolean>) {
    val selectedIndex = remember {
        mutableIntStateOf(0)
    }
    if (tutorDialog.value) {
        PanelDialog(
            onDismissRequest = { tutorDialog.value = false },
            contentBelow = {
                FlowRow(
                    modifier = Modifier,
                    horizontalArrangement = Arrangement.Start,
                    overflow = FlowRowOverflow.Visible,
                ) {
                    tutorTitle.forEachIndexed { index, s ->
                        EffectButton(onClick = {
                            selectedIndex.intValue = index
                        }) {
                            Box(
                                modifier = Modifier.size(tutorTitleWidth, tutorTitleHeight),
                                contentAlignment = Alignment.Center
                            ) {
                                Image(
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.FillBounds,
                                    painter = painterResource(id = if (selectedIndex.intValue == index) R.drawable.common_tab1 else R.drawable.common_tab2),
                                    contentDescription = null
                                )
                                Text(
                                    text = s,
                                    style = MaterialTheme.typography.h4,
                                )
                            }
                        }
                    }
                }
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = tutorText[selectedIndex.intValue],
                    style = MaterialTheme.typography.h3,
                    modifier = Modifier.padding(horizontal = padding8).padding(top = padding6),
                    color = Color.Black
                )
            }
        }
    }
}
