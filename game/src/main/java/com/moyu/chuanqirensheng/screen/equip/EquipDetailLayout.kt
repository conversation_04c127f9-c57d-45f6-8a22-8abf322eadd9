package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.VerticalScrollbar
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role


@Composable
fun EquipDetailLayout(youRole: Role) {
    Box {
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(padding4),
                verticalArrangement = Arrangement.Center,
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 3
            ) {
                youRole.getCurrentProperty().MainPropertyLine(
                    originProperty = Property(),
                    textStyle = MaterialTheme.typography.h4,
                    showBoost = false
                )
            }
            Spacer(modifier = Modifier.size(padding8))
            Text(
                modifier = Modifier.align(Alignment.Start).padding(start = padding30),
                text = stringResource(R.string.wear_equips),
                style = MaterialTheme.typography.h2,
                color = Color.Black
            )
            Spacer(modifier = Modifier.size(padding8))
            EquipSlots()
            Spacer(modifier = Modifier.size(padding8))
            Text(
                modifier = Modifier.align(Alignment.Start).padding(start = padding30),
                text = stringResource(R.string.bag_equips),
                style = MaterialTheme.typography.h2,
                color = Color.Black
            )
            Spacer(modifier = Modifier.size(padding8))
            TreasuresView()
        }
        VerticalScrollbar(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .fillMaxHeight()
                .width(padding16),
            scrollState = scrollState
        )
    }
}