package com.moyu.chuanqirensheng.screen.filter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.core.model.Ally
import com.moyu.core.model.Event


data class ItemOrder<T, R : Comparable<R>>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val order: ((T)-> R)? = null,
)

val heroOrderList = listOf<ItemOrder<Ally, Int>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.no_order), Color.White, listOf(1)) {
        it.quality * 100 + it.star
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.quality), Color.White, listOf(1)) {
        it.quality
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level_hero), Color.White, listOf(1)) {
        it.star
    },
)

val allyOrderList = listOf<ItemOrder<Ally, Int>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.no_order), Color.White, listOf(1)) {
        it.quality * 100 + it.star
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.ally_quality_title), Color.White, listOf(1)) {
        it.quality
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level), Color.White, listOf(1)) {
        it.star
    },
)

val pvpOrderList = listOf<ItemOrder<Ally, Int>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.hero), Color.White, listOf(1)) {
        if (it.isHero()) it.quality * 100000 + it.star * 1000 else it.quality * 100 + it.star
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.allies), Color.White, listOf(1)) {
        if (!it.isHero()) it.quality * 100000 + it.star * 1000 else it.quality * 100 + it.star
    },
)

data class ItemFilter<T>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val filter: (T) -> Boolean
)


val heroFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(GameApp.instance.getWrapString(R.string.hero_quality_3), 7.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.hero_quality_2), 5.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.hero_quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
)

val allyFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race1),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 1 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race2),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 2 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race3),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 3 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race4),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 4 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_7), 7.toQualityColor(), listOf(2)) { card -> card.quality == 7 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_6), 6.toQualityColor(), listOf(2)) { card -> card.quality == 6 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_5), 5.toQualityColor(), listOf(2)) { card -> card.quality == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_4), 4.toQualityColor(), listOf(2)) { card -> card.quality == 4 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_3), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_2), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group1), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 1 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group2), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 2 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group3), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 3 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group4), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 4 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group5), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 5 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group6), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 6 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group7), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 7 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group8), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 8 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group9), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 9 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group10), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 10 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group11), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 11 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group19), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 19 },
)

val pvpFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race1),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 1 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race2),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 2 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race3),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 3 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race4),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 4 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race5),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_7), 7.toQualityColor(), listOf(2)) { card -> card.quality == 7 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_6), 6.toQualityColor(), listOf(2)) { card -> card.quality == 6 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_5), 5.toQualityColor(), listOf(2)) { card -> card.quality == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_4), 4.toQualityColor(), listOf(2)) { card -> card.quality == 4 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_3), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_2), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_1), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group1), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 1 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group2), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 2 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group3), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 3 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group4), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 4 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group5), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 5 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group6), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 6 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group7), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 7 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group8), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 8 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group9), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 9 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group10), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 10 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group11), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 11 },
    ItemFilter(GameApp.instance.getWrapString(com.moyu.core.R.string.group19), Color.Blue, listOf(4)) { card -> card.getRace().raceType2 == 19 },
)

val eventFilterList = listOf<ItemFilter<Event>>(
    ItemFilter("全部", Color.White, listOf(1, 2, 3, 4)) { true },
    ItemFilter("建造类", Color.White, listOf(1)) { card -> card.play == 1 },
    ItemFilter("直接奖励类-陆上探索", Color.White, listOf(1)) { card -> card.play == 2 },
    ItemFilter("多选一奖励类-陆上探索", Color.White, listOf(1)) { card -> card.play == 3 },
    ItemFilter("治疗类-陆上探索", Color.White, listOf(1)) { card -> card.play == 4 },
    ItemFilter("升级兵种类-陆上探索", Color.White, listOf(1)) { card -> card.play == 5 },
    ItemFilter("直接奖励类-海上探索", Color.White, listOf(1)) { card -> card.play == 6 },
    ItemFilter("多选一奖励类-海上探索", Color.White, listOf(1)) { card -> card.play == 7 },
    ItemFilter("治疗类-海上探索", Color.White, listOf(1)) { card -> card.play == 8 },
    ItemFilter("直接奖励类-地下探索", Color.White, listOf(1)) { card -> card.play == 9 },
    ItemFilter("多选一奖励类-地下探索", Color.White, listOf(1)) { card -> card.play == 10 },
    ItemFilter("治疗类-地下探索", Color.White, listOf(1)) { card -> card.play == 11 },
    ItemFilter("诅咒之战直接奖励类-陆上探索", Color.Green, listOf(1)) { card -> card.play == 21 },
    ItemFilter("诅咒之战多选一奖励类-陆上探索", Color.Green, listOf(1)) { card -> card.play == 22 },
    ItemFilter("诅咒之战直接奖励类-海上探索", Color.Green, listOf(1)) { card -> card.play == 23 },
    ItemFilter("诅咒之战多选一奖励类-海上探索", Color.Green, listOf(1)) { card -> card.play == 24 },
    ItemFilter("诅咒之战直接奖励类-地下探索", Color.Green, listOf(1)) { card -> card.play == 25 },
    ItemFilter("诅咒之战多选一奖励类-地下探索", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("遭遇战", Color.Green, listOf(1)) { card -> card.play == 27 },
    ItemFilter("攻城战类", Color.Green, listOf(1)) { card -> card.play == 28 },
    ItemFilter("守城战类", Color.Green, listOf(1)) { card -> card.play == 29 },
    ItemFilter("野怪-陆上探索", Color.Green, listOf(1)) { card -> card.play == 30 },
    ItemFilter("野怪-海上探索", Color.Green, listOf(1)) { card -> card.play == 31 },
    ItemFilter("野怪-地下探索", Color.Green, listOf(1)) { card -> card.play == 32 },
)

@Composable
fun CommonFilterView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(padding60),
            painter = painterResource(id = R.drawable.menu_filter),
            contentDescription = stringResource(R.string.filter)
        )
    }
}

@Composable
fun CommonOrderView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(padding60, padding28),
            painter = painterResource(id = R.drawable.menu_rank),
            contentScale = ContentScale.FillWidth,
            contentDescription = stringResource(R.string.order)
        )
    }
}

