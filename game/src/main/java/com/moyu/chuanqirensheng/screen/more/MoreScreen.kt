package com.moyu.chuanqirensheng.screen.more

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.router.ENDING_SCREEN
import com.moyu.chuanqirensheng.feature.router.MAILS_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.server.ui.ServerSelectorLayout
import com.moyu.chuanqirensheng.feature.server.ui.ServerView
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SHARE_CODE
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.awardList
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding44
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

data class MoreItem(
    val name: String,
    val route: () -> Unit,
    val icon: String,
    val unlock: () -> Boolean = { true },
    val red: () -> Boolean = { false }
)

val moreItems = listOf(
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.life_record), route = {
            goto(ENDING_SCREEN)
        }, icon = "ending_icon"
    ),
    MoreItem(name = GameApp.instance.getWrapString(R.string.share_code),
        route = { Dialogs.shareCodeDialog.value = true },
        icon = "share_code_icon",
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_SHARE_CODE)
            UnlockManager.getUnlockedFlow(unlock)
        }),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.tutor),
        route = { Dialogs.tutorDialog.value = true },
        icon = "tutor_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.mails),
        route = { goto(MAILS_SCREEN) },
        icon = "mail_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.setting),
        route = { Dialogs.settingDialog.value = true },
        icon = "setting_icon"
    ),
)

@Composable
fun MoreScreen() {
    GameBackground(title = stringResource(R.string.more)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding6)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding36))
            FlowRow(
                modifier = Modifier.padding(horizontal = padding10),
                maxItemsInEachRow = 3,
                horizontalArrangement = Arrangement.spacedBy(padding16),
                overflow = FlowRowOverflow.Visible,
            ) {
                moreItems.filter { it.unlock() }.forEach {
                    OneItem(modifier = Modifier.width(padding110), it)
                }
            }
            val text = remember {
                mutableStateOf("")
            }
            Spacer(modifier = Modifier.size(padding19))
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = padding26)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    DecorateTextField(
                        modifier = Modifier
                            .weight(1f)
                            .height(
                                textFieldHeight
                            ), text.value, hintText = stringResource(id = R.string.input_hint)
                    ) {
                        text.value = it
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    GameButton(
                        enabled = text.value.isNotEmpty(),
                        onClick = {
                            AwardManager.doNetAward(text.value)
                        },
                        text = stringResource(R.string.exchange),
                        textColor = Color.White,
                        buttonStyle = ButtonStyle.Orange,
                        buttonSize = ButtonSize.Medium
                    )
                }
                Spacer(modifier = Modifier.size(padding10))
                if (awardList.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.common_codes),
                        style = MaterialTheme.typography.h3
                    )
                    Spacer(modifier = Modifier.size(padding6))
                }
                FlowRow(
                    modifier = Modifier.padding(padding6),
                    overflow = FlowRowOverflow.Visible,
                ) {
                    awardList.filter { getLongFlowByKey(it.code) == 0L }.forEach {
                        Text(
                            modifier = Modifier.clickable {
                                val myClipboard =
                                    GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                val myClip = ClipData.newPlainText("text", it.code)
                                myClipboard.setPrimaryClip(myClip)
                                text.value = it.code
                            },
                            text = it.code,
                            style = MaterialTheme.typography.h4,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                        Spacer(modifier = Modifier.size(padding10))
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
                if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                    Text(
                        text = stringResource(R.string.contact1),
                        style = MaterialTheme.typography.h3
                    )
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "<EMAIL>")
                        myClipboard.setPrimaryClip(myClip)
                        ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "email：",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "<EMAIL>",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "<EMAIL>")
                        myClipboard.setPrimaryClip(myClip)
                        ("email：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "email：",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "<EMAIL>",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                } else if (GameApp.instance.resources.getString(R.string.platform_channel) == "toutiao") {
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "1165306303")
                        myClipboard.setPrimaryClip(myClip)
                        ("客服一QQ:1165306303" + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "客服一QQ: ",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "1165306303",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "3805429032")
                        myClipboard.setPrimaryClip(myClip)
                        ("客服二QQ:3805429032" + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "客服二QQ: ",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "3805429032",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                } else {
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "T15760456854")
                        myClipboard.setPrimaryClip(myClip)
                        ("微信: T15760456854" + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "微信: ",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "T15760456854",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
            }
            Spacer(modifier = Modifier.size(padding10))
        }

        val showServerSelector = remember {
            mutableStateOf(false)
        }

        Row(
            Modifier.align(Alignment.BottomEnd).padding(padding22),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (GameApp.instance.loginData.value.serverList.size > 1) {
                ServerView(showServerSelector)
            }
        }
        ServerSelectorLayout(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(end = padding22, bottom = padding44),
            show = showServerSelector,
            callback = {
                if (ServerManager.getSavedServerId() != it) {
                    Dialogs.alertDialog.value = CommonAlert(
                        title = GameApp.instance.getWrapString(R.string.switch_server),
                        content = GameApp.instance.getWrapString(R.string.switch_server_tips),
                        confirmText = GameApp.instance.getWrapString(R.string.switch_title),
                        onConfirm = {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                GameApp.instance.switchServerId(it)
                            }
                        }
                    )
                }
            }
        )
    }
}

@Composable
fun OneItem(modifier: Modifier, moreItem: MoreItem, size: ItemSize = ItemSize.LargePlus, callback: () -> Unit = {}) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box {
            IconView(
                itemSize = ItemSize.Huge,
                res = getImageResourceDrawable(moreItem.icon),
                frame = R.drawable.hero_frame,
                name = moreItem.name,
                showName = false,
                clipShape = RoundedCornerShape(50)
            ) {
                callback()
                moreItem.route()
            }
            if (moreItem.red()) {
                Image(
                    modifier = Modifier
                        .size(imageSmall)
                        .align(Alignment.TopEnd),
                    painter = painterResource(id = R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
        Box(modifier = Modifier.graphicsLayer {
            translationY = -padding10.toPx()
        }, contentAlignment = Alignment.Center) {
            Text(modifier = Modifier.graphicsLayer {
                translationY = padding4.toPx()
            }, text = moreItem.name, style = size.getTextStyle(), textAlign = TextAlign.Center)
        }
    }
}
