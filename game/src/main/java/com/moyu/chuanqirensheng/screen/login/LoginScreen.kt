package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugButton
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.EVENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.TALENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.holiday.ui.HolidayIcon
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.ui.RankIcon
import com.moyu.chuanqirensheng.feature.router.ACTIVITY_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.DUNGEON_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sign.ui.SignIcon
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_QUEST
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_WORLD_BOSS
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.antiaddict.AntiAddictDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageHugeLiteFrame
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding80


@Composable
fun LoginScreen() {
    LaunchedEffect(Unit) {
        repo.inGame.value = false
        // 海外版本没有健康页面，所以这里还是要有一次初始化，里面有去重
        if (!PrivacyManager.privacyNeedShow && !PrivacyManager.permissionNeedShow) {
            GameApp.instance.initSDK(GameApp.instance.activity)
        }
        // 保证能及时刷新
        QuestManager.createTasks()

        // 学习天赋引导
        if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_MENU_SELL))) {
            // 现在要改成，只要过了前面的固定引导就弹
            if (GuideManager.guideIndex.intValue == TALENT_GUIDE_START
                || GuideManager.guideIndex.intValue == BATTLE_GUIDE_START + 1
                || GuideManager.guideIndex.intValue == BATTLE_GUIDE_START
            ) {
                GuideManager.guideIndex.intValue = TALENT_GUIDE_START
                GuideManager.showGuide.value = true
                // 显示引导后，要标记引导已经结束，免得后续中间引导冒出来
                setIntValueByKey(KEY_GUIDE_INDEX, 999)
            }
        }
    }
    GameBackground(
        showCloseIcon = false,
        showPreviewIcon = false,
    ) {
        Column(Modifier.fillMaxSize()) {
            Spacer(modifier = Modifier.size(padding72))
            GameLogo(
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .height(padding200)
            )
            Spacer(modifier = Modifier.weight(1f))
            StartAndContinueGame(
                Modifier
                    .align(Alignment.CenterHorizontally)
            )
            Spacer(modifier = Modifier.weight(1f))
            Box(Modifier.fillMaxWidth()) {
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = padding6),
                    verticalArrangement = spacedBy(padding6)
                ) {
                    SignIcon()
                    BattlePassIcon()
                    BattlePass2Icon()
                    if (GameApp.instance.isToutiao()) {
                        ErrorOrderIcon()
                    }
                    MoreIcon()
                }
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .heightIn(padding0, padding360)
                        .padding(start = padding6)
                        .verticalScroll(rememberScrollState()),
                    verticalArrangement = spacedBy(padding6)
                ) {
                    GiftDetailIcon()
                    VipIcon()
                    RankIcon()
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            BottomItems()
            Spacer(modifier = Modifier.size(padding5))
        }
        Row(Modifier.fillMaxWidth()) {
            TopItemsLeft()
            Spacer(modifier = Modifier.weight(1f))
            TopItemsRight()
        }
        HolidayIcon(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding12)
                .graphicsLayer {
                    translationY = -padding80.toPx()
                })
        DebugButton(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = padding120)
        )
    }
    AntiAddictDialog {
        if (GameApp.instance.hasLogin()) {
            GameApp.instance.checkAntiAddiction(GameApp.instance.activity)
        } else {
            GameApp.instance.login(GameApp.instance.activity)
        }
    }
}

@Composable
fun StartAndContinueGame(modifier: Modifier) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            GameButton(
                buttonSize = ButtonSize.Huge,
                buttonStyle = ButtonStyle.Orange,
                text = stringResource(id = R.string.start_game),
            ) {
                if (GuideManager.guideIndex.intValue < EVENT_GUIDE_START) {
                    GuideManager.guideIndex.intValue = EVENT_GUIDE_START
                }
                repo.clickStart()
            }
            if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_PVP))
                || UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_PVP2))
                || UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_TOWER))
            ) {
                EffectButton(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .size(
                            imageHugeLite,
                            imageHugeLiteFrame
                        ),
                    onClick = {
                        goto(DUNGEON_ALL_SCREEN)
                    },
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(id = R.drawable.blue_home_bkg),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding14)
                            .graphicsLayer {
                                translationY = -padding6.toPx()
                            },
                        contentScale = ContentScale.Fit,
                        painter = painterResource(id = R.drawable.pvp_icon),
                        contentDescription = null
                    )
                    if ((PvpManager.unlocked() && PvpManager.hasRedAll())
                        || (Pvp2Manager.unlocked() && Pvp2Manager.hasRedAll())
                        || (TowerManager.unlocked() && TowerManager.hasRed())
                    ) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .size(imageSmall),
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                    Text(
                        modifier = Modifier.align(Alignment.BottomCenter).clip(RoundedCornerShape(
                            padding4
                        )).background(B50).padding(horizontal = padding4, vertical = padding2),
                        text = stringResource(R.string.challenge),
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
            if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY))
                || UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_NEW_QUEST))
                || UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_WORLD_BOSS))
                || UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_LOTTERY))
            ) {
                EffectButton(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .size(
                            imageHugeLite,
                            imageHugeLiteFrame
                        ),
                    onClick = {
                        goto(ACTIVITY_ALL_SCREEN)
                    },
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(id = R.drawable.blue_home_bkg),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding14)
                            .graphicsLayer {
                                translationY = -padding6.toPx()
                            },
                        contentScale = ContentScale.Fit,
                        painter = painterResource(id = R.drawable.lottery_icon),
                        contentDescription = null
                    )
                    if ((SevenDayManager.show() && SevenDayManager.unlocked() && SevenDayManager.hasRed())
                        || (LotteryManager.unlocked() && LotteryManager.hasRed())
                        || (MissionManager.unlocked() && MissionManager.hasRed())
                        || (WorldBossManager.unlocked() && WorldBossManager.hasRed())
                    ) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopStart)
                                .size(imageSmall),
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                    Text(
                        modifier = Modifier.align(Alignment.BottomCenter).clip(RoundedCornerShape(
                            padding4
                        )).background(B50).padding(horizontal = padding4, vertical = padding2),
                        text = stringResource(R.string.activities_title),
                        style = MaterialTheme.typography.h3,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        Spacer(modifier = Modifier.size(padding12))
        if (ContinueManager.haveSaver()) {
            if (GuideManager.guideIndex.intValue == 0) {
                GuideManager.showGuide.value = false
                GuideManager.guideIndex.intValue = BATTLE_GUIDE_START
                setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
            }
            GameButton(
                buttonStyle = ButtonStyle.Blue,
                buttonSize = ButtonSize.Huge,
                text = stringResource(id = R.string.continue_game),
            ) {
                ContinueManager.recreateGame()
                repo.continueGame()
            }
        }
    }
}
