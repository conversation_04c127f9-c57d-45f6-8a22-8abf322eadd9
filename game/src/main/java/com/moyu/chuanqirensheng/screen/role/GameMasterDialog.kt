package com.moyu.chuanqirensheng.screen.role

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyDetailLayout
import com.moyu.chuanqirensheng.screen.dialog.PanelSheetDialog
import com.moyu.chuanqirensheng.screen.equip.EquipDetailLayout
import com.moyu.chuanqirensheng.screen.info.InfoLayout


@Composable
fun GameMasterDialog(show: MutableState<Int>) {
    if (show.value >= 0) {
        PanelSheetDialog(
            selected = remember {
                mutableIntStateOf(show.value)
            },
            titles = listOf(
                stringResource(id = R.string.master_sheet1),
                stringResource(id = R.string.master_sheet2),
                stringResource(id = R.string.master_sheet3),
            ),
            reds = listOf(false, BattleManager.equipGameData.any { it.new }, false),
            onDismissRequest = { show.value = -1 }) { index ->
            val you = BattleManager.getGameMaster()
            val youRole = BattleManager.getGameMasterRole()
            when (index) {
                0 -> {
                    AllyDetailLayout(you, youRole)
                }

                1 -> {
                    DisposableEffect(Unit) {
                        onDispose {
                            BattleManager.setEquipUnNew()
                        }
                    }
                    EquipDetailLayout(youRole)
                }
                else -> {
                    InfoLayout(Modifier, repo.lifeInfo)
                }
            }
        }
    }
}