package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward

@Composable
fun PvpRankAwardDialog(show: MutableState<Boolean>) {
    if (show.value) {
        PanelDialog(
            onDismissRequest = { show.value = false }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding16),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.pvp_rank_awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(padding16))
                
                // 获取PVP排名任务
                val pvpRankQuests = remember {
                    repo.gameCore.getGameTaskPool()
                        .filter { it.type == QuestEvent.PVP_RANK.id }
                        .sortedBy { it.subType.firstOrNull() ?: 0 }
                }
                
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(padding10)
                ) {
                    items(pvpRankQuests) { quest ->
                        PvpRankAwardItem(quest = quest)
                    }
                }
            }
        }
    }
}

@Composable
private fun PvpRankAwardItem(quest: Quest) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = padding10),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 排名标题
        val rankText = quest.name
        
        Text(
            text = rankText,
            style = MaterialTheme.typography.h2,
            color = Color.Black,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(padding10))
        
        // 奖励显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            val award = quest.toAward()
            AwardList(
                award = award,
                param = defaultParam.copy(
                    numInFrame = true,
                    showName = false,
                    itemSize = ItemSize.Medium,
                    textColor = Color.Black
                ),
                paddingVerticalInDp = padding10,
                paddingHorizontalInDp = padding10
            )
        }
    }
}
