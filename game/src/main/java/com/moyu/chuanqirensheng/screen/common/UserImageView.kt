package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable


@Composable
fun UserImageView(
    modifier: Modifier = Modifier,
    headRes: String? = null,
    name: String? = null,
) {
    Box(modifier = modifier) {
        Box(Modifier.align(Alignment.CenterStart)) {
            name?.let {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .height(userHeadHeight * 2 / 3)
                        .width(userHeadWidth)
                        .graphicsLayer {
                            translationX = padding22.toPx()
                        },
                    painter = painterResource(id = R.drawable.common_name_frame3),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
            }
            headRes?.let {
                Image(
                    // todo
                    painter = if (it.startsWith("http")) rememberAsyncImagePainter(it) else painterResource(
                        id = getImageResourceDrawable(it)
                    ),
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.TopCenter,
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .size(userHeadHeight)
                        .padding(padding6)
                        .graphicsLayer {
                            translationY = padding1.toPx()
                            translationX = -padding1.toPx()
                        }
                        .clip(RoundedCornerShape(50)),
                    contentDescription = null
                )
            }
            Image(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .size(userHeadHeight)
                    .graphicsLayer {
                        translationY = padding1.toPx()
                        translationX = -padding1.toPx()
                    },
                painter = painterResource(id = R.drawable.hero_frame2),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
        }
        name?.let {
            Text(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = userHeadHeight, bottom = padding16),
                text = name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.h3,
            )
        }
    }
}