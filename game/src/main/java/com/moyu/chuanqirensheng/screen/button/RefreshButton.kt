package com.moyu.chuanqirensheng.screen.button

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus

@Composable
fun RefreshButton(modifier: Modifier = Modifier, text: String, callback: () -> Unit) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        Image(
            modifier = Modifier.width(imageLargePlus),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.menu_refresh),
            contentDescription = text
        )
    }
}