package com.moyu.chuanqirensheng.screen.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding57
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.ui.theme.padding90

enum class ItemSize(val frameSize: Dp, val itemSize: Dp) {
    Small(imageMediumMinus, imageSmallPlus),
    Medium(imageLargePlus, imageLargeFrame),
    MediumPlus(padding48, padding40),
    LargeMinus(padding57, padding48),
    Large(padding72, padding57),
    LargeP(padding84, padding66),
    LargePlus(padding100, padding72),
    Huge(padding110, padding90),
}

@Composable
fun ItemSize.getTextStyle(): TextStyle {
    return when (this) {
        ItemSize.Small -> MaterialTheme.typography.h5
        ItemSize.Medium -> MaterialTheme.typography.h5
        ItemSize.MediumPlus -> MaterialTheme.typography.body1
        ItemSize.LargeMinus -> MaterialTheme.typography.h5
        ItemSize.Large -> MaterialTheme.typography.h4
        ItemSize.LargePlus -> MaterialTheme.typography.h3
        else -> MaterialTheme.typography.h2
    }
}