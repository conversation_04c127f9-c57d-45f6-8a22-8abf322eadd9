package com.moyu.chuanqirensheng.screen.battle

import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.animateLarge
import com.moyu.chuanqirensheng.ui.theme.buffSize
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.hpWidth
import com.moyu.chuanqirensheng.ui.theme.oneRoleWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.chuanqirensheng.ui.theme.singleRoleWidth
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.ALLY_ROW2_FIRST
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.logic.role.ENEMY_ROW2_FIRST
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.role.Role
import kotlinx.coroutines.launch

@Composable
fun BattleFieldLayout(roles: Map<Int, Role?>, extraLayout: @Composable ColumnScope.() -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(vertical = padding19),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly
    ) {
        // todo 预留一个事件title的距离
        Spacer(modifier = Modifier.size(padding34))
        EnemiesRow(roles)
        Column(
            Modifier
                .height(gapLarge)
                .align(Alignment.End)
                .graphicsLayer {
                    translationX = gapSmallPlus.toPx()
                }) {
            Spacer(modifier = Modifier.weight(1f))
            extraLayout()
        }
        TurnView(modifier = Modifier)
        Spacer(modifier = Modifier.size(gapLarge))
        PlayersRow(roles)
    }
}

@Composable
fun EnemiesRow(roles: Map<Int, Role?>) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(4) { index ->
            roles[index + ENEMY_ROW2_FIRST]?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: Box(Modifier.size(oneRoleWidth, singleRoleHeight))
        }
    }
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(4) { index ->
            roles[index + ENEMY_ROW1_FIRST]?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: Box(Modifier.size(oneRoleWidth, singleRoleHeight))
        }
    }
}


@Composable
fun PlayersRow(roles: Map<Int, Role?>) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(4) { index ->
            roles[index + ALLY_ROW1_FIRST]?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: Box(Modifier.size(oneRoleWidth, singleRoleHeight))
        }
    }
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(4) { index ->
            roles[index + ALLY_ROW2_FIRST]?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: Box(Modifier.size(oneRoleWidth, singleRoleHeight))
        }
    }
}

@Composable
fun TurnView(modifier: Modifier = Modifier) {
    val turnName = repo.battleTurn.intValue
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.height(padding54),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_frame4),
            contentDescription = null
        )
        Row {
            val environment =
                if (repo.battleEnvironment.value.name.isEmpty()) "" else "-" + repo.battleEnvironment.value.name
            Text(
                text = stringResource(R.string.round_tips, turnName) + environment,
                style = MaterialTheme.typography.h3,
                color = Color.Black
            )
            if (DebugManager.singleStep) {
                Text(modifier = Modifier.clickable {
                    GameApp.globalScope.launch(gameDispatcher) {
                        repo.battle.value.nextStep()
                    }
                }, text = "下一步", style = MaterialTheme.typography.h3)
            }
        }
//        LaunchedEffect(turnName) {
//            newTurnEffectState.value = turnEffect
//        }
//        NewTurnEffect()
    }
}

@Composable
fun OneRoleInBattle(modifier: Modifier = Modifier, role: () -> Role) {
    val alpha by animateFloatAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt() * 4
        ), targetValue = if (role().isDeath()) 0f else 1f, label = ""
    )
    Box(
        modifier
            .graphicsLayer {
                this.alpha = alpha
            }
            .onGloballyPositioned {
                BattleManager.battleRolePositions[role().playerId()] = it
                    .positionInRoot()
                    .let {
                        try {
                            Pair(
                                it.x
                                    .toInt()
                                    .pixelToDp() - hpWidth,
                                it.y
                                    .toInt()
                                    .pixelToDp()
                            )
                        } catch (e: Exception) {
                            Pair(padding1, padding1)
                        }
                    }
            }, contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (repo.inBattle.value) {
                Row(
                    Modifier
                        .height(buffSize)
                        .horizontalScroll(rememberScrollState()),
                ) {
                    role().getShowBothBuff().forEach { buff ->
                        OneBuffGrid(buff) {
                            Dialogs.buffDetailDialog.value = Pair(it, role())
                        }
                    }
                }
            }
            Box(
                Modifier.size(singleRoleWidth, singleRoleHeight),
                contentAlignment = Alignment.Center
            ) {
                SingleRoleInBattleLayout(roleGetter = role)
            }
        }
        Box(modifier = Modifier.padding(bottom = padding16)) {
            RoleStatusBeingAttackGif(role)
        }
        DamageTextLayout(roleGetter = role)
    }
}

@Composable
fun DamageTextLayout(roleGetter: () -> Role) {
    val role = roleGetter()
    val damageOffset by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt()
        ), targetValue = when {
            role.hasState(ActionStateType.BeingAttack) -> -animateLarge
            role.hasState(ActionStateType.BeingHeal) -> -animateLarge
            role.hasState(ActionStateType.DoSkill) -> -animateLarge
            else -> 0.dp
        }, label = ""
    )
    Column(modifier = Modifier
        .fillMaxWidth()
        .graphicsLayer {
            if (damageOffset
                    .toPx()
                    .isNaN()
                    .not()
            ) {
                translationY = damageOffset.toPx()
            }
//            if ((-damageOffset.toPx() - padding10.toPx())
//                    .isNaN()
//                    .not()
//            ) {
//                translationX = -damageOffset.toPx() - padding10.toPx()
//            }
//            clip = false
        }) {
        // 确保在屏幕中间靠右地方播放
        role.getStateList().forEach {
            RoleDamageOrSkillText(Modifier, it)
        }
    }
}