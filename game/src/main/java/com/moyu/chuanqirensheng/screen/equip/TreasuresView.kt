package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.skill.SingleEquipView
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding4


@Composable
fun TreasuresView() {
    val itemSize = ItemSize.Large
    val equips = BattleManager.getGameEquips().filter { !it.isEquipReplaceable() }
    Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        if (equips.isEmpty()) {
            Text(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(start = padding30),
                text = stringResource(id = R.string.empty_dialog),
                color = Color.Black,
                style = MaterialTheme.typography.h3
            )
        } else {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(padding4),
                verticalArrangement = Arrangement.spacedBy(padding4),
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 4
            ) {
                equips.forEach { skill ->
                    Box(
                        modifier = Modifier.width(itemSize.frameSize),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Box(contentAlignment = Alignment.Center) {
                                SingleEquipView(
                                    equipment = skill,
                                    itemSize = itemSize,
                                    showName = true,
                                    textColor = Color.Black
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}