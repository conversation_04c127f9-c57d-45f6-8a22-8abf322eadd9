package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.foundation.shape.GenericShape
import com.moyu.chuanqirensheng.util.dpToPixel

fun InverseRoundedCornerShape(cornerRadius: androidx.compose.ui.unit.Dp) = GenericShape { size, _ ->
    val radius = cornerRadius.value.dpToPixel()
    // 增加了控制点的距离，以使曲线更平滑
    val controlDistance = radius * 0.9f // 控制点到顶点的距离，可以调整此值来改变弧度的圆滑程度

    // Top left corner
    moveTo(0f, radius)
    cubicTo(controlDistance, radius, controlDistance, 0f, radius, 0f)
    lineTo(size.width - radius, 0f)

    // Top right corner
    cubicTo(size.width - controlDistance, 0f, size.width - controlDistance, radius, size.width, radius)
    lineTo(size.width, size.height - radius)

    // Bottom right corner
    cubicTo(size.width, size.height - controlDistance, size.width - controlDistance, size.height - radius, size.width - radius, size.height)
    lineTo(radius, size.height)

    // Bottom left corner
    cubicTo(controlDistance, size.height, controlDistance, size.height - radius, 0f, size.height - radius)
    close()
}
