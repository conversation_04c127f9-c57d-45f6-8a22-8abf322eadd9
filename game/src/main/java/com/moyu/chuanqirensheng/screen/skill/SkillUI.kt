package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure

fun Skill.getElementTypeRes(): Int {
    return if (isAdventure()) {
        getImageResourceDrawable("card_adventure_type_$elementType")
    } else {
        getImageResourceDrawable("card_skill_type_$elementType")
    }

}

fun getSkillDescColor(current: Boolean): Color {
    return if (current) Color.White else Color.Gray
}