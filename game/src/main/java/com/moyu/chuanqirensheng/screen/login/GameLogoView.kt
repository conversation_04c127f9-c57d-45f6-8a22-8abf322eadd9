package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.common.VersionTag
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun GameLogo(modifier: Modifier) {
    if (GameApp.instance.isToutiao()) {
        Column(
            modifier = modifier,
        ) {
            VersionTag(
                Modifier
                    .padding(start = padding10)
                    .align(Alignment.Start).graphicsLayer {
                        translationY = padding16.toPx()
                    }
            )
            Spacer(
                modifier = Modifier
                    .fillMaxWidth().padding(horizontal = padding12),
            )
        }
    } else {
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                modifier = Modifier
                    .fillMaxWidth().padding(horizontal = padding12),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.logo),
                contentDescription = stringResource(
                    id = R.string.app_name
                )
            )
            VersionTag(
                Modifier
                    .padding(end = padding26)
                    .align(Alignment.End).graphicsLayer {
                        translationY = padding6.toPx()
                    }
            )
        }
    }
}
