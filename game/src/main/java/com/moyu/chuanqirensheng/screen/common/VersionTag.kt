package com.moyu.chuanqirensheng.screen.common

import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.util.getVersion


@Composable
fun VersionTag(modifier: Modifier = Modifier) {
    Text(
        modifier = modifier,
        text = "V." + getVersion(),
        style = MaterialTheme.typography.h3,
    )
}