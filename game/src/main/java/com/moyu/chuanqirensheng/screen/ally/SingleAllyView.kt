package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.draw.canShowEffect
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.effect.MovableImage
import com.moyu.chuanqirensheng.screen.effect.orangeItemGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.expBarHeight
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Ally
import kotlin.math.roundToInt


@Composable
fun SingleAllyView(
    ally: Ally,
    showName: Boolean = true,
    showNum: Boolean = true,
    showHp: Boolean = false,
    extraInfo: String = "",
    textColor: Color = Color.Black,
    frame: Int? = null,
    showRed: Boolean = false,
    showStar: Boolean = true,
    hide: Boolean = false,
    showEffect: Boolean = false,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it }
) {
    LaunchedEffect(Unit) {
        if (!hide) {
            GameIllustrationManager.unlockAlly(ally)
        }
    }
    val race = repo.gameCore.getRaceById(ally.id)
    EffectButton(modifier = Modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(ally)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                frame?.let {
                    Image(
                        modifier = Modifier.size(itemSize.frameSize),
                        painter = painterResource(it),
                        contentDescription = null,
                    )
                }
                if (showEffect && ((ally.canShowEffect()))) {
                    val infiniteTransition = rememberInfiniteTransition(label = "")
                    val index = infiniteTransition.animateFloat(
                        initialValue = 1f,
                        targetValue = orangeItemGif.count.toFloat(),
                        animationSpec = infiniteRepeatable(
                            animation = tween(3800, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart,
                        ),
                        label = ""
                    )
                    if (index.value.roundToInt() <= orangeItemGif.count) { // 做一个间歇的效果
                        Image(
                            modifier = Modifier
                                .size(itemSize.frameSize)
                                .scale(1.8f)
                                .graphicsLayer {
                                    translationX = padding2.toPx()
                                    translationY = -padding2.toPx()
                                }, painter = painterResource(
                                getImageResourceDrawable(
                                    "${orangeItemGif.gif}${index.value.roundToInt()}"
                                )
                            ), contentDescription = null
                        )
                    }
                }
                MovableImage(
                    modifier = Modifier.size(itemSize.itemSize),
                    imageResource = if (hide) R.drawable.common_question else getImageResourceDrawable(
                        race.getHeadIcon()
                    ),
                    clipSize = itemSize.itemSize / 12
                )

                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                if (showStar) {
                    Box(
                        Modifier
                            .size(itemSize.itemSize / 2.4f)
                            .align(Alignment.BottomStart)
                            .graphicsLayer {
                                translationX = -(itemSize.itemSize / 6).toPx()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = ally.getQualityFrame()),
                            contentDescription = null
                        )
                        Text(
                            text = ally.star.toString(),
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
                if (showRed && ally.new) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            if (showNum) {
                Box(contentAlignment = Alignment.CenterStart) {
                    CommonBar(
                        modifier = Modifier.size(itemSize.frameSize, expBarHeight),
                        currentValue = ally.num,
                        maxValue = ally.starUpNum,
                        fullRes = R.drawable.common_card_line,
                        emptyRes = R.drawable.common_card_empty,
                        textColor = Color.White,
                        style = MaterialTheme.typography.body1
                    )
                    if (ally.num >= ally.starUpNum && ally.star < ally.starLimit) {
                        Image(
                            modifier = Modifier
                                .height(expBarHeight)
                                .graphicsLayer {
                                    translationX = -itemSize.itemSize.toPx() / 7
                                }
                                .scale(1.3f),
                            contentScale = ContentScale.FillHeight,
                            painter = painterResource(R.drawable.hero_starup),
                            contentDescription = null
                        )
                    }
                }
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim { BattleManager.getRoleByAlly(ally = ally) }
                }
            }
            if (showName) {
                Text(
                    text = if (hide) "???" else race.name,
                    style = itemSize.getTextStyle(),
                    maxLines = 2,
                    minLines = 2,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}