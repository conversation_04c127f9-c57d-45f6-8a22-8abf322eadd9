package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.language.LanguageManager.LanguageSelectorView
import com.moyu.chuanqirensheng.ui.theme.padding4


@Composable
fun TopItemsRight() {
    Column(
        verticalArrangement = Arrangement.Center,
    ) {
        Spacer(modifier = Modifier.padding(padding4))
        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
            LanguageSelectorView()
        }
    }
}