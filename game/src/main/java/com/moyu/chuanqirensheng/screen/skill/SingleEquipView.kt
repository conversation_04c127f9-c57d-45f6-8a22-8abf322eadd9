package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.MaskView
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Equipment


@Composable
fun SingleEquipView(
    equipment: Equipment,
    showName: Boolean = true,
    extraInfo: String = "",
    textColor: Color = Color.White,
    extraFrame: Int? = null,
    frameZIndex: Float = 0f,
    textInFrame: Boolean = false,
    showRed: Boolean = true,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Equipment) -> Unit = { Dialogs.equipDetailDialog.value = it }
) {
    EffectButton(modifier = Modifier, onClick = {
        selectCallBack(equipment)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize).zIndex(frameZIndex),
                    painter = painterResource(extraFrame?: equipment.quality.getQualityFrame()),
                    contentDescription = null,
                )
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 10)),
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Crop,
                    painter = painterResource(id = getImageResourceDrawable(equipment.pic)),
                    contentDescription = null
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                if (textInFrame) {
                    if (showName) {
                        MaskView(
                            modifier = Modifier.align(Alignment.BottomCenter),
                            text = equipment.name,
                            itemSize = itemSize
                        )
                    }
                }
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = itemSize.itemSize / 10),
                    equipment.star,
                    starWidth = itemSize.frameSize / 5
                )
                if (showRed && equipment.new) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            if (showName && !textInFrame) {
                Spacer(modifier = Modifier.size(padding2))
                Text(
                    text = equipment.name,
                    style = itemSize.getTextStyle(),
                    maxLines = 2,
                    minLines = 2,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}