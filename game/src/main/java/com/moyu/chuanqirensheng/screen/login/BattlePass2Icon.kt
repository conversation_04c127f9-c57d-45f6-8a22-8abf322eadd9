package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS2_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_BATTLE_PASS2
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun BattlePass2Icon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_BATTLE_PASS2)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Box(modifier = modifier.graphicsLayer {
            translationX = LabelSize.Large.width.toPx() / 2.4f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                text = stringResource(R.string.battle_pass2),
                icon = R.drawable.item_battlepass2
            ) {
                goto(BATTLE_PASS2_SCREEN)
            }
            val red = BattlePass2Manager.hasRed() || QuestManager.warPass2Tasks.any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
            if (red) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(start = padding6, top = padding2)
                        .size(imageTinyPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}
