package com.moyu.chuanqirensheng.screen.effect

data class GifData(
    val gif: String,
    val count: Int,
    val pace: Int = 1
)


val singleDrawGif = GifData(
    "cardeat_", 7
)

val awardGif = GifData(
    "reward_", 16
)

val equipGif = GifData(
    "equipon_", 8
)

val orangeItemGif = GifData(
    "rewarditem_", 35
)

val heroGif = GifData(
    "heroshape_", 11
)

val turnGif = GifData(
    "next_", 8, 3
)

val vipGif = GifData(
    "special_", 16
)

val payGif = GifData(
    "pay_", 7
)

val drawGif = GifData(
    "draw_", 63
)

val packageGif = GifData(
    "package_", 9
)