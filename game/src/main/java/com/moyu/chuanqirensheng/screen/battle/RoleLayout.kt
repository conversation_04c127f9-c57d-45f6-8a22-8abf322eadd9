package com.moyu.chuanqirensheng.screen.battle

import android.annotation.SuppressLint
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.common.HpBar
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.role.SingleRoleView
import com.moyu.chuanqirensheng.ui.theme.animateSmall
import com.moyu.chuanqirensheng.ui.theme.shakeDp
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.role.Role


@Composable
fun RoleHpWithAnim(roleGetter: () -> Role) {
    val role = roleGetter()
    val hp by animateIntAsState(
        targetValue = role.getCurrentProperty().hp, animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt()
        ), label = ""
    )
    val totalShield by animateIntAsState(
        targetValue = role.totalShield(), animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt()
        ), label = ""
    )
    HpBar(
        currentHp = if (role.hasState(ActionStateType.Normal)) role.getCurrentProperty().hp else hp,
        maxHp = role.getDefaultProperty().hp,
        totalShield = totalShield,
    )
}

@SuppressLint("UnusedTransitionTargetStateParameter")
@Composable
fun SingleRoleInBattleLayout(modifier: Modifier = Modifier, textColor: Color = Color.White, roleGetter: () -> Role) {
    val role = roleGetter()
    val shake = role.hasState(ActionStateType.BeingAttack)
    val jumpY by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt() / 2
        ), targetValue = when {
            role.hasState(ActionStateType.DoAttack) -> 0.dp
            role.hasState(ActionStateType.Jump) -> -animateSmall
            role.hasState(ActionStateType.BeingHeal) -> -animateSmall
            else -> 0.dp
        }, label = ""
    )
    val attackY by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt() / 2
        ), targetValue = when {
            role.hasState(ActionStateType.DoAttack) -> {
                val state = role.getStateList().first { it.isState(ActionStateType.DoAttack) }
                val targets = state.targets
                if (targets.size == 1 && state.skill?.isMeleeAttack() == true && role.getRace().isMelee()) {
                    (if (role.isPlayerSide()) {
                        singleRoleHeight * 0.5f
                    } else {
                        - singleRoleHeight * 0.5f
                    }) + (BattleManager.battleRolePositions[targets[0]]?.second?: 0.dp) - (BattleManager.battleRolePositions[role.playerId()]?.second?: 0.dp)
                } else {
                    -animateSmall
                }
            }
            else -> 0.dp
        }, label = ""
    )
    val attackX by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt() / 2
        ), targetValue = when {
            role.hasState(ActionStateType.DoAttack) -> {
                val state = role.getStateList().first { it.isState(ActionStateType.DoAttack) }
                val targets = state.targets
                if (targets.size == 1 && state.skill?.isMeleeAttack() == true && role.getRace().isMelee()) {
                    (BattleManager.battleRolePositions[targets[0]]?.first?: 0.dp) - (BattleManager.battleRolePositions[role.playerId()]?.first?: 0.dp)
                } else {
                    -animateSmall
                }
            }
            else -> 0.dp
        }, label = ""
    )
    val transition = updateTransition(targetState = shake, label = "shake")
    val shakeOffset by transition.animateDp(
        transitionSpec = {
            keyframes {
                durationMillis = GameSpeedManager.animDuration().toInt()
                -shakeDp at (GameSpeedManager.animDuration() / 8).toInt() using FastOutSlowInEasing
                0.dp at (GameSpeedManager.animDuration() / 4).toInt() using FastOutSlowInEasing
                shakeDp at (GameSpeedManager.animDuration() * 3 / 8).toInt() using FastOutSlowInEasing
                0.dp at (GameSpeedManager.animDuration().toInt() / 2) using FastOutSlowInEasing
                -shakeDp at (GameSpeedManager.animDuration() * 5 / 8).toInt() using FastOutSlowInEasing
                shakeDp at (GameSpeedManager.animDuration() * 3 / 4).toInt() using FastOutSlowInEasing
                0.dp at (GameSpeedManager.animDuration() * 7 / 8).toInt() using FastOutSlowInEasing
            }
        }, label = "shakeOffset"
    ) {
        0.dp
    }
    Column(
        modifier = modifier.graphicsLayer {
            val offsetX = (if (shake) shakeOffset.value.dp.toPx() else 0f) + attackX.value.dp.toPx()
            val offsetY = if (role.isPlayerSide()) {
                jumpY.value.dp.toPx() * 9 + attackY.value.dp.toPx()
            } else {
                -jumpY.value.dp.toPx() * 9 + attackY.value.dp.toPx()
            }
            if (offsetX.isNaN().not()) {
                translationX = offsetX
            }
            if (offsetY.isNaN().not()) {
                translationY = offsetY
            }

            val rotateY = if (shake) shakeOffset.value.dp.toPx() / 3 else 0f
            val rotateZ = if (shake) shakeOffset.value.dp.toPx() / 9 else 0f
            val rotateX = if (role.isPlayerSide()) -jumpY.value.dp.toPx() / 3 else jumpY.value.dp.toPx() / 3
            if (rotateY.isNaN().not()) {
                rotationY = rotateY
            }
            if (rotateZ.isNaN().not()) {
                rotationZ = rotateZ
            }
            if (rotateX.isNaN().not()) {
                rotationX = rotateX
            }
        }, horizontalAlignment = Alignment.CenterHorizontally
    ) {
        SingleRoleView(role, itemSize = ItemSize.LargePlus)
    }
}