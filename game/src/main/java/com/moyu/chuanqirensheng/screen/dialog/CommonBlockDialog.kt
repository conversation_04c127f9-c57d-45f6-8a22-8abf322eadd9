package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.ui.theme.padding22

@Composable
fun CommonBlockDialog(content: MutableState<String?>) {
    if (content.value != null) {
        PanelDialog(
            onDismissRequest = { },
            showClose = false
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding22)
                    .verticalScroll(rememberScrollState())
            ) {
                Spacer(modifier = Modifier.size(padding22))
                Text(
                    text = content.value ?: "",
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
            }
        }
    }
}
