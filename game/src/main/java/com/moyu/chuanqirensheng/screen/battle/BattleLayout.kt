package com.moyu.chuanqirensheng.screen.battle

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.forceLose
import com.moyu.chuanqirensheng.logic.event.isNeedGod
import com.moyu.chuanqirensheng.logic.event.isNeedMaster
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding54
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally
import com.moyu.core.model.Event

@Composable
fun BattleLayout(
    event: Event,
    capacity: Int = 8,
    godAllyUuid: String? = null,
    allyFilter: (Ally) -> Boolean = { true },
    extraLayout: @Composable ColumnScope.() -> Unit = {}
) {
    Box(modifier = Modifier.fillMaxSize()) {
        EventManager.getOrCreateHandler(event).apply {
            if (!eventFinished.value) {
                if (repo.inBattle.value) {
                    BattleFieldLayout(repo.battleRoles, extraLayout = extraLayout)
                } else {
                    PrepareBattleLayout(
                        event = event,
                        allyFilter = allyFilter,
                        capacity = capacity,
                        extraLayout = extraLayout,
                    ) {
                        if (Dialogs.gameWinDialog.value.isNotEmpty()) {
                            // https://xkff20230903033446466.pingcode.com/pjm/workitems/vPBbVj-8?
                            //#YXW-1124 局内事件可以跳过一天，复现步骤    战斗事件结束时，快速点击+号栏位，会弹出兵种上阵界面，吧兵种全部下阵，点击确认，失败后会跳过一天，本来事件结束是第70天，再选择一个事件后变成72天，具体看下视频
                            false
                        } else if (event.forceLose()) {
                            EventManager.doEventBattleResult(event, false)
                            true
                        } else if (BattleManager.getBattleAllies().values.filter { !it.isDead() }
                                .none(allyFilter)) {
                            Dialogs.alertDialog.value =
                                CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                    onConfirm = {
                                        EventManager.doEventResult(event = event, result = false)
                                    })
                            true
                        } else if (event.isNeedMaster() && !BattleManager.getGameMaster()
                                .isDead() && BattleManager.getBattleAllies().values.none { it.isMaster() }
                        ) {
                            GameApp.instance.getWrapString(
                                R.string.master_need_tips,
                            ).toast()
                            false
                        } else if (godAllyUuid != null && !BattleManager.getBattleAllies().values.any { it.uuid == godAllyUuid }) {
                            GameApp.instance.getWrapString(
                                R.string.god_need_tips,
                            ).toast()
                            false
                        } else if (capacity == 1 && BattleManager.allyGameData.none { it.battlePosition == ALLY_ROW1_SECOND }) {
                            GameApp.instance.getWrapString(R.string.single_ally_need_tips).toast()
                            false
                        } else if (positionListEnemy.none { repo.battleRoles[it]?.isOver() == false }) {
                            GameApp.instance.getWrapString(R.string.pvp_ally_error).toast()
                            false
                        } else {
                            val enemies = repo.battleRoles.values.mapNotNull { it }
                                .filter { !it.isPlayerSide() }
                            if (enemies.isNotEmpty() && !enemies.all { it.isOver() }) {
                                val battleAllies = BattleManager.getBattleRoles(capacity)
                                repo.setCurrentAllies(battleAllies)
                                repo.startBattle()
                            }
                            true
                        }
                    }
                }
            }
        }
        SettingColumn(
            Modifier.align(Alignment.CenterStart).graphicsLayer {
                translationY = padding16.toPx()
            }, settingBattleItems
        )
    }
}

@Composable
fun PrepareBattleLayout(
    event: Event,
    allyFilter: (Ally) -> Boolean = { true },
    capacity: Int,
    extraLayout: @Composable ColumnScope.() -> Unit = {},
    start: () -> Boolean,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly
    ) {
        // todo 预留一个事件title的距离
        Spacer(modifier = Modifier.size(padding54))
        EnemiesRow(repo.battleRoles)
        Spacer(modifier = Modifier.size(gapLarge))
        Column(
            Modifier
                .align(Alignment.End)
                .graphicsLayer {
                    translationX = gapSmallPlus.toPx()
                }) {
            extraLayout()
        }
        GameButton(
            text = stringResource(R.string.start_battle),
            buttonSize = ButtonSize.Big
        ) {
            start()
        }
        Spacer(modifier = Modifier.size(gapLarge))
        if (GuideManager.showGuide.value) {
            Spacer(modifier = Modifier.fillMaxSize())
        } else {
            AllyCardsRow(modifier = Modifier
                .fillMaxWidth(),
                allies = BattleManager.getBattleAllies(),
                capacity = capacity,
                showName = true,
                showHp = true,
                allyClick = {
                    BattleManager.selectAllyToBattle(it, -1)
                }) {
                Dialogs.selectAllyToBattleDialog.value = SelectAllyData(
                    capacity = capacity, filter = allyFilter,
                    needMaster = event.isNeedMaster(), needGod = event.isNeedGod()
                ) {
                    start()
                }
            }
        }
    }
}