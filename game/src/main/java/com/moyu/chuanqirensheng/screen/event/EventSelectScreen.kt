package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.EventDebugButton
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isSelectGroup
import com.moyu.chuanqirensheng.logic.skill.getSkillFrame
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.turnGif
import com.moyu.chuanqirensheng.screen.role.MasterInfoView
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.addAnimationVertical
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect

@Composable
fun EventSelectScreen() {
    if (EventManager.selectionEvents.firstOrNull()?.isSelectGroup() != true) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
            painter = painterResource(
                id = getImageResourceDrawable(
                    EventManager.selectionEvents.firstOrNull()?.bgPic ?: "environment_7"
                )
            ),
            contentDescription = null
        )
    }
    LaunchedEffect(Unit) {
        GameCore.instance.onBattleEffect(SoundEffect.SelectEvent)
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(B65)
    ) { // 左侧空出一些，避开setting
        Box {
            MasterInfoView(
                Modifier
                    .fillMaxWidth()
                    .height(eventTopLayoutHeight)
            )
            EventDebugButton(
                Modifier
                    .align(Alignment.BottomEnd)
                    .padding(top = padding36)
            )
        }

        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(contentAlignment = Alignment.Center) {
                TextLabel2(text = EventManager.getEventSelectTitle(), labelSize = LabelSize.Huge)
                GifView(modifier = Modifier.scale(1.5f), enabled = true, gifCount = turnGif.count, gifDrawable = turnGif.gif, pace = turnGif.pace)
            }
            EffectedSkillsRow(Modifier)
        }
        EventSelectPage(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        )
    }
}

@Composable
fun EffectedSkillsRow(modifier: Modifier) {
    if (BattleManager.effectedSkills.isNotEmpty()) {
        EffectButton(modifier = modifier, onClick = {
            Dialogs.gameMasterDialog.intValue = 2
        }) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(-padding8)
                ) {
                    BattleManager.effectedSkills.takeLast(5).forEach {
                        IconView(
                            res = getImageResourceDrawable(it.icon),
                            frame = getSkillFrame(),
                            itemSize = ItemSize.Small,
                        ) {
                            Dialogs.gameMasterDialog.intValue = 2
                        }
                    }
                }
                AnimatedContent(
                    targetState = BattleManager.effectedSkills.size,
                    transitionSpec = {
                        addAnimationVertical(duration = 600).using(
                            SizeTransform(clip = true)
                        )
                    },
                    label = ""
                ) { target ->
                    Text(
                        text = stringResource(
                            R.string.effected_skill_num,
                            target
                        ),
                        style = MaterialTheme.typography.h5
                    )
                }
            }
        }
    }
}
