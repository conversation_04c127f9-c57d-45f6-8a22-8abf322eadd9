package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun ErrorOrderIcon(modifier: Modifier = Modifier) {
    if (BillingManager.payClientDataList.isNotEmpty()) {
        Box(modifier = modifier.graphicsLayer {
            translationX = LabelSize.Large.width.toPx() / 2.4f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                text = stringResource(R.string.bill_order),
                icon = R.drawable.icon_billing_order
            ) {
                Dialogs.errorOrderDialog.value = true
            }
            Image(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(end = padding6, top = padding2)
                    .size(imageTinyPlus),
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}
