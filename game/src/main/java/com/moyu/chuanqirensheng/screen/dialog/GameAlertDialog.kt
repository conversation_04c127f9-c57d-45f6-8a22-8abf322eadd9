package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16


data class CommonAlert(
    val title: String = GameApp.instance.getWrapString(R.string.please_confirm),
    val content: String = "",
    val cancelText: String = GameApp.instance.getWrapString(R.string.cancel),
    val confirmText: String = GameApp.instance.getWrapString(R.string.confirm),
    val onlyConfirm: Boolean = false,
    val extraContent: @Composable ColumnScope.() -> Unit = {},
    val onConfirm: () -> Unit = {},
    val onClose: () -> Unit = {},
    val onCancel: () -> Unit = {},
)

@Composable
fun CommonAlertDialog(switch: MutableState<CommonAlert?>) {
    switch.value?.let { alert ->
        PanelDialog(onDismissRequest = {
            alert.onClose()
            switch.value = null
        }, contentBelow = {
            if (!alert.onlyConfirm) {
                GameButton(text = alert.cancelText, buttonStyle = ButtonStyle.Blue) {
                    alert.onCancel()
                    switch.value = null
                }
            }
            GameButton(text = alert.confirmText, buttonStyle = ButtonStyle.Orange) {
                alert.onConfirm()
                switch.value = null
            }
        }) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth().padding(horizontal = padding16),
            ) {
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    text = alert.title,
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        modifier = Modifier.verticalScroll(
                                rememberScrollState()
                            ),
                        text = alert.content,
                        style = MaterialTheme.typography.h2,
                        color = Color.Black
                    )
                    alert.extraContent.invoke(this)
                }
            }
        }
    }
}