package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.padding150

@Composable
fun CommonLine() {
    Image(
        modifier = Modifier.width(padding150),
        contentScale = ContentScale.FillWidth,
        painter = painterResource(id = R.drawable.common_line),
        contentDescription = null
    )
}