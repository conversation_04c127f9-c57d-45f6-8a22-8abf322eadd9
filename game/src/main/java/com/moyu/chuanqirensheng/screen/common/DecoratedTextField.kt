package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.hideKeyboard

@Composable
fun DecorateTextField(modifier: Modifier, text: String, hintText: String = "", onValueChange: (String) -> Unit) {
    Box(
        modifier
            .fillMaxWidth()
            ,
        contentAlignment = Alignment.Center
    ) {
        BasicTextField(
            value = text,
            onValueChange = onValueChange,
            keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(onDone = {
                hideKeyboard(GameApp.instance.activity)
            }),
            textStyle = MaterialTheme.typography.h4.copy(color = Color.Black),
            cursorBrush = SolidColor(Color.Black),
            decorationBox = { innerTextField ->//decorationBox内部负责编写输入框样式
                Box {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(id = R.drawable.common_input),
                        contentDescription = null
                    )
                    Box(
                        modifier = Modifier.padding(
                            vertical = padding10,
                            horizontal = padding16
                        )
                    ) {
                        innerTextField()
                    }
                }
            }
        )
        // Add the hint text as an overlay
        if (text.isEmpty()) {
            Text(
                text = hintText,
                color = Color.DarkGray, // You can customize the color
                modifier = Modifier.padding(horizontal = padding4), // Adjust the padding as needed
                style = MaterialTheme.typography.h5
            )
        }
    }
}