package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuidePointAndText
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.award.toConditionAward
import com.moyu.chuanqirensheng.logic.event.EventConditionLayout
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.battleDirectAwardIds
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.SingleAwardItem
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.daoZeiZhiJiaPoolIds
import com.moyu.core.model.feiQiKuangJingPoolIds
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val yongShiZhiMuEventIds = listOf(1003778,1003779,1003780,1003781,1003782,1003783,1003784,1003785,1003786,1003787,1003788,1003789,1003790,1003791,1003792,1003793,1003794,1003795,1003796,1003797,1003798,1003799,1003800,1003801,1003802,1003803,1003804,1003805,1003806,1003807,1003808)

@Composable
fun EventDetailDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val award = remember {
            event.toAward(true)
        }
        PanelDialog(onDismissRequest = {
            if (GuideManager.guideIndex.intValue == 5) {
                doSelect(event, show, true)
            } else {
                show.value = null
            }
        }, contentBelow = {
            if (GuideManager.guideIndex.intValue != 5) {
                GameButton(text = stringResource(id = R.string.cancel),
                    onClick = {
                        if (show.value != null) {
                            show.value = null
                        }
                    })
            }
            Box(contentAlignment = Alignment.Center) {
                val enabled = triggerEvent(event, false)
                GameButton(
                    text = stringResource(id = R.string.do_select),
                    enabled = enabled,
                    buttonStyle = ButtonStyle.Orange,
                    onClick = {
                        doSelect(event, show, enabled)
                    })
            }
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = event.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.size(padding8))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = event.startText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                if (event.winReward.first() != 0 && event.winReward.first() !in feiQiKuangJingPoolIds && event.winReward.first() !in daoZeiZhiJiaPoolIds) {
                    Spacer(modifier = Modifier.size(padding6))
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.next_turn_award),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    FlowRow(
                        Modifier
                            .fillMaxWidth()
                            .align(Alignment.CenterHorizontally),
                        overflow = FlowRowOverflow.Visible,) {
                        AwardList(
                            modifier = Modifier,
                            award = award,
                            param = defaultParam.copy(textColor = Color.Black),
                            mainAxisAlignment = spacedBy(padding30),
                        )
                        Box(Modifier.padding(vertical = padding10)) {
                            // todo 就简单处理了，勇士之墓显示随机装备
                            if (event.id in yongShiZhiMuEventIds) {
                                SingleAwardItem(
                                    name = stringResource(R.string.random_equip),
                                    drawable = R.drawable.random_equip,
                                    num = "",
                                    alignment = Alignment.TopCenter,
                                    contentScale = ContentScale.Crop,
                                    param = defaultParam.copy(textColor = Color.Black)
                                        .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                            GameApp.instance.getWrapString(R.string.random_equip)
                                                .toast()
                                        }),
                                )
                            }
                            if (event.playPara2.firstOrNull() != 0.0 && event.play !in battleDirectAwardIds) {
                                // 显示随机奖励图标，特别啰嗦，没办法
                                Spacer(modifier = Modifier.width(padding45))
                                val randomAward = event.playPara2.first().let {
                                    if (it.toInt() == 0) {
                                        Award()
                                    } else {
                                        repo.gameCore.getPoolById(it.toInt()).toAward()
                                    }
                                }
                                if (randomAward.resources.any { it > 0 }) {
                                    SingleAwardItem(
                                        name = stringResource(R.string.random_res),
                                        drawable = R.drawable.random_resource,
                                        num = "",
                                        alignment = Alignment.TopCenter,
                                        contentScale = ContentScale.Crop,
                                        param = defaultParam.copy(textColor = Color.Black)
                                            .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                                GameApp.instance.getWrapString(R.string.random_res)
                                                    .toast()
                                            }),
                                    )
                                } else if (randomAward.equips.isNotEmpty()) {
                                    SingleAwardItem(
                                        name = stringResource(R.string.random_equip),
                                        drawable = R.drawable.random_equip,
                                        num = "",
                                        alignment = Alignment.TopCenter,
                                        contentScale = ContentScale.Crop,
                                        param = defaultParam.copy(textColor = Color.Black)
                                            .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                                GameApp.instance.getWrapString(R.string.random_equip)
                                                    .toast()
                                            }),
                                    )
                                } else if (randomAward.skills.isNotEmpty() && randomAward.skills.first()
                                        .isMagic()
                                ) {
                                    SingleAwardItem(
                                        name = stringResource(R.string.random_magic),
                                        drawable = R.drawable.random_magic,
                                        num = "",
                                        alignment = Alignment.TopCenter,
                                        contentScale = ContentScale.Crop,
                                        param = defaultParam.copy(textColor = Color.Black)
                                            .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                                GameApp.instance.getWrapString(R.string.random_magic)
                                                    .toast()
                                            }),
                                    )
                                } else if (randomAward.skills.isNotEmpty() && randomAward.skills.first()
                                        .isAdventure()
                                ) {
                                    SingleAwardItem(
                                        name = stringResource(R.string.random_skill_adv),
                                        drawable = R.drawable.random_skill,
                                        num = "",
                                        alignment = Alignment.TopCenter,
                                        contentScale = ContentScale.Crop,
                                        param = defaultParam.copy(textColor = Color.Black)
                                            .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                                GameApp.instance.getWrapString(R.string.random_skill_adv)
                                                    .toast()
                                            }),
                                    )
                                } else if (randomAward.allies.isNotEmpty()) {
                                    SingleAwardItem(
                                        name = stringResource(R.string.random_ally),
                                        drawable = R.drawable.random_ally_icon,
                                        num = "",
                                        alignment = Alignment.TopCenter,
                                        contentScale = ContentScale.Crop,
                                        param = defaultParam.copy(textColor = Color.Black)
                                            .copy(frameDrawable = 1.getQualityFrame(), callback = {
                                                GameApp.instance.getWrapString(R.string.random_ally)
                                                    .toast()
                                            }),
                                    )
                                }
                            }
                        }
                    }

                }
                if (event.condition != 0) {
                    Spacer(modifier = Modifier.size(padding10))
                    val text = if (event.toConditionAward().titleLevel > 0) stringResource(id = R.string.event_condition2) else stringResource(id = R.string.event_condition)
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = text,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    EventConditionLayout(
                        modifier = Modifier.align(Alignment.Start),
                        event = event, itemSize = ItemSize.Small, textColor = Color.Black,
                    )
                }
                if (GuideManager.guideIndex.intValue == 5) {
                    Spacer(modifier = Modifier.weight(1f))
                    Box(Modifier.graphicsLayer {
                        translationY = padding300.toPx()
                    }) {
                        GuidePointAndText(
                            text = stringResource(R.string.guide6),
                            handType = HandType.RIGHT_HAND,
                            offsetX = -padding10,
                        )
                    }
                }
            }
        }
    }
}

fun doSelect(event: Event, show: MutableState<Event?>, enabled: Boolean) {
    if (show.value != null) {
        if (enabled) {
            show.value = null
        }
        GameApp.globalScope.launch(Dispatchers.Main) {
            EventManager.selectEvent(event)
            if (GuideManager.guideIndex.intValue == 5) {
                GuideManager.guideIndex.intValue = 6
            } else {
                if (GuideManager.guideIndex.intValue == BATTLE_GUIDE_START) {
                    if (event.isBattle()) {
                        GuideManager.showGuide.value = true
                    }
                }
            }
        }
    }
}

