package com.moyu.chuanqirensheng.screen.setting

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.login.MenuButton
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize

val settingRowItems = mutableStateListOf(
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.hero) },
        icon = { R.drawable.icon_status },
        redIcon = { BattleManager.equipGameData.any { it.new } },
        action = { Dialogs.gameMasterDialog.intValue = 0 },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.skills) },
        icon = { R.drawable.icon_skills },
        redIcon = { BattleManager.skillGameData.filter { it.isSkillSheet(1)
                || it.isSkillSheet(2)
                || it.isSkillSheet(3)
                || it.isSkillSheet(4)}.any { it.new } },
        action = { Dialogs.gameSkillDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.allies) },
        icon = { R.drawable.icon_hero2 },
        redIcon = { BattleManager.getGameAlliesNoMaster().any { it.new } },
        upgradeIcon = { BattleManager.getGameAlliesNoMaster().any { ally ->
            ally.num >= ally.starUpNum && ally.star < ally.starLimit
        } },
        action = { Dialogs.gameAllyDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.setting) },
        icon = { R.drawable.icon_setting },
        action = { Dialogs.settingDialog.value = true },
    ),
)

@Composable
fun SettingRow(modifier: Modifier, settingItems: List<SettingItem>) {
    Row(
        modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        settingItems.forEach { settingItem ->
            MenuButton(
                modifier = Modifier.size(bottomItemSize),
                text = settingItem.name(),
                icon = settingItem.icon(),
                unlock = null,
                redIcon = settingItem.redIcon(),
                upgradeIcon = settingItem.upgradeIcon(),
            ) {
                settingItem.action()
            }
        }
    }
}