package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 套在组件外面，实现点击放大的效果。
 *  @param  maxScale  扩展的最大大小,默认1.2f
 */
@Composable
fun ClickExpandLayout(
    modifier: Modifier = Modifier,
    maxScale: Float = 1.2f,
    content: @Composable BoxScope.() -> Unit
) {
    var isClick by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(targetValue = if (isClick) maxScale else 1f, label = "")
    val coroutineScope = rememberCoroutineScope()
    Box(
        modifier = modifier
            .scale(scale)
            .clickable {
                isClick = true
                coroutineScope.launch {
                    delay(200 )
                    isClick = false
                }
            },
        contentAlignment = Alignment.Center
    ) {
        content(this)
    }
}