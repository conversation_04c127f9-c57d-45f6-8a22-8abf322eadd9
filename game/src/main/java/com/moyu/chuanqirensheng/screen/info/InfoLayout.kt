package com.moyu.chuanqirensheng.screen.info

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.skill.getSkillFrame
import com.moyu.chuanqirensheng.screen.battle.GameDamageDetailAlertDialog
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType

/**
 * 战斗信息面板
 */
@Composable
fun InfoLayout(modifier: Modifier = Modifier, info: SnapshotStateList<BattleInfo>) {
    val alertDialog = remember { mutableStateOf(false) }
    val battleInfoState = remember { mutableStateOf(BattleInfo("")) }
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = padding10, vertical = padding4),
        contentAlignment = Alignment.TopStart
    ) {
        val listState = rememberLazyListState()
        // Remember a CoroutineScope to be able to launch
        LazyColumn(state = listState, content = {
            items(info.size) { index ->
                info.getOrNull(index)?.let {
                    when (it.type) {
                        BattleInfoType.Damage -> DamageInfoLayout(
                            it, alertDialog, battleInfoState
                        )

                        BattleInfoType.ExtraSkill -> ExtraSkillInfoLayout(it)
                        else -> Text(
                            text = it.content,
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                    }
                }
            }
        })
        LaunchedEffect(info.size) {
            if (info.isNotEmpty()) {
                listState.animateScrollToItem(index = info.size - 1)
            }
        }
    }
    GameDamageDetailAlertDialog(alertDialog, battleInfoState.value)
}

@Composable
fun DamageInfoLayout(
    info: BattleInfo,
    alertDialog: MutableState<Boolean>,
    battleInfoState: MutableState<BattleInfo>
) {
    EffectButton(onClick = {
        alertDialog.value = true
        battleInfoState.value = info
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = info.content,
                style = MaterialTheme.typography.h4.copy(textDecoration = TextDecoration.Underline),
                color = Color.Black
            )
        }
    }
}


@Composable
fun ExtraSkillInfoLayout(
    info: BattleInfo,
) {
    EffectButton(onClick = {
        Dialogs.skillDetailDialog.value = info.skill
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            info.skill?.let {
                IconView(
                    res = getImageResourceDrawable(it.icon),
                    frame = getSkillFrame(),
                    itemSize = ItemSize.Small,
                ) {
                    Dialogs.skillDetailDialog.value = it
                }
            }
            Spacer(modifier = Modifier.size(padding4))
            Text(
                text = info.content,
                style = MaterialTheme.typography.h4,
                color = Color.Black
            )
        }
    }
}