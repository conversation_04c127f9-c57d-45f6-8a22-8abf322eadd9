package com.moyu.chuanqirensheng.screen.tutor

import android.webkit.WebView
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding6


val advancedTutorListTabItems = mutableStateOf(
    listOf(
        "https://www.taptap.cn/moment/533623646862705921?share_id=876a0dc2c9a7&utm_medium=share&utm_source=copylink",
        "https://www.taptap.cn/moment/534053276866314919?share_id=c2d3b6b83186&utm_medium=share&utm_source=copylink",
        "https://www.taptap.cn/moment/534504333736152474?share_id=8bb9f0407874&utm_medium=share&utm_source=copylink",
    )
)

@Composable
fun AdvancedTutorScreen() {
    val pagerState = rememberPagerState {
        advancedTutorListTabItems.value.size
    }
    GameBackground(title = stringResource(R.string.advanced_tutor)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
                userScrollEnabled = false,
            ) { page ->
                val url = advancedTutorListTabItems.value.getOrNull(page)
                if (url != null) {
                    WebViewLoader(url = url)
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding6.toPx()
                },
                pagerState,
                List(advancedTutorListTabItems.value.size) { index ->
                    "${
                        GameApp.instance.getWrapString(R.string.advanced_tutor)
                    }${index + 1}"
                })
        }
    }
}

@Composable
fun WebViewLoader(url: String) {
    val webView = remember { WebView(GameApp.instance.applicationContext) }

    LaunchedEffect(url) {
        webView.loadUrl(url)
    }

    AndroidView({ webView }) { view ->
        // No need to do anything here
    }
}