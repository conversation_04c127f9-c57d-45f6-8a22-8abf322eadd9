package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.EventIdTag
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.logic.event.isBuilding
import com.moyu.chuanqirensheng.logic.event.isSelectGroup
import com.moyu.chuanqirensheng.logic.getAwardDesc
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.effect.MovableImage
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Event
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun SingleEventCard(cardSize: CardSize, event: Event, index: Int) {
    Row(
        Modifier
            .fillMaxWidth()
            .graphicsLayer {
                // todo UI细调整
                translationX = -padding12.toPx()
            }) {
        if (event.isSelectGroup() || event.isBuilding()) {
            InnerCard(cardSize = CardSize.Medium, event = event)
            EventDesc(
                Modifier
                    .weight(1f)
                    .height(cardSize.height), event
            )
        } else {
            val visible = remember(event.id) {
                mutableStateOf(false)
            }
            val random1 = remember {
                mutableStateOf(1)
            }
            val random2 = remember {
                mutableStateOf(1)
            }
            LaunchedEffect(event.id) {
                visible.value = false
                random1.value = RANDOM.nextIntClosure(2, 10)
                random2.value = RANDOM.nextIntClosure(1, 10)
                delay(RANDOM.nextIntClosure(100, 700).toLong())
                visible.value = true
            }
            LaunchedEffect(Unit) {
                if (index == 0) {
                    MusicManager.playSound(SoundEffect.Step)
                }
            }
            Spacer(modifier = Modifier.weight(random1.value.toFloat()))
            AnimatedVisibility(
                visible = visible.value,
                enter = fadeIn(animationSpec = TweenSpec(durationMillis = 2000)),
                exit = fadeOut(),
            ) {
                InnerCard(cardSize = CardSize.Small, event = event)
            }
            Spacer(modifier = Modifier.weight(random2.value.toFloat()))
        }
    }
}

@Composable
fun InnerCard(cardSize: CardSize, event: Event) {
    EffectButton(Modifier.size(cardSize.width, cardSize.height), onClick = {
        Dialogs.eventDetailDialog.value = event
    }) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), contentAlignment = Alignment.Center
            ) {
                if (event.isBattle()) {
                    MovableImage(
                        modifier = Modifier
                            .fillMaxSize(),
                        imageResource = getImageResourceDrawable(event.pic),
                    )
                } else {
                    Image(
                        modifier = Modifier
                            .fillMaxSize(),
                        painter = painterResource(id = getImageResourceDrawable(event.pic)),
                        contentDescription = null
                    )
                }
                if (event.isSelectGroup()) {
                    // 选择阵营UI特殊点
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .graphicsLayer {
                                scaleY = 1.08f
                                scaleX = 1.12f
                            },
                        painter = painterResource(id = R.drawable.common_frame2),
                        contentDescription = null
                    )
                }
            }

            TextLabel2(
                Modifier
                    .graphicsLayer {
                        translationY = if (event.isSelectGroup()) {
                            -padding0.toPx()
                        } else {
                            -padding14.toPx()
                        }
                        translationX = -padding1.toPx()
                    }
                    .scale(1.22f),
                text = event.name,
                labelSize = LabelSize.Medium2,
                frame = R.drawable.common_frame3
            )
        }
        EventIdTag(modifier = Modifier.align(Alignment.Center), event, cardSize)
    }
}


@Composable
fun EventDesc(modifier: Modifier, event: Event) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer {
                        scaleY = 1.5f
                        scaleX = 1.1f
                    },
                painter = painterResource(id = R.drawable.common_frame_long),
                contentDescription = null
            )
            Text(
                modifier = Modifier.padding(horizontal = padding4),
                text = if (event.isSelectGroup()) event.startText else event.toAward(true)
                    .getAwardDesc(),
                style = MaterialTheme.typography.h4,
                color = Color.White
            )
        }
        Spacer(modifier = Modifier.size(padding6))
        GameButton(text = stringResource(id = R.string.do_select)) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                if (event.isSelectGroup()) {
                    if (GuideManager.guideIndex.intValue == 2) {
                        GuideManager.guideIndex.intValue = 3
                    }
                    EventManager.selectEvent(event)
                } else {
                    if (GuideManager.guideIndex.intValue == 4) {
                        GuideManager.guideIndex.intValue = 5
                    }
                    Dialogs.eventDetailDialog.value = event
                }
            }
        }
    }
}