package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding226
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding66

enum class LabelSize(val width: Dp, val height: Dp) {
    Small(imageSmallPlus, imageSmall),
    Medium(padding180, padding36),
    Medium2(moneyWidth, moneyHeight),
    Large(padding226, padding48), Huge(padding300, padding66),
}

@Composable
fun LabelSize.getTextStyle(): TextStyle {
    return when (this) {
        LabelSize.Small -> MaterialTheme.typography.h4
        LabelSize.Medium2 -> MaterialTheme.typography.h4
        LabelSize.Medium -> MaterialTheme.typography.h3
        LabelSize.Large -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h1
    }
}

@Composable
fun TextLabel(
    modifier: Modifier = Modifier,
    labelSize: LabelSize = LabelSize.Large,
    text: String,
    extraTextOffset: Dp = padding0,
    flip: Boolean = false,
    icon: Int? = null,
    contentAlignment: Alignment = Alignment.CenterStart,
    forceTextStyle: TextStyle? = null,
    textAlign: TextAlign = TextAlign.Start,
    onClick: (() -> Unit)? = null
) {
    val content: @Composable BoxScope.() -> Unit = {
        Box(
            modifier = modifier
                .size(labelSize.width, labelSize.height)
                .graphicsLayer {
                    this.rotationY = if (flip) 180f else 0f
                },
            contentAlignment = contentAlignment
        ) {
            Image(
                modifier = Modifier.fillMaxSize(),
                painter = painterResource(id = R.drawable.shop_name_frame),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Text(
                modifier = Modifier
                    // todo 有点啰嗦，如果是左对齐，有两种padding，否则无padding
                    .padding(start = if (contentAlignment == Alignment.CenterStart) if (icon != null) labelSize.width / 6 else labelSize.width / 8 else padding0)
                    .graphicsLayer {
                        translationX = extraTextOffset.toPx()
                        translationY = -padding3.toPx()
                        this.rotationY = if (flip) 180f else 0f
                    },
                text = text.replace("\\n", "\n"),
                style = forceTextStyle?: labelSize.getTextStyle(),
                maxLines = 2,
                textAlign = textAlign
            )
            icon?.let {
                Image(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .size(labelSize.height / 1.3f),
                    painter = painterResource(id = it),
                    contentDescription = null
                )
            }
        }
    }
    if (onClick == null) {
        Box(contentAlignment = Alignment.Center, content = content)
    } else {
        EffectButton(onClick = {
            onClick.invoke()
        }, content = content)
    }
}