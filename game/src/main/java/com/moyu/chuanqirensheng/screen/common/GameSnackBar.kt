package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.noticeHeight
import kotlinx.coroutines.delay


@Composable
fun GameSnackBar() {
    if (Dialogs.snackbar.value.isNotEmpty()) {
        LaunchedEffect(Dialogs.snackbar.value) {
            delay(1600)
            Dialogs.snackbar.value = ""
        }
        Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            EffectButton(
                onClick = {
                    Dialogs.snackbar.value = ""
                },
            ) {
                Image(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(noticeHeight),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_tips),
                    contentDescription = null
                )
                Text(Dialogs.snackbar.value, style = MaterialTheme.typography.h3)
            }
        }
    }
}
