package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.SearchView
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.eventFilterList
import com.moyu.chuanqirensheng.screen.setting.SettingRow
import com.moyu.chuanqirensheng.screen.setting.settingRowItems
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.core.model.Event
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventSelectPage(modifier: Modifier) {
    val events = EventManager.getNextEvents()
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Event>>()
    }
    val canSelect =
        DebugManager.allEvent || (events.any { triggerEvent(it, false) } && events.isNotEmpty())

    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding16)
        ) {
            if (DebugManager.allEvent || DebugManager.validEventsAll) {
                val search = remember {
                    mutableStateOf("")
                }
                val list = events.filter {
                    if (search.value.isNotEmpty()) {
                        it.name.contains(search.value) || it.id.toString().contains(search.value)
                    } else true
                }.filter { ally ->
                    filter.all { it.filter.invoke(ally) }
                }
                Row(
                    Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Spacer(modifier = Modifier.size(padding4))
                    SearchView(search)
                    CommonFilterView(
                        Modifier.padding(end = padding10), showFilter
                    )
                }
                LazyVerticalGrid(modifier = Modifier.fillMaxSize(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    columns = GridCells.Fixed(1),
                    content = {
                        items(list.size) {
                            Column {
                                SingleEventCard(
                                    cardSize = CardSize.Medium, event = list[it], index = it
                                )
                                Spacer(modifier = Modifier.size(padding19))
                            }
                        }
                    })
            } else {
                Box {
                    Column(
                        Modifier.fillMaxHeight(), verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        events.forEachIndexed { index, event ->
                            SingleEventCard(
                                cardSize = CardSize.Medium, event = event, index = index
                            )
                        }
                    }
                }
            }
        }
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmallPlus, end = padding10),
            show = showFilter,
            filter = filter,
            filterList = eventFilterList
        )
    }
    if (!canSelect && EventManager.selectedEvent.value?.selectAge != BattleManager.getAge()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(bottomItemSize)
                .padding(bottom = padding4),
            contentAlignment = Alignment.Center
        ) {
            GameButton(
                text = stringResource(id = R.string.ending_life),
                buttonStyle = ButtonStyle.Orange
            ) {
                Dialogs.alertDialog.value = CommonAlert(
                    content = GameApp.instance.getWrapString(R.string.no_event),
                    confirmText = GameApp.instance.getWrapString(R.string.skip_day),
                    onlyConfirm = true,
                    onConfirm = {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            EventManager.gotoNextEvent(null, false)
                        }
                    },
                    onClose = {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            EventManager.gotoNextEvent(null, false)
                        }
                    }
                )
            }
        }
    } else {
        SettingRow(
            Modifier.padding(bottom = padding4), settingRowItems
        )
    }
}