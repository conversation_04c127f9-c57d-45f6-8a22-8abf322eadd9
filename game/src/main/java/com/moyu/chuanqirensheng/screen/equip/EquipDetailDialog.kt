package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Equipment
import com.moyu.core.model.property.Property


@Composable
fun EquipDetailDialog(show: MutableState<Equipment?>) {
    show.value?.let { equipment ->
        LaunchedEffect(Unit) {
            BattleManager.setEquipUnNew(equipment)
        }
        PanelDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = equipment.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Stars(stars = equipment.star, starWidth = cardStarBigSize)
                Spacer(modifier = Modifier.size(padding26))
                if (equipment.skillEffect != 0) {
                    Text(
                        text = repo.gameCore.getSkillById(equipment.skillEffect)
                            .getRealDescColorful(),
                        style = MaterialTheme.typography.h4,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(padding36))
                Row(
                    modifier = Modifier.weight(1f).fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Image(
                            modifier = Modifier
                                .size(padding130, padding150)
                                .padding(horizontal = padding4, vertical = padding6),
                            painter = painterResource(id = getImageResourceDrawable(equipment.pic)),
                            contentScale = ContentScale.Crop,
                            contentDescription = null
                        )
                        Image(
                            modifier = Modifier.size(padding130, padding150),
                            painter = painterResource(id = R.drawable.common_frame2),
                            contentDescription = null
                        )
                    }
                    Column {
                        equipment.getProperty().MainPropertyLine(
                            originProperty = Property(),
                            textStyle = MaterialTheme.typography.h4,
                            showZero = false
                        )
                    }
                }
            }
        }
    }
}