package com.moyu.chuanqirensheng.screen.setting

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.settingSize
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

val battleInfo = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.battle_info) },
    icon = { R.drawable.menu_info },
    action = { Dialogs.infoDialog.value = true },
)

val pause = SettingItem(
    name = { GameSpeedManager.getCurrentSpeed().description },
    icon = { getImageResourceDrawable(GameSpeedManager.getCurrentSpeed().icon) },
    action = { GameSpeedManager.nextSpeed() },
)

val tutor = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.tutor) },
    icon = { R.drawable.battle_menu_help },
    action = { Dialogs.tutorDialog.value = true },
)

val quit = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.quit) },
    icon = { R.drawable.battle_menu_quit },
    action = {
        Dialogs.alertDialog.value = CommonAlert(
            title = GameApp.instance.getWrapString(R.string.quit_battle_title),
            content = GameApp.instance.getWrapString(R.string.quit_battle_content),
            onConfirm = {
                val forceKill = repo.inBattle.value
                repo.battle.value.terminate()
                repo.inBattle.value = false
                if (isCurrentRoute(PVP_BATTLE_SCREEN)) {
                    PvpManager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                } else if (repo.gameMode.value.isTowerMode()) {
                    repo.battle.value.terminate()
                    repo.inBattle.value = false
                    TowerManager.failed(
                        emptyList(),
                        repo.battleRoles.values.mapNotNull { it })
                    goto(TOWER_SCREEN)
                } else {
                    EventManager.doEventBattleResult(
                        EventManager.selectedEvent.value,
                        result = false,
                        forceQuit = true,
                        forceKill = forceKill
                    )
                }
            }
        )
    },
)

val settingBattleItems = mutableStateListOf(
    battleInfo,
    pause,
    tutor,
    quit
)

data class SettingItem(
    val name: () -> String,
    val icon: () -> Int = { R.drawable.menu_info },
    val redIcon: () -> Boolean = { false },
    val action: () -> Unit,
)


@Composable
fun SettingColumn(modifier: Modifier, settings: SnapshotStateList<SettingItem>) {
    Box(
        modifier = modifier
            .width(settingSize)
            .height((settingSize + padding4) * settings.size - padding4)
            .animateContentSize(),
        contentAlignment = Alignment.Center
    ) {
        Column {
            settings.forEach {
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    it.action()
                    // TODO 刷新UI
                    val list = settings.toList()
                    settings.clear()
                    settings.addAll(list)
                }) {
                    Column {
                        Image(
                            modifier = Modifier.size(settingSize),
                            painter = painterResource(id = it.icon()),
                            contentDescription = it.name()
                        )
                        Spacer(modifier = Modifier.size(padding4))
                    }
                }
            }
        }
    }
}