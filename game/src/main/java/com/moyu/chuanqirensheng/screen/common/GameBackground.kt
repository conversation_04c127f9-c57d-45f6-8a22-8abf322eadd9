package com.moyu.chuanqirensheng.screen.common

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.titleHeight

@Composable
fun GameBackground(
    gapStatusBar: Boolean = true,
    background: Int? = null,
    title: String = "",
    bgMask: Color? = null,
    showCloseIcon: Boolean = true,
    showPreviewIcon: Boolean = true,
    topContent: @Composable (BoxScope.() -> Unit)? = null,
    content: @Composable BoxScope.() -> Unit = {}
) {
    Box(modifier = Modifier.fillMaxSize()) {
        background?.let {
            Image(
                modifier = Modifier
                    .fillMaxSize(),
                contentScale = ContentScale.Crop,
                colorFilter = if (bgMask != null) ColorFilter.tint(
                    bgMask, BlendMode.SrcAtop
                ) else null,
                painter = painterResource(background),
                contentDescription = null
            )
        }
        if (showPreviewIcon || showCloseIcon) {
            // 顶部内容与标题栏
            if (topContent != null) {
                // 有自定义 topContent 时，包裹进一个 Box
                Box(contentAlignment = Alignment.Center) {
                    Box(content = topContent)
                    // 依旧可以放标题栏和关闭按钮
                    TitleBar(title = title, showCloseIcon = showCloseIcon)
                }
            } else {
                // 如果没有自定义的 topContent，则根据 showCloseIcon 决定要不要显示标题栏
                if (title.isNotEmpty()) {
                    TitleBar(title = title, showCloseIcon = showCloseIcon)
                }
            }
        }
        val gap =
            (if (gapStatusBar) WindowInsets.systemBars.asPaddingValues().calculateTopPadding() else 0.dp) + (if (showPreviewIcon || showCloseIcon) titleHeight else 0.dp)
        Column {
            Spacer(Modifier.height(gap))
            Box(content = content)
        }
    }
}

@Composable
fun TitleBar(title: String, showCloseIcon: Boolean) {
    Box(
        modifier = Modifier
            .padding(top = WindowInsets.systemBars.asPaddingValues().calculateTopPadding())
            .fillMaxWidth()
            .height(titleHeight),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize().graphicsLayer {
                scaleX = 1.6f
            },
            painter = painterResource(id = R.drawable.common_frame4),
            contentScale = ContentScale.FillHeight,
            contentDescription = null
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = title,
                style = MaterialTheme.typography.h1,
                color = Color.Black
            )
        }
        if (showCloseIcon) {
            LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher?.let {
                TitleCloseButton(
                    Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = padding10)
                ) {
                    it.onBackPressed()
                }
            }
        }
    }
}

@Composable
fun TitleCloseButton(
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit
) {
    val pressing = remember {
        mutableStateOf(false)
    }
    EffectButton(modifier = modifier, pressing = pressing, onClick = {
        onBackPressed()
    }) {
        Image(
            modifier = Modifier
                .height(backIconHeight)
                .scale(1.2f),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_exit),
            contentDescription = stringResource(R.string.quit_page)
        )
    }
}