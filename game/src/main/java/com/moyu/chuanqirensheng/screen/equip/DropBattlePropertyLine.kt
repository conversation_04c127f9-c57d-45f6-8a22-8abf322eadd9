package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.award.AwardUIParam
import com.moyu.chuanqirensheng.screen.award.SingleAwardItem
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.property.Property
import com.moyu.core.util.percentValueToDotWithNoDigits

@Composable
fun Property.DropBattlePropertyLine(param: AwardUIParam = defaultParam) {
    this.attack.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_1,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.attack),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(),
        )
    }
    this.defenses[0].takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_2,
            name = stringResource(R.string.master) + DamageType.DamageType1.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.defenses[1].takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_3,
            name = stringResource(R.string.master) + DamageType.DamageType2.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.defenses[2].takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_4,
            name = stringResource(R.string.master) + DamageType.DamageType3.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.defenses[3].takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_5,
            name = stringResource(R.string.master) + DamageType.DamageType4.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.defenses[4].takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_6,
            name = stringResource(R.string.master) + DamageType.DamageType5.defenseName,
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.hp.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_7,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.hp),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
    this.getRealFatalRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_8,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.fatal_rate),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.getRealFatalDamage().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_9,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.fatal_damage),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.getRealDodgeRate().takeIf { it != 0.0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_10,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.dodge),
            num = (if (it > 0) "+" else "") + it.percentValueToDotWithNoDigits(),
            param = param,
        )
    }
    this.speed.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.battle_attribute_11,
            name = stringResource(R.string.master) + stringResource(com.moyu.core.R.string.speed),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        )
    }
}