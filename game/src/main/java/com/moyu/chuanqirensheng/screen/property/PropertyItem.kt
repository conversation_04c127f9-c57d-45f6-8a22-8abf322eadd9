package com.moyu.chuanqirensheng.screen.property

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.propertyBigHeight
import com.moyu.chuanqirensheng.ui.theme.propertyBigImageSize
import com.moyu.chuanqirensheng.ui.theme.propertyBigWidth
import com.moyu.core.util.percentValueToDotWithNoDigits
import kotlin.math.roundToInt


@Composable
fun PropertyItem(
    icon: Int,
    getProperty: () -> Double,
    isBoost: () -> Double = { 0.0 },
    getTips: () -> String,
    name: String,
    showPercent: Boolean = false,
    showBoost: Boolean = false,
    showIcon: Boolean = true,
    textColor: Color = Color.Black,
    textStyle: TextStyle = MaterialTheme.typography.h3,
) {
    Box(
        Modifier
            .padding(vertical = padding2)
            .size(propertyBigWidth, propertyBigHeight)
            .clickable { getTips().toast() }) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding2),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val showNum = if (showBoost && isBoost() > 0) isBoost() else getProperty()
            val text =
                if (showPercent) showNum.percentValueToDotWithNoDigits() else "${showNum.roundToInt()}"
            val showText = if (showBoost && isBoost() >= 0) "+$text" else text
            if (showIcon) {
                Image(
                    modifier = Modifier.size(propertyBigImageSize),
                    painter = painterResource(icon),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.size(padding6))
            }
            val boostColor = if (showBoost) {
                if (isBoost() == 0.0) textColor
                else if (isBoost() > 0) DarkGreen
                else DARK_RED
            } else {
                textColor
            }
            Text(
                text = "$name $showText",
                style = textStyle,
                color = boostColor,
                maxLines = 1,
                softWrap = false,
                overflow = TextOverflow.Visible,
            )
        }
    }
}