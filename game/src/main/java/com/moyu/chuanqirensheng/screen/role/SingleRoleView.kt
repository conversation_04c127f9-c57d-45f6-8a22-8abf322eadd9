package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.logic.getTouchInfo
import com.moyu.chuanqirensheng.screen.battle.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.heroGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.Red50
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.role.Role


@Composable
fun SingleRoleView(
    role: Role,
    showName: Boolean = true,
    showHp: Boolean = true,
    extraInfo: String = "",
    textColor: Color = Color.White,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Role) -> Unit = { Dialogs.roleDetailDialog.value = it }
) {
    val race = role.getRace()
    val ally = role.getAlly()
    EffectButton(modifier = Modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(role)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                // 斜向剪影
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .graphicsLayer {
                            // 压扁和倾斜效果
                            scaleY = 0.3f
                            rotationZ = 10f
                            transformOrigin = TransformOrigin(0.5f, 1f) // 设置旋转中心为底部中心
                        },
                    alignment = Alignment.TopCenter,
                    painter = painterResource(id = getImageResourceDrawable(race.getHeadIcon())),
                    colorFilter = ColorFilter.tint(Color.Black, BlendMode.SrcIn),
                    contentDescription = null
                )
                val colorFilter = if (role.getShowBadBuff().isEmpty()) {
                    null
                } else {
                    ColorFilter.tint(
                        Red50, BlendMode.SrcAtop
                    )
                }
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize),
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Fit,
                    painter = painterResource(id = getImageResourceDrawable(race.getHeadIcon())),
                    contentDescription = ally.getTouchInfo(),
                    colorFilter = colorFilter
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                Box(
                    Modifier
                        .size(itemSize.itemSize / 2.4f)
                        .align(Alignment.BottomStart)
                        .graphicsLayer {
                            translationX = -(itemSize.itemSize / 6).toPx()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = ally.getQualityFrame()),
                        contentDescription = null
                    )
                    Text(
                        text = ally.star.toString(),
                        style = itemSize.getTextStyle()
                    )
                }
                if (ally.isHero()) {
                    ForeverGif(
                        modifier = Modifier
                            .size(itemSize.itemSize),
                        resource = heroGif.gif,
                        num = heroGif.count,
                        needGap = true
                    )
                }
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim { role }
                }
                Spacer(modifier = Modifier.size(padding2))
            }
            if (showName) {
                Text(
                    text = race.name,
                    style = itemSize.getTextStyle(),
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}