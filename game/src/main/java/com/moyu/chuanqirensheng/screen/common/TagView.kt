package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.White80
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding5

@Composable
fun TagView(
    modifier: Modifier = Modifier,
    title: String,
    selected: Boolean = true,
    red: Boolean,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(
                id = if (selected) R.drawable.common_tab1 else R.drawable.common_tab2
            ),
            contentDescription = null
        )
        Text(
            text = title,
            style = if (title.length >= 4) MaterialTheme.typography.h4 else MaterialTheme.typography.h3,
            fontWeight = FontWeight.Bold,
            color = if (selected) Color.White else White80,
            maxLines = 1,
            overflow = TextOverflow.Visible,
            softWrap = false,
        )
        if (red) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall)
                    .graphicsLayer {
                        translationX = padding5.toPx()
                        clip = false
                    },
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}