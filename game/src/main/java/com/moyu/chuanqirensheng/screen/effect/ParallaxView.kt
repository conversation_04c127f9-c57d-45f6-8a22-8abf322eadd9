package com.moyu.chuanqirensheng.screen.effect

import android.annotation.SuppressLint
import android.os.Build
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.BiasAlignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.draw.blur
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.CardSize


@SuppressLint("UnrememberedMutableState")
@Composable
fun ShadowedView(
    modifier: Modifier = Modifier,
    cardSize: CardSize,
    content: @Composable BoxScope.() -> Unit
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        val versionCode = Build.VERSION.SDK_INT
        if (versionCode >= Build.VERSION_CODES.S) {
            Box(
                modifier = Modifier
                    .offset {
                        IntOffset(
                            x = -(4 * 1.6).dp.roundToPx(),
                            y = (2 * 1.3).dp.roundToPx()
                        )
                    }
                    .blur(radius = cardSize.getRadius(), edgeTreatment = BlurredEdgeTreatment.Unbounded),
            ) {
                Image(
                    modifier = Modifier.size(cardSize.width, cardSize.height),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.main_frame_1),
                    contentDescription = null
                )
            }
        } else {
            // Android 11及以下版本
        }
        Box(modifier = Modifier
            .size(cardSize.width, cardSize.height)
            .offset {
                IntOffset(
                    x = 4.dp.roundToPx(),
                    y = -2.dp.roundToPx()
                )
            }
            .align(Alignment.Center),
            contentAlignment = BiasAlignment(
                horizontalBias = (4 * 0.01).toFloat(),
                verticalBias = -(2 * 0.01).toFloat(),
            )) {
            content()
        }
    }
}