package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextInt
import kotlinx.coroutines.delay

@Composable
fun GifView(modifier: Modifier, enabled: <PERSON>olean, gifCount: Int, gifDrawable: String, pace: Int = 1, end:()->Unit = {}) {
    val switch = remember { mutableStateOf(false) }
    LaunchedEffect(enabled) {
        delay(RANDOM.nextInt(10, 80).toLong())
        switch.value = enabled
    }
    val effectIndex by animateIntAsState(
        targetValue = if (switch.value) gifCount else 1,
        animationSpec = TweenSpec(
            durationMillis = gifCount * pace * 33,
            easing = LinearEasing
        ),
        finishedListener = {
            switch.value = false
            end()
        }, label = ""
    )
    if (switch.value) {
        Image(
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${gifDrawable}${effectIndex}"
                )
            ),
            contentDescription = null,
            modifier = modifier.scale(2f),
            contentScale = ContentScale.FillHeight
        )
    }
}
