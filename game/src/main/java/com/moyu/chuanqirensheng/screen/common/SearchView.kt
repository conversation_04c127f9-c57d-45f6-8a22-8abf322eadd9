package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.ui.theme.codeInputHeight
import com.moyu.chuanqirensheng.ui.theme.codeInputWidth
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight

@Composable
fun SearchView(filterName: MutableState<String>) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.size(codeInputWidth, codeInputHeight)
    ) {
        DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), filterName.value) {
            filterName.value = it.trim()
        }
    }
}