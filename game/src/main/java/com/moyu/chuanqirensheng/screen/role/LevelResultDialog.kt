package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.skill.LevelUpEvent
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award
import com.moyu.core.model.Title
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun LevelResultDialog(show: MutableState<Award?>) {
    show.value?.let { title ->
        val awards = title
        PanelDialog(onDismissRequest = {
            if (show.value != null) {
                show.value = null
                GameApp.globalScope.launch(Dispatchers.Main) {
                    adventureSkillTrigger(triggerSkill = LevelUpEvent)
                    // 存在一次升级没有升完的情况
                    if (BattleManager.yourTitle.value.level < Title.getTitleLevel(BattleManager.yourExp.value)) {
                        Dialogs.levelUpDialog.value = true
                    }
                }
            }
        }, contentBelow = {
            GameButton(text = stringResource(R.string.confirm)) {
                if (show.value != null) {
                    show.value = null
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        adventureSkillTrigger(triggerSkill = LevelUpEvent)
                        // 存在一次升级没有升完的情况
                        if (BattleManager.yourTitle.value.level < Title.getTitleLevel(BattleManager.yourExp.value)) {
                            Dialogs.levelUpDialog.value = true
                        }
                    }
                }
            }
        }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.title_level_up),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Column(
                    Modifier
                        .fillMaxSize()
                        .padding(horizontal = padding6)) {
                    Spacer(modifier = Modifier.size(padding16))
//                    Text(
//                        text = stringResource(R.string.title_up_content, title.name),
//                        style = MaterialTheme.typography.h3,
//                        color = Color.Black
//                    )
//                    Spacer(modifier = Modifier.size(padding10))
                    Text(
                        text = stringResource(R.string.award_property),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(
                        award = awards,
                        param = defaultParam.copy(textColor = Color.Black)
                    )
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
        }
    }
}