package com.moyu.chuanqirensheng.screen.fortune

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.core.model.Ally
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun OutAllyScreen() {
    GameBackground(title = stringResource(R.string.out_ally)) {
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(allyOrderList.first())
        }
        val list = repo.allyManager.data.filter { !it.isHero() }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { order.value.order?.invoke(it) }
        DisposableEffect(Unit) {
            onDispose {
                repo.allyManager.setUnNew() {
                    !it.isHero()
                }
            }
        }
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = padding45)
        ) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = padding34),
                painter = painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = padding16)
                    .verticalScroll(rememberScrollState())
            ) {
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(3),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(padding10))
                                val ally = list[index]
                                SingleAllyView(
                                    ally = ally,
                                    showName = true,
                                    showRed = true,
                                    showNum = true,
                                    itemSize = ItemSize.LargePlus,
                                    textColor = Color.White
                                )
                                Spacer(modifier = Modifier.size(padding10))
                            }
                        }
                    })
                Row(
                    Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CommonOrderView(
                        Modifier.padding(start = padding10), showOrder
                    )
                    CommonFilterView(
                        Modifier.padding(end = padding10), showFilter
                    )
                }
            }
            OrderLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding72, end = padding10),
                show = showOrder,
                filter = order,
                filterList = allyOrderList
            )
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = padding72, end = padding10),
                show = showFilter,
                filter = filter,
                filterList = allyFilterList
            )
        }
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
            GameButton(
                Modifier.padding(bottom = padding6),
                buttonSize = ButtonSize.Big,
                text = stringResource(R.string.star_up_all)
            ) {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    repo.allyManager.starUpAll(false)
                }
            }
        }
    }
}