package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.TALENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.ui.GuidePointAndText
import com.moyu.chuanqirensheng.feature.guide.ui.HandType
import com.moyu.chuanqirensheng.logic.ally.AllySlots
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.screen.common.TagView
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.screen.filter.heroOrderList
import com.moyu.chuanqirensheng.screen.filter.pvpFilterList
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.tabButtonHeight
import com.moyu.chuanqirensheng.ui.theme.tabButtonWidth
import com.moyu.core.model.Ally

data class SelectAllyData(
    val capacity: Int = 6,
    val needMaster: Boolean = false,
    val needGod: Boolean = false,
    val filter: (Ally) -> Boolean = { true },
    val start: () -> Boolean,
)

@Composable
fun SelectAllyToBattleDialog(show: MutableState<SelectAllyData?>) {
    show.value?.let { data ->
        val selectedHero = remember {
            mutableStateOf(true)
        }
        val orders = if (repo.gameMode.value.isNotNormalMode()) {
            if (selectedHero.value) {
                heroOrderList
            } else {
                allyOrderList
            }
        } else allyOrderList
        val filters =
            if (repo.gameMode.value.isNotNormalMode()) pvpFilterList else allyFilterList
        val currentSlotIndex = remember {
            mutableIntStateOf(0)
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(orders.first())
        }
        val list =
            if (repo.gameMode.value.isNotNormalMode()) {
                // pvp模式，不区分英雄和兵种
                val rawList = BattleManager.getGameAllies().filter { it.isHero() || data.filter(it) }
                    .filter { ally ->
                        filter.all { it.filter.invoke(ally) }
                    }.filter {
                        if (selectedHero.value) it.isHero() else !it.isHero()
                    }
                rawList.filter { it.battlePosition >= 0 }
                    .sortedByDescending { order.value.order?.invoke(it) } + rawList.filter { it.battlePosition < 0 }
                    .sortedByDescending { order.value.order?.invoke(it) }
            } else {
                // 常规模式，英雄永远在前面，因为只有一个英雄
                val rawList = BattleManager.getGameAlliesNoMaster()
                    .filter { !it.temp }
                    .filter { !it.isDead() }
                    .filter { ally ->
                        filter.all { it.filter.invoke(ally) }
                    }
                BattleManager.getGameAllies()
                    .filter { it.isHero() && !it.isDead() } + rawList.filter { it.battlePosition >= 0 }
                    .sortedByDescending { order.value.order?.invoke(it) } + rawList.filter { it.battlePosition < 0 }
                    .sortedByDescending { order.value.order?.invoke(it) }
            }
        EmptyDialog(
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                PanelLayout(Modifier, PanelSize.SmallPlus) {
                    AllySlots(data.capacity, currentSlotIndex)
                }
                Spacer(modifier = Modifier.size(padding14))
                PanelLayout(Modifier, PanelSize.Normal, showClose = true, onClose = {
                    show.value = null
                }) {
                    LazyVerticalGrid(modifier = Modifier.fillMaxSize(),
                        columns = GridCells.Fixed(3),
                        content = {
                            items(list.size) { index ->
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    Spacer(modifier = Modifier.size(padding4))
                                    val ally = list[index]
                                    Box {
                                        if (ally.battlePosition >= 0) {
                                            Image(
                                                painter = painterResource(id = R.drawable.common_choose),
                                                modifier = Modifier
                                                    .size(imageMedium)
                                                    .align(Alignment.BottomEnd)
                                                    .zIndex(999f),
                                                contentDescription = null
                                            )
                                        }
                                        SingleAllyView(
                                            ally = ally,
                                            showName = false,
                                            showHp = true,
                                            showNum = false,
                                            frame = null,
                                            itemSize = ItemSize.LargePlus,
                                            extraInfo = if (ally.isDead()) stringResource(R.string.died)
                                            else if (ally.isHurt()) stringResource(R.string.hurt) else ""
                                        )
                                    }
                                    Spacer(modifier = Modifier.size(padding4))
                                    val selected = BattleManager.isAllyInBattle(ally)
                                    GameButton(
                                        text = if (selected) stringResource(
                                            id = R.string.cancel
                                        ) else stringResource(
                                            id = R.string.do_select
                                        ),
                                        buttonSize = ButtonSize.Small,
                                        buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Orange,
                                        onClick = {
                                            if (data.capacity == 1) {
                                                // 单挑
                                                BattleManager.selectAllyToBattle(
                                                    ally,
                                                    1,
                                                )
                                            } else {
                                                // 其他
                                                BattleManager.selectAllyToBattle(
                                                    ally,
                                                    currentSlotIndex.intValue,
                                                )
                                                // 刷新
                                                currentSlotIndex.intValue =
                                                    (0..8).toList().firstOrNull {
                                                        BattleManager.getBattleAllies()[it] == null
                                                    } ?: 0
                                            }
                                        })
                                }
                            }
                        })
                    OrderLayout(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(bottom = padding6, end = padding10),
                        show = showOrder,
                        filter = order,
                        filterList = orders
                    )
                    FilterLayout(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(bottom = padding6, end = padding10),
                        show = showFilter,
                        filter = filter,
                        filterList = filters
                    )
                }
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(padding28)
                        .graphicsLayer {
                            translationX = -padding19.toPx()
                            translationY = -padding6.toPx()
                        },
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    if (repo.gameMode.value.isNotNormalMode()) {
                        Spacer(modifier = Modifier.width(padding80))
                        EffectButton(onClick = {
                            selectedHero.value = true
                        }) {
                            TagView(
                                modifier = Modifier
                                    .size(tabButtonWidth, tabButtonHeight),
                                GameApp.instance.getWrapString(R.string.hero),
                                selected = selectedHero.value,
                                false
                            )
                        }
                        EffectButton(onClick = {
                            selectedHero.value = false
                        }) {
                            TagView(
                                modifier = Modifier
                                    .size(tabButtonWidth, tabButtonHeight),
                                GameApp.instance.getWrapString(R.string.allies),
                                selected = !selectedHero.value,
                                false
                            )
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    CommonOrderView(
                        Modifier.padding(start = padding10), showOrder
                    )
                    CommonFilterView(
                        Modifier.padding(end = padding10), showFilter
                    )
                }
                Spacer(modifier = Modifier.size(padding22))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    if (BattleManager.getBattleAllies().isNotEmpty()) {
                        GameButton(text = stringResource(R.string.one_click_deselect_battle)) {
                            BattleManager.oneShotDeselect()
                        }
                    } else {
                        GameButton(text = stringResource(R.string.one_click_select_battle)) {
                            BattleManager.oneShotSelect(data)
                            if (GuideManager.showGuide.value) {
                                GuideManager.showGuide.value = false
                                GuideManager.guideIndex.intValue += 1
                                setIntValueByKey(KEY_GUIDE_INDEX, TALENT_GUIDE_START)
                            }
                        }
                    }
                    GameButton(
                        text = stringResource(id = R.string.start_battle),
                        buttonStyle = ButtonStyle.Orange
                    ) {
                        if (GuideManager.showGuide.value) {
                            GuideManager.showGuide.value = false
                            GuideManager.guideIndex.intValue = TALENT_GUIDE_START
                            setIntValueByKey(KEY_GUIDE_INDEX, TALENT_GUIDE_START)
                        }
                        if (data.start()) show.value = null
                    }
                }
            }
            if (GuideManager.guideIndex.intValue == BATTLE_GUIDE_START + 1 && GuideManager.showGuide.value) {
                Column(
                    Modifier
                        .fillMaxSize()
                        .background(B65)
                        .clickable {

                        }, horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .width(padding300),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        GuidePointAndText(
                            text = GameApp.instance.getWrapString(R.string.guide10),
                            handType = HandType.DOWN_HAND,
                            offsetX = -padding60,
                        )
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .graphicsLayer {
                                translationY = padding22.toPx()
                            }, horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(text = stringResource(R.string.one_click_select_battle)) {
                            BattleManager.oneShotSelect(data)
                            GuideManager.guideIndex.intValue += 1
                            setIntValueByKey(KEY_GUIDE_INDEX, TALENT_GUIDE_START)
                        }
                        GameButton(
                            modifier = Modifier.alpha(0f),
                            text = stringResource(id = R.string.start_battle),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            // 引导故意不响应不显示
                        }
                    }
                    Spacer(modifier = Modifier.size(padding45))
                }
            }
            if (GuideManager.guideIndex.intValue == BATTLE_GUIDE_START + 2 && GuideManager.showGuide.value) {
                Column(
                    Modifier
                        .fillMaxSize()
                        .background(B65)
                        .clickable {

                        }, horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(
                        modifier = Modifier
                            .align(Alignment.End)
                            .width(padding300),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        GuidePointAndText(
                            text = GameApp.instance.getWrapString(R.string.guide11),
                            handType = HandType.DOWN_HAND,
                        )
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .graphicsLayer {
                                translationY = padding22.toPx()
                            }, horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            modifier = Modifier.alpha(0f),
                            text = stringResource(R.string.one_click_select_battle)
                        ) {
                            // 引导故意不响应不显示
                        }
                        GameButton(
                            text = stringResource(id = R.string.start_battle),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            show.value = null
                            data.start()
                            GuideManager.showGuide.value = false
                            GuideManager.guideIndex.intValue = TALENT_GUIDE_START
                            setIntValueByKey(KEY_GUIDE_INDEX, TALENT_GUIDE_START)
                        }
                    }
                    Spacer(modifier = Modifier.size(padding45))
                }
            }
        }
    }
}


