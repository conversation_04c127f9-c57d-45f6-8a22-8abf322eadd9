package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.foundation.layout.Box
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign

@Composable
fun StrokedText(
    modifier: Modifier = Modifier,
    text: String,
    textColor: Color = Color.White,
    style: TextStyle,
    strokeColor: Color = Color.Black,
    strokeWidth: Float = 4f // 描边宽度
) {
    Box(
        modifier = modifier, contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = strokeColor,
            style = style.copy(
                drawStyle = Stroke(
                    miter = strokeWidth,
                    width = strokeWidth
                )
            ),
            textAlign = TextAlign.Center
        )
        Text(
            text = text,
            color = textColor,
            style = style,
            textAlign = TextAlign.Center
        )
    }
}
