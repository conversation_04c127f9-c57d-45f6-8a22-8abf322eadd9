package com.moyu.chuanqirensheng.media

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView


@Composable
fun VideoPlayer2(modifier: Modifier) {
    if (VideoPlayerManager.mExoPlayer.value != null) {
        AndroidView(modifier = modifier, factory = { context ->
            PlayerView(context).apply {
                try {
                    useController = false
                    player = VideoPlayerManager.mExoPlayer.value
                    player = player
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })
    }
}