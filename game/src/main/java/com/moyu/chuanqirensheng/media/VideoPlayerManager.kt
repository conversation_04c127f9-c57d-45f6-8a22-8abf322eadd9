package com.moyu.chuanqirensheng.media

import androidx.compose.runtime.mutableStateOf
import androidx.media3.common.MediaItem
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.RawResourceDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


object VideoPlayerManager {
    val mExoPlayer = mutableStateOf<ExoPlayer?>(null)

    fun init() {
        if (!GameApp.instance.isToutiao()) {
            try {
                if (mExoPlayer.value == null) {
                    val mContext = GameApp.instance
                    mExoPlayer.value = ExoPlayer.Builder(mContext).build()

                    val uri = RawResourceDataSource.buildRawResourceUri(R.raw.game_bg)
                    val dataSourceFactory = DefaultDataSource.Factory(mContext)
                    val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
                        .createMediaSource(MediaItem.fromUri(uri))
                    mExoPlayer.value?.setMediaSource(mediaSource)
                    mExoPlayer.value?.prepare()
                    mExoPlayer.value?.repeatMode = ExoPlayer.REPEAT_MODE_ONE
                    mExoPlayer.value?.playWhenReady = true
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
