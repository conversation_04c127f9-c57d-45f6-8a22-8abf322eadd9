package com.moyu.chuanqirensheng.application

import androidx.activity.ComponentActivity
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.navigation.NavHostController
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.inittask.AdjustTask
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.ChannelTask
import com.moyu.chuanqirensheng.application.inittask.ConfigTask
import com.moyu.chuanqirensheng.application.inittask.DataStoreTask
import com.moyu.chuanqirensheng.application.inittask.DebugTask
import com.moyu.chuanqirensheng.application.inittask.LifecycleTask
import com.moyu.chuanqirensheng.application.inittask.MusicTask
import com.moyu.chuanqirensheng.application.inittask.ReportTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.application.inittask.TimberTask
import com.moyu.chuanqirensheng.application.inittask.UncompressTask
import com.moyu.chuanqirensheng.cloud.loginsdk.GameSdkDefaultProcessor
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.loginsdk.LoginData
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.sub.saver.CloudSaver
import com.moyu.chuanqirensheng.sub.saver.CloudSaverDefault
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.sub.saver.getFootPrint
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.AppWrapper
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber

class GameApp(
    gameSdkProcessor: GameSdkProcessor = GameSdkDefaultProcessor(),
    cloudSaver: CloudSaver = CloudSaverDefault()
) : com.xingma.sdk.XMApplication(), GameSdkProcessor by gameSdkProcessor, CloudSaver by cloudSaver {

    companion object {
        lateinit var instance: GameApp
        lateinit var globalScope: CoroutineScope
        var newUser: Boolean = false
    }

    var isForeground: Boolean = true
    var navController: NavHostController? = null
    var lastNetWorkTime = mutableLongStateOf(0L)
    var elapsedDiffTime: Long = 0L
    lateinit var activity: ComponentActivity
    var loginData = mutableStateOf(LoginData(0L, verified = true, showDialog = false))

    override fun onCreate() {
        super.onCreate()
        instance = this
        AppWrapper.initialize(::getWrapString)
        AppWrapper.setIsToutiao(isToutiao())
        globalScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

        // 这个必须放到最前面，否则资源错误
        runBlocking {
            LanguageManager.init()
            UncompressTask().execute(this@GameApp)
            ConfigTask().execute(this@GameApp)
            MusicTask().execute(this@GameApp)
            AdjustTask().execute(this@GameApp)
            ReportTask().execute(this@GameApp)
            DebugTask().execute(this@GameApp)
            LifecycleTask().execute(this@GameApp)
            BuglyTask().execute(this@GameApp)
            BillingTask().execute(this@GameApp)
            ChannelTask().execute(this@GameApp)
            RootCheckerTask().execute(this@GameApp)
            DataStoreTask().execute(this@GameApp)
            TTRewardAdTask().execute(this@GameApp)
        }
    }

    fun tryLogin() {
        globalScope.launch(Dispatchers.IO) {
            RetrofitModel.getLoginData()
            withContext(Dispatchers.Main) {
                repo.doInitAfterLogin()
                instance.uploadRoleInfo()
            }
            GuideManager.showFirstGuide()
            if (newUser) { // 新用户
                // 看下是不是有存档
                CloudSaverManager.checkIfNewUserHaveCloudSave()
            }
            setBooleanValueByKey(KEY_NEW_USER, false)
        }
    }

    fun getShareCode(): String {
        return AdUtil.simpleEncodeText(getObjectId()!!)
    }

    suspend fun getLoginUser(): LoginUser {
        return LoginUser(
            versionCode = getVersionCode(),
            userId = instance.getObjectId() ?: "",
            userName = instance.getUserName() ?: "",
            footPrint = getFootPrint(),
            randomInt = RANDOM.nextInt(),
            isCheating = AntiCheatManager.isCheating(),
            platformChannel = instance.resources.getString(R.string.platform_channel),
            buildFlavor = BuildConfig.FLAVOR,
            signature = AESUtil.getSignature(),
            serverId = ServerManager.getSavedServerId()
        )
    }

    fun getWrapString(resId: Int): String {
        return try {
            instance.activity.getString(resId)
        } catch (e: Exception) {
            instance.getString(resId)
        }
    }

    fun getWrapString(resId: Int, vararg formatArgs: Any): String {
        return try {
            instance.activity.getString(resId, *formatArgs)
        } catch (e: Exception) {
            instance.getString(resId, *formatArgs)
        }
    }

    fun canShowAifadian(): Boolean {
        return if (!instance.resources.getBoolean(R.bool.has_billing)) {
            loginData.value.canShowAifadian
        } else {
            // google版，这里是true，不是说有爱发电，而是说有付费商品
            true
        }
    }

    fun isToutiao(): Boolean {
        return instance.resources.getString(
            R.string.platform_channel
        ) == "toutiao"
    }

    fun isTaptap(): Boolean {
        return instance.resources.getString(
            R.string.platform_channel
        ) == "taptap"
    }
}