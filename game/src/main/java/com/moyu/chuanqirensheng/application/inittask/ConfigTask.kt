package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.config.ConfigManager
import com.moyu.chuanqirensheng.sub.job.JobContent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class ConfigTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        runBlocking {
            ConfigManager.loadConfigs(repo.gameCore)
        }
    }
}