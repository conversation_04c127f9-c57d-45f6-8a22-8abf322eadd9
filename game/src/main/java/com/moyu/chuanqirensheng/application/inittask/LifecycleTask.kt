package com.moyu.chuanqirensheng.application.inittask

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.sub.job.JobContent
import kotlinx.coroutines.launch

/**
 * 另外音乐会根据前后台处理
 */
class LifecycleTask : JobContent<GameApp> {

    private var activityAccount = 0
    override fun execute(context: GameApp) {
        context.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            }

            override fun onActivityStarted(activity: Activity) {
                if (activityAccount == 0) {
                    MusicManager.muteByBackGround(false)
                }
                GameApp.instance.isForeground = true
                activityAccount++
            }

            override fun onActivityResumed(activity: Activity) {
                playerMusicByScreen()
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
                activityAccount--
                if (activityAccount == 0) {
                    GameApp.instance.isForeground = false
                    GameApp.globalScope.launch {
                        MusicManager.muteByBackGround(true)
                        MusicManager.stopAll()
                    }
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            override fun onActivityDestroyed(activity: Activity) {
            }
        })
    }
}
