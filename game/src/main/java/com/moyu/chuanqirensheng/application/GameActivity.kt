package com.moyu.chuanqirensheng.application

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.PersistableBundle
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.addCallback
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs.alertDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyInGameStarUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyStarUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.awardDialog
import com.moyu.chuanqirensheng.application.Dialogs.buffDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.commonBlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugAdvSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.detailTalentDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.editUserInfoDialog
import com.moyu.chuanqirensheng.application.Dialogs.endingDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.endingDialog
import com.moyu.chuanqirensheng.application.Dialogs.equipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.errorOrderDialog
import com.moyu.chuanqirensheng.application.Dialogs.eventDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.eventFailDialog
import com.moyu.chuanqirensheng.application.Dialogs.eventPassDialog
import com.moyu.chuanqirensheng.application.Dialogs.fatalEnemyDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameAllyDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameLoseDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameMasterDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameReviewDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameWinDialog
import com.moyu.chuanqirensheng.application.Dialogs.giftDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.infoDialog
import com.moyu.chuanqirensheng.application.Dialogs.levelResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.levelUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.moneyTransferDialog
import com.moyu.chuanqirensheng.application.Dialogs.newQuestAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.payBlockingDialog
import com.moyu.chuanqirensheng.application.Dialogs.pvp2RankAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.pvpRankAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.roleDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.selectAllyToBattleDialog
import com.moyu.chuanqirensheng.application.Dialogs.selectAllyToGameDialog
import com.moyu.chuanqirensheng.application.Dialogs.sellPoolDialog
import com.moyu.chuanqirensheng.application.Dialogs.settingDialog
import com.moyu.chuanqirensheng.application.Dialogs.shareCodeDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPermissionDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPrivacyDialog
import com.moyu.chuanqirensheng.application.Dialogs.skillDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.skillLevelInfoDialog
import com.moyu.chuanqirensheng.application.Dialogs.statisticView
import com.moyu.chuanqirensheng.application.Dialogs.tutorDialog
import com.moyu.chuanqirensheng.application.Dialogs.useSaveDialog
import com.moyu.chuanqirensheng.application.Dialogs.vipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPass2UnlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPassDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPassUnlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.worldBossCrossRankAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.worldBossCrossRankGetAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.worldBossRankAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.worldBossRankGetAwardDialog
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.ChannelTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.debug.DebugAdvSkillDialog
import com.moyu.chuanqirensheng.debug.DebugBattleScreen
import com.moyu.chuanqirensheng.debug.DebugScreen
import com.moyu.chuanqirensheng.debug.DebugSkillDialog
import com.moyu.chuanqirensheng.feature.activities.ActivitiesScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePass2Screen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPass2UnlockDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPassUnlockDialog
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.draw.ui.DrawResultDialog
import com.moyu.chuanqirensheng.feature.draw.ui.DrawScreen
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.ending.ui.EndingDetailDialog
import com.moyu.chuanqirensheng.feature.ending.ui.EndingDialog
import com.moyu.chuanqirensheng.feature.ending.ui.EndingScreen
import com.moyu.chuanqirensheng.feature.gift.ui.GiftDetailDialog
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideMask
import com.moyu.chuanqirensheng.feature.holiday.ui.HolidayAllScreen
import com.moyu.chuanqirensheng.feature.illustration.ui.IllustrationScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.CheapLotteryScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.ExpensiveLotteryScreen
import com.moyu.chuanqirensheng.feature.more.MailsScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.NewTaskAllScreen
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2BattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2ChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2EntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2QuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.Pvp2RankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpBattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpEntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpQuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpRankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpSellScreen
import com.moyu.chuanqirensheng.feature.quest.ui.NewQuestAwardDialog
import com.moyu.chuanqirensheng.feature.quest.ui.NewQuestScreen
import com.moyu.chuanqirensheng.feature.quest.ui.QuestAllScreen
import com.moyu.chuanqirensheng.feature.rank.ui.RankScreen
import com.moyu.chuanqirensheng.feature.router.ACTIVITY_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.ADVANCED_TUTOR_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS2_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS_SCREEN
import com.moyu.chuanqirensheng.feature.router.CREATE_GAME_SCREEN
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.DRAW_SCREEN
import com.moyu.chuanqirensheng.feature.router.DUNGEON_ALL_SCREEN
import com.moyu.chuanqirensheng.feature.router.ENDING_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.HOLIDAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.MAILS_SCREEN
import com.moyu.chuanqirensheng.feature.router.MORE_SCREEN
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.OUT_ALLY
import com.moyu.chuanqirensheng.feature.router.OUT_HERO
import com.moyu.chuanqirensheng.feature.router.PAGE_PARAM_TAB_INDEX
import com.moyu.chuanqirensheng.feature.router.PVP2_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.SELL_SCREEN_PREFIX
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.SIGN_SCREEN
import com.moyu.chuanqirensheng.feature.router.SKILL_ILLUSTRATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.VIP_SCREEN
import com.moyu.chuanqirensheng.feature.router.WORLD_BOSS_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.WORLD_BOSS_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.feature.router.popTop
import com.moyu.chuanqirensheng.feature.sell.ui.SellAllScreen
import com.moyu.chuanqirensheng.feature.sell.ui.SellPoolDialog
import com.moyu.chuanqirensheng.feature.sign.ui.SignScreen
import com.moyu.chuanqirensheng.feature.talent.ui.TalentDetailDialog
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.ui.DungeonScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerAllScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerBattleScreen
import com.moyu.chuanqirensheng.feature.vip.ui.VipDialog
import com.moyu.chuanqirensheng.feature.vip.ui.VipScreen
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossBattleScreen
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossCrossRankAwardDialog
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossCrossRankGetAwardDialog
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossRankAwardDialog
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossRankGetAwardDialog
import com.moyu.chuanqirensheng.feature.worldboss.ui.WorldBossScreen
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyDetailDialog
import com.moyu.chuanqirensheng.screen.ally.AllyInGameStarUpDialog
import com.moyu.chuanqirensheng.screen.ally.AllyStarUpDialog
import com.moyu.chuanqirensheng.screen.ally.GameAllyDialog
import com.moyu.chuanqirensheng.screen.ally.SelectAllyToBattleDialog
import com.moyu.chuanqirensheng.screen.ally.SelectAllyToGameDialog
import com.moyu.chuanqirensheng.screen.award.AwardDialog
import com.moyu.chuanqirensheng.screen.battle.GameLoseDialog
import com.moyu.chuanqirensheng.screen.battle.GameWinDialog
import com.moyu.chuanqirensheng.screen.battle.StatisticDialog
import com.moyu.chuanqirensheng.screen.buff.BuffDetailDialog
import com.moyu.chuanqirensheng.screen.common.AppBackground
import com.moyu.chuanqirensheng.screen.common.GameSnackBar
import com.moyu.chuanqirensheng.screen.common.StatusBarMask
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.CommonAlertDialog
import com.moyu.chuanqirensheng.screen.dialog.CommonBlockDialog
import com.moyu.chuanqirensheng.screen.dialog.EditUserInfoDialog
import com.moyu.chuanqirensheng.screen.dialog.Pvp2RankAwardDialog
import com.moyu.chuanqirensheng.screen.dialog.PvpRankAwardDialog
import com.moyu.chuanqirensheng.screen.equip.EquipDetailDialog
import com.moyu.chuanqirensheng.screen.event.EventDetailDialog
import com.moyu.chuanqirensheng.screen.event.EventDetailScreen
import com.moyu.chuanqirensheng.screen.event.EventFailDialog
import com.moyu.chuanqirensheng.screen.event.EventPassDialog
import com.moyu.chuanqirensheng.screen.event.EventSelectScreen
import com.moyu.chuanqirensheng.screen.event.FatalEnemyDialog
import com.moyu.chuanqirensheng.screen.fortune.OutAllyScreen
import com.moyu.chuanqirensheng.screen.fortune.OutHeroScreen
import com.moyu.chuanqirensheng.screen.info.InfoDialog
import com.moyu.chuanqirensheng.screen.life.TalentScreen
import com.moyu.chuanqirensheng.screen.login.CreateGameScreen
import com.moyu.chuanqirensheng.screen.login.LoginScreen
import com.moyu.chuanqirensheng.screen.more.MoreScreen
import com.moyu.chuanqirensheng.screen.resource.MoneyTransferDialog
import com.moyu.chuanqirensheng.screen.role.GameMasterDialog
import com.moyu.chuanqirensheng.screen.role.LevelResultDialog
import com.moyu.chuanqirensheng.screen.role.LevelUpDialog
import com.moyu.chuanqirensheng.screen.role.RoleDetailDialog
import com.moyu.chuanqirensheng.screen.setting.SettingDialog
import com.moyu.chuanqirensheng.screen.skill.GameSkillDialog
import com.moyu.chuanqirensheng.screen.skill.SkillDetailDialog
import com.moyu.chuanqirensheng.screen.skill.SkillLevelInfoDialog
import com.moyu.chuanqirensheng.screen.tutor.AdvancedTutorScreen
import com.moyu.chuanqirensheng.screen.tutor.TutorDialog
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.GoogleActivityResultHandler
import com.moyu.chuanqirensheng.sub.bill.pay.ErrorOrderDialog
import com.moyu.chuanqirensheng.sub.bill.pay.PayBlockDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.loginsdk.ui.LoginResultDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PermissionAlertDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PrivacyAlertDialog
import com.moyu.chuanqirensheng.sub.review.ui.GameReviewDialog
import com.moyu.chuanqirensheng.sub.saver.ui.UseCloudSaverDialog
import com.moyu.chuanqirensheng.sub.share.ui.ShareCodeDialog
import com.moyu.chuanqirensheng.ui.theme.ComposedTheme
import com.moyu.chuanqirensheng.util.killSelf
import com.xingma.sdk.XmSdk
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

class GameActivity : ComponentActivity() {

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(updateBaseContext(newBase))
    }

    fun updateBaseContext(context: Context?): Context? {
        if (context == null) return context

        // 对于API 33+，系统会自动处理语言设置，不需要手动更新Context
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return context
        }

        // 对于较低版本的Android，继续使用传统方法
        val lang = LanguageManager.selectedLanguage.value
        val locale = Locale(lang)
        Locale.setDefault(locale)

        return updateResourcesLocale(context, locale)
    }

    private fun updateResourcesLocale(context: Context, locale: Locale): Context {
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        return context.createConfigurationContext(configuration)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (GameApp.instance.resources.getString(
                R.string.platform_channel
            ).contains("xiaomi")) {
            window.setFlags(
                android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN,
                android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
            window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            or View.SYSTEM_UI_FLAG_FULLSCREEN
                    )
        }
        GameApp.instance.activity = this
        setContent {
            val navController = rememberNavController()
            ComposedTheme {
                Scaffold { padding ->
                    val heathTipsVisible = remember {
                        mutableStateOf(true)
                    }
                    AppBackground()
                    if (!heathTipsVisible.value || !GameApp.instance.isToutiao()) {
                        RegisterScreens(navController, padding)
                        RegisterDialogs()
                        RegisterFullScreenEffect()
                        RegisterRouter(navController)
                        RegisterSnackBar()
                        RegisterBackCallback(onBackPressedDispatcher)
                    } else {
                        HeathTipsLayout(heathTipsVisible)
                    }
                    StatusBarMask()
                }
            }
        }
        XmSdk.getInstance().onCreate(this)
    }

    override fun onStart() {
        XmSdk.getInstance().onStart(this)
        super.onStart()
    }

    override fun onRestart() {
        XmSdk.getInstance().onRestart(this)
        super.onRestart()
    }

    override fun onPause() {
        XmSdk.getInstance().onPause(this)
        super.onPause()
    }

    override fun onStop() {
        XmSdk.getInstance().onStop(this)
        super.onStop()
    }

    override fun onDestroy() {
        XmSdk.getInstance().onDestroy(this)
        super.onDestroy()
    }

    override fun onNewIntent(intent: Intent?) {
        XmSdk.getInstance().onNewIntent(intent)
        super.onNewIntent(intent)
    }

    override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
        XmSdk.getInstance().onSaveInstanceState(outState)
        super.onSaveInstanceState(outState, outPersistentState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        XmSdk.getInstance().onSaveInstanceState(outState)
        super.onSaveInstanceState(outState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        XmSdk.getInstance().onConfigurationChanged(newConfig)
        super.onConfigurationChanged(newConfig)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        XmSdk.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onResume() {
        XmSdk.getInstance().onResume(this)
        super.onResume()
        BillingManager.queryAsync()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        XmSdk.getInstance().onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)
        GoogleActivityResultHandler.onActivityResult(requestCode, resultCode, data)
    }
}

@Composable
fun RegisterFullScreenEffect() {
    GuideMask()
}

@Composable
fun RegisterScreens(
    navController: NavHostController,
    padding: PaddingValues,
) {
    NavHost(
        navController = navController,
        startDestination = LOGIN_SCREEN,
        modifier = Modifier.padding(padding),
    ) {
        composable(route = LOGIN_SCREEN) { LoginScreen() }
        composable(route = CREATE_GAME_SCREEN) { CreateGameScreen() }
        composable(route = OUT_HERO) { OutHeroScreen() }
        composable(route = OUT_ALLY) { OutAllyScreen() }
        composable(route = TALENT_SCREEN) { TalentScreen() }
        composable(route = SIGN_SCREEN) { SignScreen() }
        composable(route = SELL_SCREEN_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            SellAllScreen(
                it.arguments?.getString(
                    PAGE_PARAM_TAB_INDEX
                )?.toInt() ?: 0
            )
        }
        composable(route = QUEST_SCREEN) { QuestAllScreen() }
        composable(route = RANK_SCREEN) { RankScreen() }
        composable(route = MORE_SCREEN) { MoreScreen() }
        composable(route = EVENT_SELECT_SCREEN) { EventSelectScreen() }
        composable(route = EVENT_DETAIL_SCREEN) { EventDetailScreen() }
        composable(route = VIP_SCREEN) { VipScreen() }
        composable(route = DRAW_SCREEN) { DrawScreen() }
        composable(route = NEW_TASK_SCREEN) { NewQuestScreen() }
        composable(route = ENDING_SCREEN) { EndingScreen() }
        composable(route = SKILL_ILLUSTRATION_SCREEN) { IllustrationScreen() }
        composable(route = ADVANCED_TUTOR_SCREEN) { AdvancedTutorScreen() }
        composable(route = MAILS_SCREEN) { MailsScreen() }
        composable(route = PVP_SCREEN) { PvpEntryScreen() }
        composable(route = PVP2_SCREEN) { Pvp2EntryScreen() }
        composable(route = PVP_SELL_SCREEN) { PvpSellScreen() }
        composable(route = PVP_CHOOSE_ENEMY_SCREEN) { PvpChooseEnemyScreen() }
        composable(route = PVP2_CHOOSE_ENEMY_SCREEN) { Pvp2ChooseEnemyScreen() }
        composable(route = PVP_RANK_SCREEN) { PvpRankScreen() }
        composable(route = PVP2_RANK_SCREEN) { Pvp2RankScreen() }
        composable(route = PVP_QUEST_SCREEN) { PvpQuestScreen() }
        composable(route = PVP2_QUEST_SCREEN) { Pvp2QuestScreen() }
        composable(route = PVP_BATTLE_SCREEN) { PvpBattleScreen() }
        composable(route = PVP2_BATTLE_SCREEN) { Pvp2BattleScreen() }
        composable(route = BATTLE_PASS_SCREEN) { BattlePassScreen() }
        composable(route = BATTLE_PASS2_SCREEN) { BattlePass2Screen() }
        composable(route = SEVEN_DAY_SCREEN) { NewTaskAllScreen() }
        composable(route = LOTTERY_SCREEN1) { CheapLotteryScreen() }
        composable(route = LOTTERY_SCREEN2) { ExpensiveLotteryScreen() }
        composable(route = HOLIDAY_SCREEN) { HolidayAllScreen() }
        composable(route = TOWER_BATTLER_SCREEN) { TowerBattleScreen() }
        composable(route = TOWER_SCREEN) { TowerAllScreen() }
        composable(route = WORLD_BOSS_SCREEN) { WorldBossScreen() }
        composable(route = WORLD_BOSS_BATTLE_SCREEN) { WorldBossBattleScreen() }
        composable(route = ACTIVITY_ALL_SCREEN) { ActivitiesScreen() }
        composable(route = DUNGEON_ALL_SCREEN) { DungeonScreen() }
        composable(route = DEBUG_SCREEN) { DebugScreen() }
        composable(route = DEBUG_BATTLE) { DebugBattleScreen() }
    }
}

@Composable
fun RegisterDialogs() {
    ErrorOrderDialog(errorOrderDialog)
    GiftDetailDialog(giftDetailDialog)

    VipDialog(vipDetailDialog)
    BattlePassDialog(warPassDetailDialog)
    WarPassUnlockDialog(warPassUnlockDialog)
    WarPass2UnlockDialog(warPass2UnlockDialog)
    SellPoolDialog(sellPoolDialog)
    NewQuestAwardDialog(newQuestAwardDialog)
    RoleDetailDialog(roleDetailDialog)
    SkillDetailDialog(skillDetailDialog)
    EquipDetailDialog(equipDetailDialog)
    AllyDetailDialog(allyDetailDialog)
    AllyStarUpDialog(allyStarUpDialog)
    AllyInGameStarUpDialog(allyInGameStarUpDialog)
    TalentDetailDialog(detailTalentDialog)
    GameWinDialog(gameWinDialog)
    GameLoseDialog(gameLoseDialog)
    StatisticDialog(statisticView)
    TutorDialog(tutorDialog)
    EditUserInfoDialog(editUserInfoDialog)
    PvpRankAwardDialog(pvpRankAwardDialog)
    Pvp2RankAwardDialog(pvp2RankAwardDialog)
    WorldBossRankAwardDialog(worldBossRankAwardDialog)
    WorldBossRankGetAwardDialog(worldBossRankGetAwardDialog)
    WorldBossCrossRankAwardDialog(worldBossCrossRankAwardDialog)
    WorldBossCrossRankGetAwardDialog(worldBossCrossRankGetAwardDialog)
    BuffDetailDialog(buffDetailDialog)
    InfoDialog(infoDialog)
    LevelUpDialog(levelUpDialog)
    LevelResultDialog(levelResultDialog)

    SelectAllyToGameDialog(selectAllyToGameDialog)
    SelectAllyToBattleDialog(selectAllyToBattleDialog)
    SettingDialog(settingDialog)
    ShareCodeDialog(shareCodeDialog)
    GameAllyDialog(gameAllyDialog)
    GameSkillDialog(gameSkillDialog)
    GameMasterDialog(gameMasterDialog)
    EventPassDialog(eventPassDialog)
    EventFailDialog(eventFailDialog)
    EventDetailDialog(eventDetailDialog)
    FatalEnemyDialog(fatalEnemyDialog)
    EndingDetailDialog(endingDetailDialog)
    EndingDialog(endingDialog)
    SkillLevelInfoDialog(skillLevelInfoDialog)
    PayBlockDialog(payBlockingDialog)
    ErrorOrderDialog(errorOrderDialog)
    GameReviewDialog(gameReviewDialog)
    CommonBlockDialog(commonBlockDialog)

    DebugSkillDialog(debugSkillDialog)
    DebugAdvSkillDialog(debugAdvSkillDialog)
    CommonAlertDialog(alertDialog)
    LoginResultDialog()
    MoneyTransferDialog(moneyTransferDialog)
    AwardDialog(awardDialog)
    DrawResultDialog(drawResultDialog)
    UseCloudSaverDialog(useSaveDialog)

    PrivacyAlertDialog(switch = showPrivacyDialog, quit = { killSelf() }, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PRIVACY, false)
        showPermissionDialog.value = true
    })
    PermissionAlertDialog(switch = showPermissionDialog, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PERMISSION, false)
        GameApp.instance.initSDK(GameApp.instance.activity)
        GameApp.globalScope.launch {
            delay(1500) //有时候会拉不起来登录，delay下其他操作
            BuglyTask().execute(GameApp.instance)
            TTRewardAdTask().execute(GameApp.instance)
            ChannelTask().execute(GameApp.instance)
            RootCheckerTask().execute(GameApp.instance)
            BillingTask().execute(GameApp.instance)
        }
    })
}

@Composable
fun RegisterRouter(navController: NavHostController) {
    LaunchedEffect(Unit) {
        GameApp.instance.navController = navController
        navController.addOnDestinationChangedListener { _: NavController, _: NavDestination, _: Bundle? ->
            GameApp.globalScope.launch {
                // todo 不delay的话，这里判定时候，页面还没有完成退出
                delay(100)
                playerMusicByScreen()
            }
        }
    }
}

@Composable
fun RegisterSnackBar() {
    GameSnackBar()
}

@Composable
fun RegisterBackCallback(onBackPressedDispatcher: OnBackPressedDispatcher) {
    LaunchedEffect(Unit) {
        onBackPressedDispatcher.addCallback(
            GameApp.instance.activity,
        ) {
            if (!GuideManager.canBack()) {
                GameApp.instance.getWrapString(R.string.guide_block_quit).toast() // 引导时候全局block返回按钮
            } else if (isCurrentRoute(PVP_BATTLE_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_pvp_title),
                    content = GameApp.instance.getWrapString(R.string.quit_pvp_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        PvpManager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                    }
                )
            } else if (isCurrentRoute(PVP2_BATTLE_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_pvp_title),
                    content = GameApp.instance.getWrapString(R.string.quit_pvp_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        Pvp2Manager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                    }
                )
            } else if (isCurrentRoute(WORLD_BOSS_BATTLE_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_world_boss_title),
                    content = GameApp.instance.getWrapString(R.string.quit_world_boss_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        WorldBossManager.battleFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                    }
                )
            } else if (repo.gameMode.value.isTowerMode() && isCurrentRoute(TOWER_BATTLER_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        TowerManager.failed(
                            emptyList(),
                            repo.battleRoles.values.mapNotNull { it })
                        goto(TOWER_SCREEN)
                    }
                )
            } else if (repo.inBattle.value && !isCurrentRoute(DEBUG_BATTLE)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {
                        val forceKill = repo.inBattle.value
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        EventManager.doEventBattleResult(
                            EventManager.selectedEvent.value,
                            result = false,
                            forceQuit = true,
                            forceKill = forceKill,
                        )
                    }
                )
            } else if (isCurrentRoute(EVENT_DETAIL_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_event_title),
                    content = GameApp.instance.getWrapString(R.string.quit_event_content),
                    onConfirm = {
                        EventManager.selectedEvent.value?.let {
                            EventManager.doEventResult(
                                it,
                                it.isMainLine != 1 && !it.isBattle()
                            )
                        }
                    }
                )
            } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
                doQuitGame()
            } else {
                popTop()
            }
        }
    }
}


fun doQuitGame() {
    alertDialog.value = CommonAlert(
        title = GameApp.instance.getWrapString(R.string.quit_life_title),
        content = GameApp.instance.getWrapString(R.string.quit_life_content),
        confirmText = GameApp.instance.getWrapString(R.string.quit_life_title),
        cancelText = GameApp.instance.getWrapString(R.string.temp_save),
        onCancel = {
            repo.inGame.value = false
            goto(LOGIN_SCREEN)
        },
        onConfirm = {
            val story = EndingManager.saveEnding(
                BattleManager.you.value,
                EventManager.eventRecorder.usedEvents,
                false
            )
            EndingManager.ending(story)
            if (story == null) {
                // story为空，需要手动跳转
                repo.inGame.value = false
                goto(LOGIN_SCREEN)
            }
            ContinueManager.clearSave()
        }
    )
}
