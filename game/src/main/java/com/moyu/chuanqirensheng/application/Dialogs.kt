package com.moyu.chuanqirensheng.application

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.saver.GameSaver
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Gift
import com.moyu.core.model.Vip
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

fun String.toast() {
    Dialogs.snackbar.value = this
}

object Dialogs {
    val giftDetailDialog = mutableStateOf<Gift?>(null)
    val vipDetailDialog = mutableStateOf<Vip?>(null)
    val warPassUnlockDialog = mutableStateOf(false)
    val warPass2UnlockDialog = mutableStateOf(false)
    val warPassDetailDialog = mutableStateOf<BattlePass?>(null)
    val sellPoolDialog = mutableStateOf<Award?>(null)
    val newQuestAwardDialog = mutableStateOf(-1)
    val payBlockingDialog = mutableStateOf<String?>(null)
    val commonBlockDialog = mutableStateOf<String?>(null)
    val errorOrderDialog = mutableStateOf(false)
    val levelUpDialog = mutableStateOf(false)
    val levelResultDialog = mutableStateOf<Award?>(null)
    val roleDetailDialog = mutableStateOf<Role?>(null)
    val skillLevelInfoDialog = mutableStateOf<Skill?>(null)
    val skillDetailDialog = mutableStateOf<Skill?>(null)
    val equipDetailDialog = mutableStateOf<Equipment?>(null)
    val allyDetailDialog = mutableStateOf<Ally?>(null)
    val allyStarUpDialog = mutableStateOf<Ally?>(null)
    val allyInGameStarUpDialog = mutableStateOf<Ally?>(null)
    val detailTalentDialog = mutableStateOf<Int?>(null) // Talent mainId
    val gameWinDialog = mutableStateOf<List<Role>>(emptyList())
    val gameLoseDialog = mutableStateOf<List<Role>>(emptyList())
    val awardDialog = mutableStateOf<Award?>(null)
    val drawResultDialog = mutableStateOf<Award?>(null)
    val statisticView = mutableStateOf(false)
    val infoDialog = mutableStateOf(false)
    val buffDetailDialog = mutableStateOf<Pair<Buff, Role>?>(null)
    val snackbar = mutableStateOf("")
    val showPrivacyDialog = mutableStateOf(false)
    val showPermissionDialog = mutableStateOf(false)

    val settingDialog = mutableStateOf(false)
    val shareCodeDialog = mutableStateOf(false)
    val gameMasterDialog = mutableIntStateOf(-1)
    val gameAllyDialog = mutableStateOf(false)
    val gameSkillDialog = mutableStateOf(false)
    val selectAllyToGameDialog = mutableStateOf<Boolean?>(null)
    val selectAllyToBattleDialog = mutableStateOf<SelectAllyData?>(null)
    val eventPassDialog = mutableStateOf<Event?>(null)
    val eventFailDialog = mutableStateOf<Event?>(null)
    val eventDetailDialog = mutableStateOf<Event?>(null)
    val fatalEnemyDialog = mutableStateOf<Event?>(null)
    val endingDetailDialog = mutableStateOf<Ending?>(null)
    val endingDialog = mutableStateOf<Ending?>(null)

    val moneyTransferDialog = mutableStateOf(false)

    val tutorDialog = mutableStateOf(false)
    val useSaveDialog = mutableStateOf<GameSaver?>(null)
    val gameReviewDialog = mutableStateOf(false)
    val editUserInfoDialog = mutableStateOf(false)
    val pvpRankAwardDialog = mutableStateOf(false)
    val pvp2RankAwardDialog = mutableStateOf(false)
    val worldBossRankAwardDialog = mutableStateOf(false)
    val worldBossRankGetAwardDialog = mutableStateOf(false)
    val worldBossCrossRankAwardDialog = mutableStateOf(false)
    val worldBossCrossRankGetAwardDialog = mutableStateOf(false)

    val alertDialog = mutableStateOf<CommonAlert?>(null) // confirm cancel
    val debugSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
    val debugAdvSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
}