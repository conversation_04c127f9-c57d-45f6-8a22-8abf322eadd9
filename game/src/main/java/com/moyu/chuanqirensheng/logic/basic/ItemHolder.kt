package com.moyu.chuanqirensheng.logic.basic

import androidx.compose.runtime.snapshots.SnapshotStateList
import kotlinx.serialization.KSerializer

interface ItemHolder<T : Any> {
    val saveKey: String
    val elementSerializer: KSerializer<T>
    val data: SnapshotStateList<T>
    val sameGroup: (T, T) -> Boolean
    val increase: (T, T) -> T
    suspend fun init()
    fun gain(value: List<T>)
    fun gain(value: T)
    fun remove(value: T)
    fun save()
    fun reset()
    fun haveNew(): Boolean
    fun setUnNew(filter: (T) -> Boolean = { true })
}