package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.util.fastForEachReversed
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Event
import com.moyu.core.model.toAward

class EventRecorder {

    val succeededEvents = mutableStateListOf<Event>()
    val failedEvents = mutableStateListOf<Event>()
    val usedEvents = mutableStateListOf<Event>()
    
    fun clear() {
        succeededEvents.clear()
        failedEvents.clear()
        usedEvents.clear()
    }

    fun addResult(event: Event, adjustedResult: Boolean): Boolean {
        if (usedEvents.any { it.uuid == event.uuid }) return true
        if (usedEvents.any { it.selectAge == BattleManager.getAge() }) return true
        event.copy(selectAge = BattleManager.getAge()).let {
            usedEvents.add(it.simple())
            if (adjustedResult) {
                succeededEvents.add(it.simple())
            } else {
                failedEvents.add(it.simple())
            }
            return false
        }
    }

    fun resetEvents(used: List<Event>, success: List<Event>, failed: List<Event>) {
        usedEvents.clear()
        usedEvents.addAll(used)
        succeededEvents.clear()
        succeededEvents.addAll(success)
        failedEvents.clear()
        failedEvents.addAll(failed)
    }

    val filterNotUsed: (Event) -> Boolean = {
        if (it.isRepeat == 1) true else it.id !in usedEvents.map { it.id }
    }
    val filterFrontAndDisappear: (Event) -> Boolean = {
        if (it.front.first() == 0 && it.front2.first() == 0 && it.disappear.first() == 0) {
            true
        } else {
            var canShow = it.front.first() == 0
            var done = false
            usedEvents.fastForEachReversed { usedEvent ->
                if (!done && it.disappear.first() != 0 && usedEvent.id in it.disappear) {
                    canShow = false
                    done = true
                    return@fastForEachReversed
                }
                if (!done && it.front.first() != 0 && usedEvent.id in it.front) {
                    if (it.front2.first() == 0) {
                        canShow = true
                        done = true
                        return@fastForEachReversed
                    } else {
                        // 新增了front2，所有条件都需要满足才显示，否则不显示
                        if (it.front2.all { needId ->
                            if (needId == 999) {
                                repo.gameCore.getPoolById(needId).let { pool ->
                                    // 有可能front2里有poolId
                                    AwardManager.isAffordable(pool.toAward())
                                }
                            } else {
                                usedEvents.map { it.id }.contains(needId)
                            }
                        }) {
                            canShow = true
                            done = true
                            return@fastForEachReversed
                        } else {
                            canShow = false
                            done = true
                            return@fastForEachReversed
                        }
                    }
                }
            }
            canShow
        }
    }

    val filterFront: (Event) -> Boolean = {
        it.front.first() == 0 || it.front.intersect(usedEvents.map { it.id }
            .toSet()).isNotEmpty()
    }
    val filterDisappear: (Event) -> Boolean = {
        it.disappear.first() == 0 || it.disappear.intersect(usedEvents.map { it.id }
            .toSet())
            .isEmpty()
    }
}