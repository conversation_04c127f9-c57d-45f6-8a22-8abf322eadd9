package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Event


const val INIT_AGE = 0
val battleEventIds = listOf(21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32)
val keyBattleEventIds = listOf(27, 28, 29)
val singleBattleEventIds = listOf(24, 26)
val masterEventIds = listOf(21, 22, 23, 26)
const val EVENT_SIZE = 3

// 只生效一次，生效后要移除
fun Event.forceLose(): Boolean {
    return if (BattleManager.adventureProps.value.failEventPlayIds.contains(
            play
        )
    ) {
        BattleManager.adventureProps.value.failEventPlayIds.toMutableList().let {
            it.remove(play)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                failEventPlayIds = it
            )
        }
        true
    } else if (BattleManager.adventureProps.value.failEventPlayIds.toMutableList().contains(0)) {
        BattleManager.adventureProps.value.failEventPlayIds.toMutableList().let {
            it.remove(0)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                failEventPlayIds = it
            )
        }
        true
    } else {
        false
    }
}

// 只生效一次，生效后要移除
fun Event.tryForceWin(): Boolean {
    return if (BattleManager.adventureProps.value.winEventPlayIds.contains(
            play
        )
    ) {
        BattleManager.adventureProps.value.winEventPlayIds.toMutableList().let {
            it.remove(play)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                winEventPlayIds = it
            )
        }
        true
    } else if (BattleManager.adventureProps.value.winEventPlayIds.toMutableList().contains(0)) {
        BattleManager.adventureProps.value.winEventPlayIds.toMutableList().let {
            it.remove(0)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                winEventPlayIds = it
            )
        }
        true
    } else {
        false
    }
}

fun Event.isBattle() = play in battleEventIds
fun Event.isKeyBattle() = play in keyBattleEventIds
fun Event.isSingle() = false
fun Event.isNeedMaster() = true
fun Event.isNeedGod() = false
fun Event.isSelectGroup() = age.first() == 0
fun Event.isBuilding() = play == 1

fun Int.ageToProgress(): String {
    val daysSinceStart = this - INIT_AGE
    return if (daysSinceStart == 0) {
        GameApp.instance.getWrapString(R.string.select_zhenying)
    } else {
        val years = (daysSinceStart - 1) / 360 // 计算年份
        val daysInCurrentYear = (daysSinceStart - 1) % 360 + 1 // 计算当前年中的天数
        val month = (daysInCurrentYear - 1) / 30 + 1 // 计算月份
        val day = (daysInCurrentYear - 1) % 30 + 1 // 计算日

        when (years) {
            -1, 0 -> GameApp.instance.getWrapString(R.string.age_to_progress_text, month, day)
            else -> GameApp.instance.getWrapString(R.string.age_to_progress_text_with_year, years + 1, month, day)
        }
    }
}

fun Int.levelToDifficultProgress(): String {
    val difficult = repo.gameCore.getDifficultPool()[this / 1000]
    return difficult.name + " " + (this - INIT_AGE).let {
        (this % 1000).ageToProgress()
    }
}