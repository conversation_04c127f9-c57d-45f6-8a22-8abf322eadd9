package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.quest.onTaskAge
import com.moyu.chuanqirensheng.feature.quest.onTaskDoneEvent
import com.moyu.chuanqirensheng.feature.quest.onTaskEnding
import com.moyu.chuanqirensheng.feature.quest.onTaskEnterEvent
import com.moyu.chuanqirensheng.feature.router.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.reportLevels
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.oneBattleContinueFightCount
import com.moyu.chuanqirensheng.logic.event.detail.DirectAwardHandler
import com.moyu.chuanqirensheng.logic.event.detail.HALO10_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO2_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO3_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO4_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO5_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO6_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO7_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO8_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO9_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HALO_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.HaloBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.NonePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.PROTECT_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.ProtectBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SIEGE_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.SelectAwardHandler
import com.moyu.chuanqirensheng.logic.event.detail.SiegeBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.UpgradeAllyHandler
import com.moyu.chuanqirensheng.logic.playName
import com.moyu.chuanqirensheng.logic.skill.AgeEvent
import com.moyu.chuanqirensheng.logic.skill.EnterEvent
import com.moyu.chuanqirensheng.logic.skill.FailedEvent
import com.moyu.chuanqirensheng.logic.skill.StartGame
import com.moyu.chuanqirensheng.logic.skill.SucceededEvent
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.core.model.Event
import com.moyu.core.model.Timing
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val battleDirectAwardIds = listOf(
    HALO_BATTLE,
    HALO3_BATTLE,
    HALO5_BATTLE,
    SIEGE_BATTLE,
    PROTECT_BATTLE
)

val reportAges = listOf(5, 15, 30, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 999)

object EventManager {
    val selectedEvent = mutableStateOf<Event?>(null)// 当前选中事件
    val selectionEvents = mutableStateListOf<Event>() // 当前可选事件
    val eventRecorder = EventRecorder()

    private val eventHandlerClasses = hashMapOf(
        1 to { DirectAwardHandler(playId = 1) },
        2 to { DirectAwardHandler(playId = 2) },
        3 to { SelectAwardHandler(playId = 3) },
        4 to { DirectAwardHandler(playId = 4) },
        5 to { UpgradeAllyHandler() },
        6 to { DirectAwardHandler(playId = 6) },
        7 to { SelectAwardHandler(playId = 7) },
        8 to { DirectAwardHandler(playId = 8) },
        9 to { DirectAwardHandler(playId = 9) },
        10 to { SelectAwardHandler(playId = 10) },
        11 to { DirectAwardHandler(playId = 11) },
        21 to { HaloBattlePlayHandler(playId = HALO_BATTLE) },
        22 to { HaloBattlePlayHandler(playId = HALO2_BATTLE) },
        23 to { HaloBattlePlayHandler(playId = HALO3_BATTLE) },
        24 to { HaloBattlePlayHandler(playId = HALO4_BATTLE) },
        25 to { HaloBattlePlayHandler(playId = HALO5_BATTLE) },
        26 to { HaloBattlePlayHandler(playId = HALO6_BATTLE) },
        27 to { HaloBattlePlayHandler(playId = HALO7_BATTLE) },
        28 to { SiegeBattlePlayHandler() },
        29 to { ProtectBattlePlayHandler() },
        30 to { HaloBattlePlayHandler(playId = HALO8_BATTLE) },
        31 to { HaloBattlePlayHandler(playId = HALO9_BATTLE) },
        32 to { HaloBattlePlayHandler(playId = HALO10_BATTLE) },
    )
    private val playHandlers = hashMapOf<Int, PlayHandler>()

    fun onNewGame() {
        playHandlers.clear()
        eventRecorder.clear()
        selectedEvent.value = null
        selectionEvents.clear()
    }

    fun getEventSelectTitle(): String {
        return BattleManager.adventureProps.value.age.ageToProgress() + selectionEvents.firstOrNull()
            ?.let {
                " " + it.playName()
            }
    }

    fun getNextEvents(): List<Event> {
        if (selectionEvents.isNotEmpty()) { // 保护
            return selectionEvents
        }

        if (DebugManager.allEvent) {
            return repo.gameCore.getEventPool().filter { StoryManager.eventInStoryBag(it) }
                .map { it.createUUID() }
        }

        val allEvents = if (getUsedEvents().isEmpty()) {
            // 首次选阵营
            if (GameApp.newUser) {
                repo.gameCore.getEventPool().filter { it.age[0] == 0 }
                    .filter { StoryManager.eventInStoryBag(it) }
                    .map { it.createUUID() }
                    .toList()
            } else {
                repo.gameCore.getEventPool().filter { it.age[0] == 0 }
                    .filter { StoryManager.eventInStoryBag(it) }
                    .map { it.createUUID() }
                    .toList().shuffled(RANDOM)
            }
        } else {
            val age = BattleManager.adventureProps.value.age
            // 计算是否fixedAppear，要先过一次条件再处理
            val day = (age - 1) % 30 + 1
            val available = repo.gameCore.getEventPool().asSequence()
                .filter { age >= it.age[0] && age <= it.age[1] }
                .filter { StoryManager.eventInStoryBag(it) }
                .filter(eventRecorder.filterNotUsed)
                .filter(eventRecorder.filterFrontAndDisappear)
            val fixedAppear = available.any { it.appear.any { it == day } }
            val allFixedEvents = if (fixedAppear) {
                /**
                 * 如果这一年，存在appear和这一年相等的event，
                 * 筛选出来，直接随机，比如第1年，因为存在appear==1的event，全部筛选出来，随机3个，不需要看same
                 */
                available.filter { it.appear.any { it == day } }
                    .map { it.createUUID() }
                    .toList().shuffled(RANDOM).let {
                        // same控制最终数量
                        it.take(if (DebugManager.validEventsAll) 999 else if (it.first().same == 3) 1 else if (it.first().same == 2) 2 else 3)
                    }
            } else emptyList()
            allFixedEvents.ifEmpty {
                /**
                 * 如果这一年，不存在appear和这一年相等的event，
                 * 则从所有appear == -1的events里随机选一个，
                 * 再根据随机到的这个event的same和play，选3个
                 * 注意same==1则要求play相同
                 * same==0则不要求
                 */
                val innerList = available
                    .filter { it.appear.any { it == 0 } }
                    .map { it.createUUID() }
                    .toList().shuffled(RANDOM)
                if (DebugManager.validEventsAll) innerList else {
                    selectEvents(innerList.toMutableList())
                }
            }
        }

        val events = allEvents.take(if (DebugManager.validEventsAll) 999 else EVENT_SIZE)
        selectionEvents.clear()
        selectionEvents.addAll(events)
        return events.map { it.createUUID() }
    }

    fun selectEvents(innerList: MutableList<Event>, targetCount: Int = 3): List<Event> {
        val selectedEvents = mutableListOf<Event>()
        var realCount = targetCount

        while (selectedEvents.size < realCount && innerList.isNotEmpty()) {
            val selectedEvent = selectEventByWeight(innerList)
            selectedEvents.add(selectedEvent)

            // 更新列表基于selectedEvent的属性
            if (selectedEvent.same != 0) {
                // 删除与selectedEvent play不同的事件
                if (selectedEvent.same != -1) {
                    innerList.removeAll { it.play != selectedEvent.play }
                }
            } else {
                // 删除与selectedEvent play相同的事件以及所有same为1的事件
                innerList.removeAll { it.play == selectedEvent.play || it.same == 1 }
            }
            // 移除当前选出来的事件
            innerList.removeAll { it.uuid == selectedEvent.uuid }

            // same控制最终数量
            if (selectedEvent.same == 3) realCount = 1
            else if (selectedEvent.same == 2) realCount = 2
        }

        return selectedEvents
    }

    fun selectEventByWeight(events: List<Event>): Event {
        val groups = events.groupBy { it.play }.entries
        val totalWeight = groups.sumOf { it.value.first().weight }

        if (totalWeight == 0) return events.shuffled().first()
        val randomWeight = RANDOM.nextInt(totalWeight) + 1

        var currentWeight = 0
        for (group in groups) {
            currentWeight += group.value.first().weight
            if (randomWeight <= currentWeight) {
                val event = events.filter { it.play == group.key }.shuffled(RANDOM).first()
                return event
            }
        }

        throw IllegalStateException("Should never reach here if the list is not empty")
    }

    suspend fun selectEvent(event: Event): Boolean {
        if (getUsedEvents().any { it.selectAge == BattleManager.getAge() } && !DebugManager.debug) return false
        if (event.isSelectGroup() && getUsedEvents().any { it.isSelectGroup() }) return false
        if (!DebugManager.repeatEvent && event.isRepeat == 0 && getUsedEvents().any { it.id == event.id }) return false
        if (selectedEvent.value?.selectAge == BattleManager.getAge() && !DebugManager.debug) return false
        return if (triggerEvent(event)) {
            // 条件满足，根据玩法进行游戏
            goto(EVENT_DETAIL_SCREEN)
            selectedEvent.value = event.copy(selectAge = BattleManager.getAge())
            ContinueManager.selectEvent(event)

            doEvent(event)
            playerMusicByScreen()
            true
        } else {
            // 条件不满足
            GameApp.instance.getWrapString(R.string.event_not_ready).toast()
            false
        }
    }

    fun getOrCreateHandler(event: Event): PlayHandler {
        val handler = playHandlers[event.id]
        return if (handler == null) {
            val newHandler = eventHandlerClasses[event.play]?.invoke() ?: NonePlayHandler()
            playHandlers[event.id] = newHandler
            newHandler
        } else {
            handler
        }
    }

    private suspend fun doEvent(event: Event) {
        onTaskEnterEvent(event)
        oneBattleContinueFightCount = 0
        repo.onBattleInfo(
            "\n" + GameApp.instance.getWrapString(
                R.string.choose_event1, BattleManager.adventureProps.value.age, event.name
            ),
            BattleInfoType.ExtraSkill
        )
        adventureSkillTrigger(triggerSkill = EnterEvent.copy(mainId = event.play))
        BattleManager.onEventSelect()
        getOrCreateHandler(event).eventSelect(event)
    }

    fun doEventBattleResult(
        event: Event?,
        result: Boolean,
        forceQuit: Boolean = false,
        forceKill: Boolean = true
    ) {
        if (event == null) return
        if (getOrCreateHandler(event).eventFinished.value) return

        // 战斗失败
        BattleManager.onBattleEnd()
        playerMusicByScreen() // 音乐
        GameApp.globalScope.launch(Dispatchers.Main) {
            // 需要杀死你的出战军团卡，因为可能你强行退出战斗了
            if (forceKill) {
                repo.battle.value.getAllPlayers().filter { !it.isDeath() }.forEach {
                    BattleManager.updateAllyInGameById(it, 0)
                }
            }
            BattleManager.checkAnyAllyDied()
            if (!forceQuit && event.isMainLine == 1 && !result && BattleManager.canKeepFight(event)
                && oneBattleContinueFightCount < 2
            ) {
                // 继续战斗，判定还有没有盟友，不需要要做啥
                GameApp.instance.getWrapString(R.string.keep_fight_tips).toast()
                oneBattleContinueFightCount += 1
            } else {
                getOrCreateHandler(event).apply {
                    eventFinished.value = true
                    eventResult.value = result
                    doEventResult(event, result)
                }
            }
        }
    }

    fun doEventResult(event: Event, result: Boolean) {
        GuideManager.nonBattleEventShowed.value = true
        if (event.dialogType == 1) {
            // 1=普通对话时
            goNextEvent(event, result)
        } else if (event.dialogType == 2 && result) {
            // 结束后对话
        } else if (event.dialogType == 3 && !result) {
            // 失败后对话
        } else {
            goNextEvent(event, result)
        }
    }

    fun goNextEvent(event: Event, result: Boolean) {
        if (eventRecorder.addResult(event, result)) {
            // 这里说明不可重复事件重复进行了结算，直接跳回事件选择页面，极端情况，防止卡死
            GameApp.globalScope.launch(Dispatchers.Main) {
                gotoNextEvent(event, false)
            }
            return
        }
        GameApp.globalScope.launch(Dispatchers.Main) {
            repo.onBattleInfo(
                "【${event.name}】${GameApp.instance.getWrapString(R.string.events)}${
                    if (result) GameApp.instance.getWrapString(R.string.win) else GameApp.instance.getWrapString(
                        R.string.lose
                    )
                }", BattleInfoType.ExtraSkill
            )
            if (result) {
                // 其他事件在弹窗里跳转
                Dialogs.eventPassDialog.value = event
            } else {
                Dialogs.eventFailDialog.value = event
            }
        }
    }

    fun getUsedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.usedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.usedEvents
    }

    fun getSucceededEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.succeededEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.succeededEvents
    }

    fun getFailedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.failedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.failedEvents
    }

    suspend fun gotoNextEvent(
        event: Event?,
        pass: Boolean,
        agePlus: Int = 1,
        surrender: Boolean = false,
    ) {
        BattleManager.onNextEvent()
        selectionEvents.clear()
        if (getUsedEvents().isNotEmpty()) {
            if (pass) {
                adventureSkillTrigger(triggerSkill = SucceededEvent.copy(mainId = event?.play ?: 0))
            } else {
                adventureSkillTrigger(triggerSkill = FailedEvent.copy(mainId = event?.play ?: 0))
            }
        }
        val story = EndingManager.saveEnding(
            BattleManager.you.value,
            eventRecorder.usedEvents,
            pass
        )
        if (pass && event != null) {
            onTaskDoneEvent(event)
        }
        event?.let {
            onTaskEnding(it, pass)
        }
        if (!pass && (event?.isMainLine == 1 || StoryManager.selectedEndless())) {
            EndingManager.ending(story)
        } else if (event?.isEnd == true && ((pass && event.endType == 1) || (!pass && event.endType == 2))) {
            EndingManager.ending(story)
        } else if (BattleManager.adventureProps.value.age > 0 && BattleManager.getGameAllies()
                .all { it.isDead() }
        ) {
            EndingManager.ending(story)
        } else if (BattleManager.getAge() == StoryManager.getMaxAge()) {
            DifficultManager.pass()
            EndingManager.ending(story)
        } else if (surrender) {
            EndingManager.ending(story)
        } else {
            if (getUsedEvents().isNotEmpty()) {
                adventureSkillTrigger(triggerSkill = AgeEvent)
                BattleManager.gainAdventureProp(AdventureProps(age = agePlus))
                repeat(agePlus) {
                    BattleManager.oneYearPass()
                }
                onTaskAge(BattleManager.adventureProps.value.age)
                BattleManager.you.value.markSkillNewTurn(BattleManager.you.value)
                BattleManager.you.value.clearGrave(Timing.TurnBegin)
                if ((BattleManager.getAge()) in reportAges) {
                    ReportManager.onDungeonProgress(day = BattleManager.getAge())
                }
            } else {
                adventureSkillTrigger(triggerSkill = StartGame)
            }
            BattleManager.updateYou()

            if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                GameReviewManager.checkTriggerReviewDialog(BattleManager.getAge())
            }
            val events = getNextEvents()
            if (BattleManager.adventureProps.value.age > 0) {
                ContinueManager.onSelections(events)
            }
            goto(EVENT_SELECT_SCREEN)
            playerMusicByScreen()
        }
    }

    fun extraIsGameFailed(): Boolean {
//        selectedEvent.value?.play?.takeIf { it == SIEGE_BATTLE }?.let {
//            return repo.battleTurn.intValue > (selectedEvent.value?.playPara1?.get(1) ?: 0)
//        }
        if (repo.gameMode.value.isTowerMode() && TowerManager.targetLevel.value.type.first() == 4) {
            return repo.battleTurn.intValue > (TowerManager.targetLevel.value.playPara1.first())
        }
        return false
    }

    fun extraIsGameWin(): Boolean {
//        selectedEvent.value?.play?.takeIf { it == PROTECT_BATTLE }?.let {
//            return repo.battleTurn.intValue > (selectedEvent.value?.playPara1?.get(1) ?: 0)
//        }
        return false
    }

    fun resetEventRecorder(used: List<Event>, success: List<Event>, failed: List<Event>) {
        eventRecorder.resetEvents(used, success, failed)
    }
}