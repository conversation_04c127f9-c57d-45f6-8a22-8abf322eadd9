package com.moyu.chuanqirensheng.logic.skill

import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.GameCore
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.toAward
import com.moyu.core.util.chance
import kotlin.math.abs

object ExtraSkillTrigger {
    fun trigger(
        skill: Skill,
        skillOwner: Role,
        triggeringSkill: Skill?
    ): Boolean {
        skill.apply {
            val conditionTrigger = mutableListOf<Boolean>()
            // 概率可以被技能影响，根据类型先获得最终的实际概率
            val realRate = skillOwner.getChance(this, triggerType, rate)

            // 如果没有roll中，则返回
            if (!realRate.chance() && !GameCore.instance.getDebugConfig().easySkill) return false

            // roll中了，还要看条件，如果有的话
            activeCondition.forEachIndexed { index, condition ->
                val conditionResult = when (abs(condition)) {
                    10000 -> {
                        // 进入事件时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isEnterEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    10100 -> {
                        // 事件成功时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isSucceededEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    10200 -> {
                        // 事件失败时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isFailedEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    in 10300..10399 -> {
                        // 事件累计完成x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10300
                        triggeringSkill?.isSucceededEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getSucceededEvents(skill.gainAge)
                            .filter {
                                it.play == activeConditionNum[index] || activeConditionNum[index] == 0
                            }.size.let {
                                it > 0 && it % x == 0
                            }
                    }

                    in 10400..10499 -> {
                        // 事件累计失败x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10400
                        triggeringSkill?.isFailedEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getFailedEvents(skill.gainAge)
                            .filter { it.play == activeConditionNum[index] || activeConditionNum[index] == 0 }.size.let {
                                it > 0 && it % x == 0
                            }
                    }

                    in 10500..10599 -> {
                        // 事件累计完成x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10500
                        (triggeringSkill?.isSucceededEvent() == true || triggeringSkill?.isFailedEvent() == true)
                                && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play == activeConditionNum[index] || activeConditionNum[index] == 0 }.size.let {
                               it > 0 && it % x == 0
                            }
                    }

                    10600 -> {
                        (triggeringSkill?.isSucceededEvent() == true || triggeringSkill?.isFailedEvent() == true)
                                && triggeringSkill.id == activeConditionNum[index]
                    }

                    10700 -> {
                        // 所有商店购买物品时
                        triggeringSkill?.isBuyGoods() == true
                    }

                    10800 -> {
                        // 累计进行任意种类战斗时（只要是战斗类事件都可触发）（可反复触发）
                        triggeringSkill?.isBattleEvent() == true
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    10801 -> {
                        // 累计进行任意种类战斗时（只要是战斗类事件都可触发）（只能触发一次）
                        triggeringSkill?.isBattleEvent() == true
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size >= activeConditionNum[index]
                    }

                    10802 -> {
                        // 累计胜利x次任意种类战斗时（只要是战斗类事件都可触发）（可反复触发）
                        triggeringSkill?.isWinBattleEvent() == true
                                && EventManager.getSucceededEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    10803 -> {
                        // 累计胜利x次任意种类战斗时（只要是战斗类事件都可触发）（只能触发一次）
                        triggeringSkill?.isWinBattleEvent() == true
                                && EventManager.getSucceededEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size >= activeConditionNum[index]
                    }

                    10804 -> {
                        // 累计失败x次任意种类战斗时（只要是战斗类事件都可触发）（可反复触发）
                        triggeringSkill?.isFailedBattleEvent() == true
                                && EventManager.getFailedEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    10805 -> {
                        // 累计失败x次任意种类战斗时（只要是战斗类事件都可触发）（只能触发一次）
                        triggeringSkill?.isFailedBattleEvent() == true
                                && EventManager.getFailedEvents(skill.gainAge)
                            .filter { it.play in battleEventIds }.size >= activeConditionNum[index]
                    }

                    10810 -> {
                        // 累计进行任意种类陆上事件时（可反复触发）
                        triggeringSkill?.isLandEvent() == true
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play in landEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    10811 -> {
                        // 累计进行任意种类海上事件时（可反复触发）
                        triggeringSkill?.isSeaEvent() == true
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play in seaEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    10812 -> {
                        // 累计进行任意种类地下事件时（可反复触发）
                        triggeringSkill?.isDungeonEvent() == true
                                && EventManager.getUsedEvents(skill.gainAge)
                            .filter { it.play in dungeonEventIds }.size.let {
                                it > 0 && it % activeConditionNum[index] == 0
                            }
                    }

                    11001 -> {
                        // x岁时（参数位置填写岁数）
                        triggeringSkill?.isAgeEvent() == true && BattleManager.getAge() == activeConditionNum[index]
                    }

                    11002 -> {
                        // 年龄每增长x岁时
                        triggeringSkill?.isAgeEvent() == true
                                // 这一条的意思是，除非是每天都触发，否则获得的当天不触发，因为当天整除都是0，都满足
                                && (activeConditionNum[index] == 1 || BattleManager.getAge() != gainAge)
                                && (BattleManager.getAge() - gainAge) % activeConditionNum[index] == 0
                    }

                    11003 -> {
                        // 小于x岁时（负号表示大于等于）
                        if (activeConditionNum[index] > 0) {
                            triggeringSkill?.isAgeEvent() == true && BattleManager.getAge() < activeConditionNum[index]
                        } else {
                            triggeringSkill?.isAgeEvent() == true && BattleManager.getAge() >= -activeConditionNum[index]
                        }
                    }

                    12000 -> {
                        // 持有至少1个指定ID的物品时
                        BattleManager.skillGameData.any { it.mainId == activeConditionNum[index] }
                                || BattleManager.allyGameData.any { it.mainId == activeConditionNum[index] }
                    }

                    12001 -> {
                        // 没有指定ID的物品时
                        BattleManager.skillGameData.none { it.mainId == activeConditionNum[index] }
                    }

                    12100 -> {
                        // 持有某一种物品至少达到要求数量时，物品种类和数量要求配到pool里
                        BattleManager.haveMoreThan(
                            repo.gameCore.getPoolById(
                                activeConditionNum[index]
                            )
                        )
                    }

                    12101 -> {
                        // 累计获得某一种物品至少达到要求数量时，物品种类和数量要求配到pool里（可反复触发）
                        DetailProgressManager.gainedMoreThanRepeatable(
                            repo.gameCore.getPoolById(
                                activeConditionNum[index]
                            ), skill.gainAge
                        )
                    }

                    12102 -> {
                        // 累计获得某一种物品至少达到要求数量时，物品种类和数量要求配到pool里（只能触发一次）
                        DetailProgressManager.gainedMoreThanOneTime(
                            repo.gameCore.getPoolById(
                                activeConditionNum[index]
                            ), skill.gainAge
                        )
                    }

                    12103 -> {
                        // todo 未使用：累计消耗某一种物品至少达到要求数量时，物品种类和数量要求配到pool里（可反复触发）
                        val pool = repo.gameCore.getPoolById(activeConditionNum[index])
                        DetailProgressManager.consumedMoreThanRepeatable(triggeringSkill, pool)
                    }

                    12104 -> {
                        // todo 未使用：累计消耗某一种物品至少达到要求数量时，物品种类和数量要求配到pool里（只能触发一次）
                        val award = repo.gameCore.getPoolById(activeConditionNum[index]).toAward()
                        DetailProgressManager.consumedMoreThanOneTime(award)
                    }
                
               

                    in 23001..23010 -> {
                        // 玩家的冒险属性i大于等于x点时（Num负号表示小于）
                        val indexFromZero = abs(condition) - 23001
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.getValueByIndex(indexFromZero) < abs(
                                activeConditionNum[index]
                            )
                        } else {
                            BattleManager.adventureProps.value.getValueByIndex(indexFromZero) >= activeConditionNum[index]
                        }
                    }

                    24100 -> {
                        // 指定声望的等级提高时
                        triggeringSkill?.isReputationLevelUp(activeConditionNum[index]) == true
                    }

                    24200 -> {
                        // 指定声望的等级降低时
                        triggeringSkill?.isReputationLevelDown(activeConditionNum[index]) == true
                    }

                    24300 -> {
                        // 获得指定声望的声望值时（无论+还是-）
                        triggeringSkill?.isReputationUp(activeConditionNum[index]) == true
                                || triggeringSkill?.isReputationDown(activeConditionNum[index]) == true
                    }

                    25000 -> {
                        // 每击败x个敌人时（可反复触发）
                        triggeringSkill?.isDefeatEnemy(0) == true
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.filter { it.addAge >= skill.gainAge }
                            .isNotEmpty()
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.size % activeConditionNum[index] == 0
                    }

                    in 25001..25009 -> {
                        // 每击败x个人类/恶魔/亡灵/野兽敌人时（可反复触发）
                        val targetRace = abs(condition) - 25000
                        triggeringSkill?.isDefeatEnemy(targetRace) == true
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.size > 0
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.filter {
                            it.type == targetRace && it.addAge >= skill.gainAge
                        }.size % activeConditionNum[index] == 0
                    }

                    25100 -> {
                        // 累计击败x个敌人时（只触发一次）
                        triggeringSkill?.isDefeatEnemy(0) == true
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.size > 0
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.size >= activeConditionNum[index]
                    }

                    in 25101..25109 -> {
                        // 累计击败x个人类/恶魔/亡灵/野兽敌人时（只触发一次）
                        val targetRace = abs(condition) - 25100
                        triggeringSkill?.isDefeatEnemy(targetRace) == true
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.size > 0
                                && DetailProgressManager.detailProgressData.defeatEnemyRecord.filter {
                            it.type == targetRace && it.addAge >= skill.gainAge
                        }.size >= activeConditionNum[index]
                    }

                    26000 -> {
                        // 每失去x个盟友卡时（可反复触发）
                        triggeringSkill?.isLoseAllyCard(0) == true
                                && DetailProgressManager.detailProgressData.lostAllyRecord.size > 0
                                && DetailProgressManager.detailProgressData.lostAllyRecord.size % activeConditionNum[index] == 0
                    }

                    in 26001..26005 -> {
                        // 每失去x个人类/恶魔/亡灵/野兽盟友卡时（可反复触发）
                        val targetRace = abs(condition) - 26000
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && DetailProgressManager.detailProgressData.lostAllyRecord.size > 0
                                && DetailProgressManager.detailProgressData.lostAllyRecord.filter {
                            it.type == targetRace && it.addAge >= skill.gainAge
                        }.size % activeConditionNum[index] == 0
                    }

                    26100 -> {
                        // 累计失去x个盟友卡时（只触发一次）
                        triggeringSkill?.isLoseAllyCard(0) == true
                                && DetailProgressManager.detailProgressData.lostAllyRecord.filter { it.addAge >= skill.gainAge }
                            .isNotEmpty()
                                && DetailProgressManager.detailProgressData.lostAllyRecord.size >= activeConditionNum[index]
                    }

                    in 26101..26105 -> {
                        // 累计失去x个人类/恶魔/亡灵/野兽盟友卡时（只触发一次）
                        val targetRace = abs(condition) - 26100
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && DetailProgressManager.detailProgressData.lostAllyRecord.size > 0
                                && DetailProgressManager.detailProgressData.lostAllyRecord.filter {
                            it.type == targetRace && it.addAge >= skill.gainAge
                        }.size >= activeConditionNum[index]
                    }

                    in 27000..27002 -> {
                        // 在【野战】后执行1次[掠夺敌营]（可以反复触发）
                        val type = (abs(condition) - 27000) + 1
                        triggeringSkill?.isBattle1Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord1
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27003..27005 -> {
                        // 在【攻城战】后执行1次[邀功领赏]（可以反复触发）
                        val type = (abs(condition) - 27003) + 1
                        triggeringSkill?.isBattle2Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord2
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27006..27008 -> {
                        // 在【守城战】后执行1次[等候援军]（可以反复触发）
                        val type = (abs(condition) - 27006) + 1
                        triggeringSkill?.isBattle3Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord3
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27009..27011 -> {
                        // 在【武将单挑】后执行1次[斩首]（可以反复触发）
                        val type = (abs(condition) - 27009) + 1
                        triggeringSkill?.isBattle4Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord4
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27012..27014 -> {
                        // 在【增援战】后执行1次[游说加入]（可以反复触发）
                        val type = (abs(condition) - 27012) + 1
                        triggeringSkill?.isBattle5Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord5
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27015..27017 -> {
                        // 在【仙人试炼】后执行1次[战场境界]（可以反复触发）
                        val type = (abs(condition) - 27015) + 1
                        triggeringSkill?.isBattle6Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord6
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }

                    in 27100..27102 -> {
                        // 在【野战】后执行1次[掠夺敌营]（只触发一次）
                        val type = (abs(condition) - 27100) + 1
                        triggeringSkill?.isBattle1Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord1
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27103..27105 -> {
                        // 在【攻城战】后执行1次[邀功领赏]（只触发一次）
                        val type = (abs(condition) - 27103) + 1
                        triggeringSkill?.isBattle2Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord2
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27106..27108 -> {
                        // 在【守城战】后执行1次[等候援军]（只触发一次）
                        val type = (abs(condition) - 27106) + 1
                        triggeringSkill?.isBattle3Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord3
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27109..27111 -> {
                        // 在【武将单挑】后执行1次[斩首]（只触发一次）
                        val type = (abs(condition) - 27109) + 1
                        triggeringSkill?.isBattle4Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord4
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27112..27114 -> {
                        // 在【增援战】后执行1次[游说加入]（只触发一次）
                        val type = (abs(condition) - 27112) + 1
                        triggeringSkill?.isBattle5Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord5
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27115..27117 -> {
                        // 在【仙人试炼】后执行1次[战场境界]（只触发一次）
                        val type = (abs(condition) - 27115) + 1
                        triggeringSkill?.isBattle6Decision(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord6
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }
                    in 27118..27120 -> {
                        // 在【使用城市设施】中使用募兵所（可以反复触发）
                        val type = (abs(condition) - 27118) + 1
                        triggeringSkill?.isUseFacilityRecover(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord7
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size % activeConditionNum[index] == 0
                    }
                    in 27121..27123 -> {
                        // 在【使用城市设施】中使用募兵所（只触发一次）
                        val type = (abs(condition) - 27121) + 1
                        triggeringSkill?.isUseFacilityRecover(type) == true
                                && DetailProgressManager.detailProgressData.specialDecisionRecord7
                            .filter {
                                it.addAge >= skill.gainAge && it.type == type
                            }.size >= activeConditionNum[index]
                    }

                    30000 -> {
                        // 等级达到lv.x时（参数位置填写等级）
                        BattleManager.you.value.getLevel() >= activeConditionNum[index]
                    }

                    30001 -> {
                        // 升级时
                        triggeringSkill?.isLevelUp() == true
                    }

                    30002 -> {
                        // 获得经验值时
                        triggeringSkill?.isGainExp() == true
                    }

                    30003 -> {
                        // 任意建筑等级达到lv.x
                        false
//                        BattleManager.buildingGameData.data.any { it.level >= activeConditionNum[index] }
                    }

//                    30100 -> {
//                        // 仅当冒险技能附着在盟友卡身上时有用，当拥有本技能的盟友自己进入指定类型事件时
//                        isTriggerTheOwner && triggeringSkill?.isEnterEventByType(activeConditionNum[index]) == true
//                    }
//
//                    30101 -> {
//                        // 仅当冒险技能附着在盟友卡身上时有用，当拥有本技能的盟友自己阵亡时
//                        isTriggerTheOwner && triggeringSkill?.isLoseAllyCard(0) == true
//                    }
//
//                    30102 -> {
//                        // 仅当冒险技能附着在盟友卡身上时有用，当拥有本技能的盟友自己被复活时
//                        isTriggerTheOwner && triggeringSkill?.isReliveAllyCard(0) == true
//                    }
//
//                    30103 -> {
//                        // 仅当冒险技能附着在盟友卡身上时有用，当拥有本技能的盟友自己进入任意战斗类事件时（进入表示被选中参与战斗）
//                        isTriggerTheOwner && triggeringSkill?.isEnterBattleEvent() == true
//                    }

                    in 30200..30299 -> {
                        // 若玩家的国家头衔处于1-5时
                        // 部落-城邦-王国-联邦-帝国
//                        val targetRace = abs(condition) - 30200
//                        BattleManager.adventureProps.value.getPopulationLevel().level == targetRace
                        false
                    }

//                    40001 -> {
//                        // 手动使用冒险道具的条件：只能对生命值不高于x%的盟友卡使用
//                        triggeringSkill?.isUseItem(activeConditionNum[index]) == true
//                    }

                    else -> {
                        if (condition != 0) {
                            "请确认是否配置错误，技能triggerType不存在${condition}".toast()
                        }
                        true
                    }
                }
                // 支持配置condition为负数，负数则表示取反
                conditionTrigger.add(if (condition >= 0) conditionResult else !conditionResult)
            }
            // 这里是支持条件逻辑，与或者非
            return if (conditionLogic == 1) conditionTrigger.all { it }
            else conditionTrigger.any { it }
        }
    }
}