package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleLayout
import com.moyu.core.logic.role.positionListAllies
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role

const val GOD_BATTLE_PLAY = 999

class GodBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val playId: Int = GOD_BATTLE_PLAY
) : PlayHandler() {

    val godAlly = mutableStateOf<Ally?>(null)

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event, godAllyUuid = godAlly.value?.uuid)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.forEachIndexed { index, role ->
            // 固定站到中间
            positionMap[positionListEnemy[index]] = role
        }
        repo.setCurrentEnemies(positionMap)


        val poolGod = repo.gameCore.getPoolById(event.playPara1[1])
        poolGod.pool.map { poolValue ->
            repo.gameCore.getAllyPool().first { it.id == poolValue }
        }.first().let { godAlly->
            BattleManager.getBattleAllies()[positionListAllies.last()]?.let {
                // 先把最后一个盟友反选
                BattleManager.godReplacedAlly.value = it
                BattleManager.selectAllyToBattle(it, positionListAllies.last())
            }
            // 临时获得神，把神选中
            this.godAlly.value = BattleManager.gainTempInGame(godAlly)

            BattleManager.selectAllyToBattle(
                this.godAlly.value!!,
                positionListAllies.last(),
            )
        }
    }
}