package com.moyu.chuanqirensheng.logic.battle

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.quest.onTaskStarUp
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.event.createPvpPlayerRole
import com.moyu.chuanqirensheng.logic.event.createTowerRole
import com.moyu.chuanqirensheng.logic.event.detail.GOD_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.event.isNeedMaster
import com.moyu.chuanqirensheng.logic.event.isSingle
import com.moyu.chuanqirensheng.logic.indexToResourceName
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doReverseEffect
import com.moyu.chuanqirensheng.logic.skill.GainEquip
import com.moyu.chuanqirensheng.logic.skill.GainExp
import com.moyu.chuanqirensheng.logic.skill.GainResource
import com.moyu.chuanqirensheng.logic.skill.UseResource
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemory
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemoryList
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.GameCore
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.logic.role.ALLY_ROW2_FIRST
import com.moyu.core.logic.role.ALLY_ROW2_THIRD
import com.moyu.core.logic.role.positionList
import com.moyu.core.logic.role.positionOrderedListAllies
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.GameItem
import com.moyu.core.model.Pool
import com.moyu.core.model.RACE_SIZE
import com.moyu.core.model.Title
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.role.RoleExtraInfo
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdvForAlly
import com.moyu.core.model.skill.isAdvProfession
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isEquipAdv
import com.moyu.core.model.skill.isEquipBattle
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isProfession
import com.moyu.core.model.skill.isTroopSkill
import com.moyu.core.model.whiteList
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import kotlin.math.max

val NO_TITLE = Title(name = GameApp.instance.getWrapString(R.string.no_title))

object BattleManager {
    val battleRolePositions = mutableMapOf<String, Pair<Dp, Dp>>()

    val you = mutableStateOf(Role(roleIdentifier = Identifier.player()))
    val yourTitle = mutableStateOf(NO_TITLE)
    val yourExp = GuardedMemory()

    val skillGameData = mutableStateListOf<Skill>()
    val allyGameData = mutableStateListOf<Ally>()
    val equipGameData = mutableStateListOf<Equipment>()

    val resources = GuardedMemoryList()

    val adventureProps = mutableStateOf(EMPTY_ADV_PROPS)
    val masterProp = mutableStateOf(EMPTY_PROPERTY)
    val battleProp = mutableStateOf(EMPTY_PROPERTY)
    val battleSkillPropMap = mutableMapOf<Int, Property>()
    val battleRaceProps = mutableListOf(EMPTY_PROPERTY)
    val battleEnchants = mutableStateListOf<Skill>()
    val masterSkills = mutableListOf<Skill>()

    val currentBgMusic = mutableIntStateOf(0)
    val godReplacedAlly = mutableStateOf<Ally?>(null)

    val effectedSkills = mutableStateListOf<Skill>()
    var oneBattleContinueFightCount = 0

    fun init() {
        currentBgMusic.intValue = MusicManager.getRandomDungeonMusic()
        battleRolePositions.clear()

        resources.clear()
        resources.addAll(EMPTY_RESOURCES)
        // 开局初始木材
        resources[0] = repo.gameCore.getInitGold()
        resources[1] = repo.gameCore.getInitWood()
        resources[2] = repo.gameCore.getInitStone()

        battleRaceProps.clear()
        repeat(RACE_SIZE) {
            battleRaceProps.add(EMPTY_PROPERTY)
        }

        adventureProps.value = EMPTY_ADV_PROPS

        battleProp.value = EMPTY_PROPERTY
        masterProp.value = EMPTY_PROPERTY
        battleSkillPropMap.clear()

        battleEnchants.clear()
        masterSkills.clear()
        allyGameData.clear()
        skillGameData.clear()
        equipGameData.clear()
        effectedSkills.clear()

        if (!getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
            // 还没完成首次游戏
            repo.gameCore.getFirstAllyIds().map {
                repo.gameCore.getAllyById(it)
            }.forEach {
                allyGameData.add(it.copy(num = 0).copyToGame())
            }
        } else {
            allyGameData.addAll(repo.allyManager.data.filter {
                it.selected
            }.sortedBy { it.selectedTime }.map { it.copy(num = 0).copyToGame() })
        }

        createYou()
        yourTitle.value = NO_TITLE
        yourExp.value = 0
    }

    fun onNewGame() {
        init() // 还要再init一次，改变了选择，要重新梳理下技能和军团卡的关系
    }

    fun createYou() {
        adventureProps.value = AdventureProps.createNew()
        val talents = TalentManager.talents.mapNotNull { talent ->
            repo.gameCore.getSkillPool()
                .firstOrNull { it.mainId == talent.key && it.level == talent.value }
        }
        you.value = Role(
            extraInfo = RoleExtraInfo(allyUuid = UUID.generateUUID().toString()),
            roleIdentifier = Identifier.player()
        ).apply {
            talents.forEach {
                learnSkill(it, roleIdentifier)
            }
        }
        // 英雄自带的冒险技能要算进去
        getGameMaster().getRace().skillId.map { repo.gameCore.getSkillById(it) }.filter { it.isAdventure() }.forEach {
            you.value.learnSkill(it, you.value)
            skillGameData.add(it)
        }
    }

    fun selectAllToGame() {
        allyGameData.clear()
        allyGameData.addAll(repo.allyManager.data.map {
            it.copyToGame()
        })
    }

    fun selectToGame(target: Ally) {
        val index = allyGameData.indexOfFirst { target.id == it.id } // 这里没有重复技能，未进入游戏，没有uuid，用id判定
        if (index != -1) {
            allyGameData.removeAt(index)
            repo.allyManager.selectToGame(target)
        } else {
            if (!target.isHero() && allyGameData.filter { !it.isHero() }.size >= UnlockManager.getInitAllyNum()) {
                GameApp.instance.getWrapString(R.string.carry_to_game, UnlockManager.getInitAllyNum())
                    .toast()
            } else {
                repo.allyManager.selectToGame(target)
                allyGameData.add(target.copyToGame())
            }
        }
    }

    suspend fun gainInGame(target: Ally) {
        // todo 注意，如果是首次获得，数量要-1，本体扣除1
        allyGameData.firstOrNull { it.mainId == target.mainId }?.let {
            allyGameData.indexOfItemInGame(it.uuid) { index ->
                allyGameData[index] = allyGameData[index].copy(num = allyGameData[index].num + target.num, new = true)
            }
        }?: allyGameData.add(target.copyToGame().copy(num = target.num - 1))
        DetailProgressManager.gainInGame(target)
    }

    fun gainTempInGame(target: Ally): Ally {
        val result = target.copyToGame().copy(temp = true)
        allyGameData.add(result)
        return result
    }

    suspend fun gainSkillInGame(target: Skill) {
        val setAgeSkill = if (!target.isAdventure()) target else {
            target.copy(gainAge = adventureProps.value.age)
        }
        setAgeSkill.copyToGame().let {
            if (it.isAdventure()) {
                you.value.apply {
                    if (it.level > 1) {
                        forgetSkill(repo.gameCore.getSkillById(it.id - 1))
                        skillGameData.removeAll { learnedSkill -> learnedSkill.mainId == it.mainId }
                        you.value.getGraveSkills().firstOrNull { it.id == target.id - 1 }?.doReverseEffect()
                    }
                    if (!getSkills().map { it.uuid }.contains(it.uuid)) {
                        // 保护下，防止重复学习同一个技能
                        learnSkill(it, this.roleIdentifier)
                    }
                }
            } else {
                // 战斗技能，只能是主角技能
                if (it.level > 1) {
                    masterSkills.removeAll { learnedSkill -> learnedSkill.mainId == it.mainId }
                    skillGameData.removeAll { learnedSkill -> learnedSkill.mainId == it.mainId }
                }
                if (masterSkills.map { it.id }.contains(it.id)) {
                    masterSkills.removeAll { learnedSkill -> learnedSkill.id == it.id }
                    skillGameData.removeAll { learnedSkill -> learnedSkill.id == it.id }
                }
                masterSkills.add(it)
            }
            if (!skillGameData.map { it.uuid }.contains(it.uuid)) {
                skillGameData.add(it)
                DetailProgressManager.gainInGame(it)
            }
        }
    }

    suspend fun dropFromGame(target: Ally, needTrigger: Boolean = true) {
        if (target.isMaster()) {
            // 主角死亡，直接下阵
            if (isAllyInBattle(getGameMaster())) {
                selectAllyToBattle(getGameMaster(), -1)
            }
        } else {
//            skillGameData.filter { it.equipAllyUuid == target.uuid }.forEach {
//                skillGameData.indexOfItemInGame(it.uuid) { index ->
//                    //bug，遇到交换军团卡的事件，如果不取下军团卡身上的技能，交换军团卡之后军团卡身上技能就没了，这种情况改成自动卸下吧
//                    skillGameData[index] = skillGameData[index].copy(equipAllyUuid = "")
//                }
//            }
            allyGameData.removeAll { it.uuid == target.uuid }
            if (needTrigger) {
                DetailProgressManager.dropFromGame(target)
            }
        }
    }

    suspend fun dropFromGame(target: Skill) {
        skillGameData.removeAll { it.uuid == target.uuid }
        DetailProgressManager.dropInGame(target)
        you.value.apply {
            forgetSkill(target)
        }
    }

    fun troopSkill(allyUuid: String, skill: Skill) {
//        masterSkills[allyUuid] = skill
    }

    fun upgradeAlly(ally: Ally) {
        allyGameData.filter { it.id == ally.id }.forEach {
            allyGameData.indexOfItemInGame(it.uuid) { index ->
                allyGameData[index] = allyGameData[index].upgrade()
            }
        }
    }

    fun getGameAllies(): List<Ally> {
        return allyGameData.sortedByDescending { it.battlePosition }
    }

    fun getGameAlliesNoMaster(): List<Ally> {
        return allyGameData.filterNot { it.isMaster() }
    }

    fun getAliveGameAllies(): List<Ally> {
        return allyGameData.filter {
            !it.isDead()
        }
    }

    fun getBattleAllies(): Map<Int, Ally> {
        val mutableMap = mutableMapOf<Int, Ally>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = it
        }
        return mutableMap
    }

    fun getBattleRoles(capacity: Int): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        if (capacity == 1) {
            allyGameData.firstOrNull { it.battlePosition == ALLY_ROW1_SECOND }?.let {
                mutableMap[ALLY_ROW1_SECOND] = getRoleByAlly(it)
            }
        } else {
            allyGameData.filter { it.battlePosition >= 0 }.forEach {
                mutableMap[it.battlePosition] = getRoleByAlly(it)
            }
        }
        return mutableMap
    }

    fun getPvpBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        val heroAdvSkills = allyGameData.first { it.isHero() && it.battlePosition >= 0 }.let {
            it.getRace().skillId.filter {
                repo.gameCore.getSkillById(it).isAdvForAlly() || repo.gameCore.getSkillById(it).isAdvProfession()
            }
        }
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createPvpPlayerRole(it.getRace(), TalentManager.talents, heroAdvSkills?: emptyList())
        }
        return mutableMap
    }

    fun getWorldBossBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        val heroAdvSkills = allyGameData.first { it.isHero() && it.battlePosition >= 0 }.let {
            it.getRace().skillId.filter {
                repo.gameCore.getSkillById(it).isAdvForAlly() || repo.gameCore.getSkillById(it).isAdvProfession()
            }
        }
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createPvpPlayerRole(it.getRace(), TalentManager.talents, heroAdvSkills?: emptyList())
        }
        return mutableMap
    }

    fun getTowerBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        val heroAdvSkills = allyGameData.first { it.isHero() && it.battlePosition >= 0 }.let {
            it.getRace().skillId.filter {
                repo.gameCore.getSkillById(it).isAdvForAlly() || repo.gameCore.getSkillById(it).isAdvProfession()
            }
        }
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createTowerRole(it.getRace(), TalentManager.talents, heroAdvSkills?: emptyList())
        }
        return mutableMap
    }

    fun selectAllyToBattle(ally: Ally, position: Int) {
        if (isAllyInBattle(ally)) {
            if (ally.temp) {
                GameApp.instance.getWrapString(R.string.god_need_tips).toast()
            } else {
                allyGameData.indexOfItemInGame(ally.uuid) {
                    allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
                }
            }
        } else if (position !in positionList && position != -1) {
            return
        } else if (allyGameData.any { it.battlePosition == position }) {
            allyGameData.indexOfItemInGame(allyGameData.first { it.battlePosition == position }.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
            }
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        } else if (ally.isHero() && allyGameData.any { it.battlePosition > 0 && it.isHero() }) {
            GameApp.instance.getWrapString(R.string.only_one_hero_tips).toast()
        } else {
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        }
    }

    suspend fun checkAnyAllyDied() {
        // 不可以直接foreach，因为要改变list内容
        allyGameData.filter { it.isDead() }.map { it.uuid }.forEach {
            allyGameData.indexOfItemInGame(it) { index ->
                if (allyGameData[index].isDead()) {
                    dropFromGame(allyGameData[index])
                }
            }
        }
    }

    fun updateAllyInGameById(role: Role, hp: Int) {
        allyGameData.indexOfItemInGame(role.extraInfo.allyUuid) { index ->
            val minHp = if (hp > 0) 1 else 0
            allyGameData[index] =
                allyGameData[index].copy(gameHp = if (hp >= role.getOriginProperty().hp) 100 else max(minHp, hp * 100 / role.getOriginProperty().hp))
        }
    }

    fun reliveAllyInGame(ally: Ally, relive: Boolean = true) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].relive()
        }
        if (relive) {
            GameCore.instance.onBattleEffect(SoundEffect.ReliveAlly)
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.HealAlly)
        }
    }

    fun healBattleAllyInGame(percent: Int) {
        getBattleAllies().values.forEach { ally ->
            allyGameData.indexOfItemInGame(ally.uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        if (getGameMaster().isDead()) {
            allyGameData.indexOfItemInGame(getGameMaster().uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    fun healAllAllyInGame(percent: Int) {
        allyGameData.forEachIndexed { index, _ ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    fun healOneAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    suspend fun hurtAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].hurt(percent)
            if (allyGameData[index].isDead()) {
                dropFromGame(allyGameData[index])
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Damage1)
    }

    fun getGameSkills(): List<Skill> {
        return skillGameData
    }

    fun getRoleByAlly(ally: Ally): Role {
        if (repo.inGame.value) {
            val extraProperty =
                battleProp.value + (battleRaceProps.getOrNull(ally.getRaceType() - 1)?: Property())
            val masterProperty = if (ally.mainId == getGameMaster().mainId) {
                masterProp.value
            } else {
                Property()
            }
            val masterEquipProperty = if (ally.isMaster()) {
                getGameEquips().map { it.getProperty() }
                    .reduceOrNull { acc, property -> acc + property }
                    ?: Property()
            } else {
                Property()
            }

            return DefaultAllyCreator.create(
                race = repo.gameCore.getRaceById(ally.id),
                identifier = Identifier.player(name = ally.name),
                diffProperty = extraProperty
                        + masterEquipProperty
                        + masterProperty
                        + (ally.exerciseProperty ?: Property()) // 附加属性加上
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    // 盟友不会装备技能，没有这个逻辑
//                    ally.equipSkills.forEach {
//                        learnSkill(it, this.roleIdentifier)
//                    }

                    if (ally.isMaster()) {
                        masterSkills.forEach {
                            learnSkill(it, roleIdentifier)
                        }
                        // 装备里的战斗技能要弄上去
                        equipGameData.filter { it.skillEffect != 0 }.map { repo.gameCore.getSkillById(it.skillEffect) }
                            .filter { it.isEquipBattle() }.forEach {
                                learnSkill(it, roleIdentifier)
                            }
                    }

                    val extraSkillProp = getSkills().mapNotNull {
                        battleSkillPropMap[it.id]
                    }.reduceOrNull { acc, property -> acc + property } ?: Property()
                    setInitProperty(getInitProperty() + extraSkillProp)
                    setPropertyToDefault()
                    setCurrentHp(Integer.max(1, this.getOriginProperty().hp * ally.gameHp / 100))
                }
        } else {
            return DefaultAllyCreator.create(
                repo.gameCore.getRaceById(ally.id),
                Property(),
                identifier = Identifier.player(name = ally.name)
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    setPropertyToDefault()
                }
        }
    }

    fun getGameRoles(): List<Role> {
        return getGameAllies().filterNot { it.isDead() }.map {
            getRoleByAlly(it)
        }
    }

    fun onEventSelect() {
        allyGameData.removeAll { it.temp }
    }

    fun onBattleEnd() {
        allyGameData.removeAll { it.temp }
        godReplacedAlly.value?.let { ally ->
            allyGameData.firstOrNull { it.uuid == ally.uuid }?.let {
                selectAllyToBattle(it, ALLY_ROW2_THIRD)
            }
        }
        godReplacedAlly.value = null
    }

    fun onNextEvent() {
        // todo 闪退保护
        try {
            if (getGameMaster().isDead()) {
                allyGameData.indexOfItemInGame(getGameMaster().uuid) {
                    // 主角不会死，保留1点血
                    allyGameData[it] = allyGameData[it].copy(gameHp = 1)
                }
            }
        } catch (e: Exception) {
            e.message?.toast()
            e.printStackTrace()
        }
        // 防止数据过大
        battleRolePositions.clear()
    }

    fun isAllyInBattle(ally: Ally): Boolean {
        return allyGameData.any { it.uuid == ally.uuid && it.battlePosition >= 0 }
    }

    fun onPermanentDiff(target: Role, diff: Property) {
        allyGameData.indexOfItemInGame(target.extraInfo.allyUuid) {
            val newProperty = (allyGameData[it].exerciseProperty ?: Property()) + diff
            allyGameData[it] = allyGameData[it].copy(exerciseProperty = newProperty)
        }
    }

    fun setAllyUnNew() {
        val temp = allyGameData.toList()
        allyGameData.clear()
        allyGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setAllyUnNew(ally: Ally) {
        allyGameData.indexOfItemInGame(ally.uuid) {
            allyGameData[it] = allyGameData[it].copy(new = false)
        }
    }

    fun setSkillUnNew(filter: (Skill) -> Boolean) {
        val temp = skillGameData.filter { filter(it) }.toList()
        skillGameData.removeAll { filter(it) }
        skillGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setSkillUnNew(skill: Skill) {
        skillGameData.indexOfItemInGame(skill.uuid) {
            skillGameData[it] = skillGameData[it].copy(new = false)
        }
    }

    fun setEquipUnNew() {
        val temp = equipGameData.toList()
        equipGameData.clear()
        equipGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setEquipUnNew(equipment: Equipment) {
        equipGameData.indexOfItemInGame(equipment.uuid) {
            equipGameData[it] = equipGameData[it].copy(new = false)
        }
    }

    fun oneYearPass() {
        skillGameData.map {
            it.nextYear()
        }.let {
            skillGameData.clear()
            skillGameData.addAll(it)
        }
        you.value.oneYearPass()
    }

    fun gainBattleProp(property: Property) {
        if (property.getNonZeroString().isNotEmpty()) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_battle_prop) + property.getNonZeroString(),
                BattleInfoType.ExtraSkill
            )
        }
        masterProp.value += property
    }

    suspend fun gainAdventureProp(property: AdventureProps) {
        if (property.getNonZeroString().isNotEmpty()) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_adv_prop) + property.getNonZeroString(),
                BattleInfoType.ExtraSkill
            )
        }
        withContext(Dispatchers.Main) {
            adventureProps.value += property
            adventureProps.value = adventureProps.value.ensureNonNegative()
        }
    }

    fun gainPermanentProp(propertyByEnum: Property) {
        battleProp.value += propertyByEnum
    }

    fun gainPermanentRaceProp(race: Int, property: Property) {
        battleRaceProps[race] = battleRaceProps[race] + property
    }

    fun gainPermanentSkillProp(skillId: Int, property: Property) {
        battleSkillPropMap[skillId] = (battleSkillPropMap[skillId] ?: EMPTY_PROPERTY) + property
    }

    fun getGameMaster(): Ally {
        return (if (StoryManager.selectedEndless()) {
            allyGameData.firstOrNull()
        } else {
//            allyGameData.first { it.id == StoryManager.getSelectStory().id }
            allyGameData.firstOrNull { it.isHero() }
        })?: repo.gameCore.getAllyPool().first { it.isHero() }
    }

    fun Ally.isMaster(): Boolean {
        return if (StoryManager.selectedEndless()) {
            this.uuid == allyGameData.first().uuid
        } else {
            this.isHero()
//            this.id == StoryManager.getSelectStory().id
        }
    }

    fun getGameMasterRole(): Role {
        return getRoleByAlly(getGameMaster())
    }

    fun getGameEquips(): List<Equipment> {
        return equipGameData
    }

    fun haveMoreThan(poolById: Pool): Boolean {
        return false
    }

    fun getAge(): Int {
        return adventureProps.value.age
    }

    suspend fun gainEquip(equipment: Equipment) {
        if (equipment.isEquipReplaceable()) {
            if (equipGameData.any { it.mainId == equipment.mainId }) {
                val currentLevel = equipGameData.first { it.mainId == equipment.mainId }.star
                val newLevel = currentLevel + 1
                repo.gameCore.getEquipPool()
                    .firstOrNull { it.mainId == equipment.mainId && it.star == newLevel }?.let {
                        // 如果装备有冒险技能，要挂到you上
                        if (it.skillEffect != 0) {
                            val skill = repo.gameCore.getSkillById(it.skillEffect)
                            if (skill.isEquipAdv()) {
                                // 如果已经触发了，要先reverse
                                you.value.getSkills().firstOrNull { it.mainId == skill.mainId }?.let {
                                    it.doReverseEffect()
                                    you.value.forgetSkill(it)
                                }
                                you.value.learnSkill(skill.copy(gainAge = adventureProps.value.age), you.value.roleIdentifier)
                            }
                        }
                        equipGameData.removeAll { it.mainId == equipment.mainId }
                        equipGameData.add(it)
                    }
            } else if (equipGameData.any { it.type == equipment.type }) {
                // 如果装备有冒险技能，要挂到you上
                if (equipment.skillEffect != 0) {
                    val skill = repo.gameCore.getSkillById(equipment.skillEffect)
                    val reverseSkill = equipGameData.firstOrNull { it.type == equipment.type }
                    if (skill.isEquipAdv()) {
                        // 如果已经触发了，要先reverse
                        you.value.getSkills().firstOrNull { it.mainId == reverseSkill?.mainId }?.let {
                            it.doReverseEffect()
                            you.value.forgetSkill(it)
                        }
                        you.value.learnSkill(skill.copy(gainAge = adventureProps.value.age), you.value.roleIdentifier)
                    }
                }
                equipGameData.removeAll { it.type == equipment.type }
                equipGameData.add(equipment)
            } else {
                // 如果装备有冒险技能，要挂到you上
                if (equipment.skillEffect != 0) {
                    val skill = repo.gameCore.getSkillById(equipment.skillEffect)
                    if (skill.isEquipAdv()) {
                        you.value.learnSkill(skill.copy(gainAge = adventureProps.value.age), you.value.roleIdentifier)
                    }
                }
                equipGameData.add(equipment)
            }
        } else {
            if (equipGameData.any { it.mainId == equipment.mainId }) {
                val currentLevel = equipGameData.first { it.mainId == equipment.mainId }.star
                val newLevel = currentLevel + 1
                repo.gameCore.getEquipPool()
                    .firstOrNull { it.mainId == equipment.mainId && it.star == newLevel }?.let {
                        // 如果装备有冒险技能，要挂到you上
                        if (it.skillEffect != 0) {
                            val skill = repo.gameCore.getSkillById(it.skillEffect)
                            if (skill.isEquipAdv()) {
                                // 如果已经触发了，要先reverse
                                you.value.getSkills().firstOrNull { it.mainId == skill.mainId }?.let {
                                    it.doReverseEffect()
                                    you.value.forgetSkill(it)
                                }
                                you.value.learnSkill(skill.copy(gainAge = adventureProps.value.age), you.value.roleIdentifier)
                            }
                        }
                        equipGameData.removeAll { it.mainId == equipment.mainId }
                        equipGameData.add(it)
                    }
            } else {
                // 如果装备有冒险技能，要挂到you上
                if (equipment.skillEffect != 0) {
                    val skill = repo.gameCore.getSkillById(equipment.skillEffect)
                    if (skill.isEquipAdv()) {
                        you.value.learnSkill(skill.copy(gainAge = adventureProps.value.age), you.value.roleIdentifier)
                    }
                }
                equipGameData.add(equipment)
            }
        }
        adventureSkillTrigger(triggerSkill = GainEquip)
    }

    fun getUpgradeSkillPool(): List<Pair<String, Skill>> {
        // 获取所有盟友
        val allies = getGameRoles()
        // 尝试获取所有盟友的兵种技能，过滤掉没有兵种技能的盟友
        val allySkills = allies.mapNotNull { ally ->
            ally.getSkills().firstOrNull { it.isTroopSkill() }?.let { skill ->
                Pair(ally, skill)
            }
        }
        // 可用升级
        val targetSkillPool = allySkills.mapNotNull { (ally, currentSkill) ->
            repo.gameCore.getSkillPool().firstOrNull {
                it.mainId == currentSkill.mainId && it.level == currentSkill.level + 1
            }?.let { upgradedSkill ->
                Pair(ally, upgradedSkill)
            }
        }.shuffled(RANDOM).sortedByDescending { pair ->
            if (pair.first.getAlly().isMaster()) 999 else pair.first.getRace().quality
        }.take(3)

        return targetSkillPool.map { Pair(it.first.extraInfo.allyUuid, it.second) }

    }

    fun updateYou() {
        you.value = you.value.copy(updateId = you.value.updateId + 1)
    }

    suspend fun gainExp(exp: Int) {
        val realExp = exp + adventureProps.value.gainExp
        val oldLevel = Title.getTitleLevel(yourExp.value)
        yourExp.value += realExp
        val newLevel = Title.getTitleLevel(yourExp.value)
        repo.onBattleInfo(
            GameApp.instance.getWrapString(R.string.gain_exp) + realExp,
            BattleInfoType.Battle
        )
        // todo 这个实现有点搓
        adventureSkillTrigger(triggerSkill = GainExp)
        if (newLevel != oldLevel) {
            Dialogs.levelUpDialog.value = true
        }
    }

    fun gainTitle(titleId: Int) {
        yourTitle.value = repo.gameCore.getTitlePool().first { it.id == titleId }
        repo.onBattleInfo(
            GameApp.instance.getWrapString(R.string.gain_position_title) + yourTitle.value.name,
            BattleInfoType.Battle
        )
    }

    suspend fun gainResources(resources: List<Int>) {
        resources.forEachIndexed { index, resource ->
            if (resource != 0) {
                BattleManager.resources[index] = BattleManager.resources[index] + resource
            }

            if (resource > 0) {
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_resource) + index.indexToResourceName() + resource,
                    BattleInfoType.ExtraSkill
                )
                adventureSkillTrigger(triggerSkill = GainResource.copy(num = resource))
            } else if (resource < 0) {
                // 失去金币-1000 改为1000
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_resource) + index.indexToResourceName() + -resource,
                    BattleInfoType.ExtraSkill
                )
                adventureSkillTrigger(triggerSkill = UseResource.copy(num = -resource))
            }
        }
    }

    fun getAdventureSkillTips(skill: Skill): String {
        return if (you.value.getGraveSkills().any { it.uuid == skill.uuid }) {
            GameApp.instance.getWrapString(R.string.effected)
        } else if (skill.grave != -1) {
            GameApp.instance.getWrapString(R.string.effect_multiple_times)
        } else {
            GameApp.instance.getWrapString(R.string.not_effect)
        }
    }

    fun oneShotDeselect() {
        getBattleAllies().forEach {
            selectAllyToBattle(it.value, -1)
        }
    }

    fun oneShotSelect(data: SelectAllyData) {
        getBattleAllies().forEach {
            selectAllyToBattle(it.value, -1)
        }
        if (!getGameMaster().isDead()) {
            selectAllyToBattle(
                getGameMaster(),
                ALLY_ROW2_FIRST
            )
        }
        if (data.needGod) {
            godReplacedAlly.value = getBattleAllies()[ALLY_ROW2_THIRD]
            selectAllyToBattle(
                getGameAllies().first { it.temp },
                ALLY_ROW2_THIRD
            )
        }
        val battleAllies = getBattleAllies()
        positionOrderedListAllies.forEach { targetPosition ->
            if (targetPosition !in battleAllies.keys) {
                getGameAllies().asSequence().filter { !it.isHero() }.filter { data.filter(it) }
                    .filter { it.battlePosition == -1 }
                    .sortedByDescending { it.star }
                    .sortedByDescending { it.quality }
                    .firstOrNull { !it.isDead() }
                    ?.let {
                        selectAllyToBattle(it, targetPosition)
                    }
            }
        }
    }

    fun canKeepFight(event: Event): Boolean {
        val helperAlive = if (event.play == GOD_BATTLE_PLAY) {
            getGameAllies().any { it.temp }
        } else {
            true
        }
        val singleMasterAlive = if (event.isNeedMaster() && event.isSingle()) {
            getGameAllies().any { it.isMaster() }
        } else {
            true
        }
        return getAliveGameAllies()
            .isNotEmpty() && helperAlive && singleMasterAlive
    }

    fun getTwoLevelUpAwards(): Collection<Award> {
        val allProfessionSkills = repo.gameCore.getSkillPool().filter { it.isProfession() }
        val group = (skillGameData + you.value.getSkills()).filter { it.isProfession() }.groupBy { it.mainId }
        val learnedProfessionsSize = group.size
        val canLearnMore = learnedProfessionsSize < 10
        val canLearnSkills = allProfessionSkills.filter { it.level == 1 && it.mainId !in group }
        val banSkills = getGameMaster().getRace().banSkillId
        val realCanLearn = canLearnSkills.filter { it.id !in banSkills }
        val canUpgradeSkills = group.values.map { skills ->
            val maxLevel = skills.maxOf { it.level }
            if (maxLevel < 3) {
                repo.gameCore.getSkillPool()
                    .first { it.mainId == skills.first().mainId && it.level == maxLevel + 1 }
            } else null
        }.mapNotNull { it }
        val allSkills = if (canLearnMore) realCanLearn + canUpgradeSkills else canUpgradeSkills
        return allSkills.shuffled(RANDOM).take(2).map {
            Award(skills = listOf(it))
        }
    }

    fun canLearnMagic(skill: Skill, poolId: Int): Boolean {
        // 智慧术 中级智慧术 高级智慧术 todo 写死
        if (masterSkills.map { it.id }.contains(skill.id)) {
            return false
        }
        // 自带的技能，就不要学习了
        if (getGameMaster().getRace().skillId.contains(skill.id)) return false
        if (skill.isMagic()) {
            if (poolId in whiteList) return true
            // todo 简单处理，不然改动太大了，如果是EventDetailDialog，也就是事件详情弹窗存在的时候，不参考智慧术
            if (Dialogs.eventDetailDialog.value != null) return true
            return if (skillGameData.any { it.id == 60113 }) {
                true
            } else if (skillGameData.any { it.id == 60112 }) {
                skill.quality() <= 4
            } else if (skillGameData.any { it.id == 60111 }) {
                skill.quality() <= 3
            } else {
                skill.quality() <= 2
            }
        } else if (skill.isProfession()) {
            if (getGameMaster().getRace().banSkillId.contains(skill.id)) {
                return false
            }
            if (skill.isAdventure()) {
                you.value.getSkills().let { learnedSkills ->
                    if (skill.level == 1 && learnedSkills.none { it.mainId == skill.mainId }) {
                        // 专业技能有前置
                        if (skillGameData.filter { it.isProfession() }.size >= 10) {
                            return false
                        }
                        return true
                    }
                    // 不论多少级，当前等级以及后续等级，如果已经学习了，不显示
                    if (learnedSkills.any { it.mainId == skill.mainId && it.level == skill.level - 1 }) {
                        return true
                    }
                }
            } else {
                masterSkills.let { learnedSkills ->
                    if (skill.level == 1 && learnedSkills.none { it.mainId == skill.mainId }) {
                        // 专业技能有前置
                        if (skillGameData.filter { it.isProfession() }.size >= 10) {
                            return false
                        }
                        return true
                    }
                    // 不论多少级，当前等级以及后续等级，如果已经学习了，不显示
                    if (learnedSkills.any { it.mainId == skill.mainId && it.level == skill.level - 1 }) {
                        return true
                    }
                }
            }
            return false
        }
        return true
    }

    fun upgrade(ally: Ally): Ally? {
        if (ally.starUpNum == 0) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else if (ally.starUpNum > ally.num) {
            GameApp.instance.getWrapString(R.string.card_not_enough).toast()
        } else if (ally.star >= ally.starLimit) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
            restartEffect(dialogEffectState, starUpEffect)
            allyGameData.filter { it.id == ally.id }.forEach {
                allyGameData.indexOfItemInGame(it.uuid) { index ->
                    onTaskStarUp(allyGameData[index], allyGameData[index].star + 1)
                    allyGameData[index] = allyGameData[index].copy(num = allyGameData[index].num - ally.starUpNum).starUp()
                    return allyGameData[index]
                }
            }
        }
        return null
    }

    suspend fun starUpAll() {
        val upgradeInfo = mutableListOf<String>()
        var upgradeCount = 0
        val upgradeResultInfo = mutableListOf<String>()
        Dialogs.commonBlockDialog.value = GameApp.instance.getWrapString(R.string.star_up_all_tips)
        repeat(allyGameData.filter { !it.isHero() }.size) { index ->
            var upgraded = true
            var needCount = false
            val startLevel = allyGameData.filter { !it.isHero() }[index].star
            upgradeInfo.add(
                0, GameApp.instance.getWrapString(
                    R.string.star_up_name_tips, allyGameData.filter { !it.isHero() }[index].name
                )
            )
            while (upgraded) {
                val ally = allyGameData.filter { !it.isHero() }[index]
                when {
                    ally.starUpNum == 0 -> {
                        upgraded = false
                    }

                    ally.starUpNum > ally.num -> {
                        upgraded = false
                    }

                    ally.star >= ally.starLimit -> {
                        upgraded = false
                    }

                    else -> {
                        allyGameData.indexOfFirst { it.id == ally.id }.takeIf { it != -1 }?.let {
                            upgradeInfo.add(
                                0, GameApp.instance.getWrapString(
                                    R.string.star_up_all_info,
                                    allyGameData[it].name,
                                    allyGameData[it].star,
                                    allyGameData[it].star + 1
                                )
                            )
                            onTaskStarUp(allyGameData[index], allyGameData[index].star + 1)
                            allyGameData[it] = allyGameData[it].copy(num = allyGameData[it].num - ally.starUpNum).starUp()
                            needCount = true
                        }
                    }
                }
            }
            if (needCount) {
                upgradeCount += 1
                upgradeResultInfo.add(
                    GameApp.instance.getWrapString(
                        R.string.star_up_all_info,
                        allyGameData.filter { !it.isHero() }[index].name,
                        startLevel,
                        allyGameData.filter { !it.isHero() }[index].star
                    )
                )
            }
            Dialogs.commonBlockDialog.value =
                GameApp.instance.getWrapString(R.string.star_up_all_tips) + "\n" + upgradeInfo.joinToString(
                    "\n"
                )
            delay(20)
        }
        GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
        Dialogs.alertDialog.value = CommonAlert(
            title = GameApp.instance.getWrapString(R.string.upgrade_all_result_title),
            content = GameApp.instance.getWrapString(R.string.upgrade_all_result_content, upgradeCount) + "\n" + upgradeResultInfo.joinToString(
                "\n"
            ),
            onlyConfirm = true,
        )
        delay(500)
        Dialogs.commonBlockDialog.value = null
    }
}

inline fun <T : GameItem> List<T>.indexOfItemInGame(uuid: String, callback: (Int) -> Unit) {
    indexOfFirst { uuid == it.uuid }.takeIf { it != -1 }?.let { index ->
        callback(index)
    }
}