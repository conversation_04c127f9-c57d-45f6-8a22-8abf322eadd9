package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doExtraEffect
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillTrigger
import com.moyu.core.GameCore
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

suspend fun adventureSkillTrigger(triggerSkill: Skill?) {
    withContext(Dispatchers.Main) {
        BattleManager.you.value.getSkills().filter { it.isAdventure() }.sortedBy { it.priority /*按照优先级排序*/ }.forEach { originSkill ->
            var skill = originSkill
            val skillOwner = BattleManager.you.value
            val trigger = ExtraSkillTrigger.trigger(skill, skillOwner, triggerSkill)
            if (!trigger) {
                return@forEach
            }
            val inGrave = skillOwner.getGraveSkills().find { it.id == skill.id && it.uuid == skill.uuid } != null
            if (inGrave) {
                return@forEach
            }
            val needCoolDown = skill.needCoolDown(skillOwner.getRealCoolDown(skill))
            if (needCoolDown) {
                return@forEach
            }
            // 生效后，判定是否临时移除
            // 这里会改变skill，所以后续要用新的skill
            skill = skillOwner.skillMaster.checkIfAddGraveSkill(skill, skillOwner)
            skill = skillOwner.skillMaster.markReCoolDown(skill, skillOwner)
            if (BattleManager.adventureProps.value.age > 0) {
                GameCore.instance.onBattleEffect(SoundEffect.TriggerSkill)
            }
            if (BattleManager.effectedSkills.size >= 30) {
                // 性能优化
                val temp = BattleManager.effectedSkills.takeLast(5)
                BattleManager.effectedSkills.clear()
                BattleManager.effectedSkills.addAll(temp)
            }
            BattleManager.effectedSkills.add(skill)
            skill.doExtraEffect()
        }
    }
}