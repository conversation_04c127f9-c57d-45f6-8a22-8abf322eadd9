package com.moyu.chuanqirensheng.logic.setting

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_DIALOG
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey

object SettingManager {
    val noDialog = mutableStateOf(false)
    fun init() {
        noDialog.value = getBooleanFlowByKey(KEY_MUTE_DIALOG)
    }

    fun switchMuteDialog() {
        noDialog.value = !noDialog.value
        setBooleanValueByKey(KEY_MUTE_DIALOG, noDialog.value)
    }
}