package com.moyu.chuanqirensheng.logic.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.equipGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.core.model.Ally


@Composable
fun AllySlots(capacity: Int = 8, currentSlotIndex: MutableState<Int>) {
    val itemSize = ItemSize.LargeP
    val allyMap = BattleManager.getBattleAllies()
    val rememberRefreshIndex = remember {
        mutableStateOf(0)
    }
    LaunchedEffect(rememberRefreshIndex.value) {
        if (capacity == 1) {
            // 单挑
            currentSlotIndex.value = 1
        } else {
            currentSlotIndex.value = (0..capacity).toList().firstOrNull {
                allyMap[it] == null
            } ?: 0
        }
    }
    FlowRow(
        modifier = Modifier.fillMaxSize(),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 4
    ) {
        repeat(8) { index ->
            Box {
                AllySlot(itemSize, allyMap, currentSlotIndex, index)
                if (capacity == 1 && index != 1) {
                    Box(
                        modifier = Modifier
                            .size(itemSize.frameSize)
                            .background(B50)
                            .clip(RoundedCornerShape(padding4))
                    )
                }
                allyMap[index]?.let {
                    EffectButton(modifier = Modifier
                        .size(itemSize.frameSize / 4)
                        .align(Alignment.TopEnd), onClick = {
                        BattleManager.selectAllyToBattle(it, -1)
                        rememberRefreshIndex.value++
                    }) {
                        Image(
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.FillBounds,
                            painter = painterResource(id = R.drawable.common_exit),
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun AllySlot(
    itemSize: ItemSize,
    equips: Map<Int, Ally>,
    currentSlotIndex: MutableState<Int>,
    index: Int
) {
    Box(
        modifier = Modifier.width(itemSize.frameSize), contentAlignment = Alignment.Center
    ) {
        val gif = remember {
            mutableStateOf(false)
        }
        equips[index]?.let { skill ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(contentAlignment = Alignment.Center) {
                    SingleAllyView(
                        ally = skill,
                        itemSize = itemSize,
                        showName = false,
                        frame = null,
                        showNum = false,
                        showHp = true
                    )
                    GifView(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .size(itemSize.itemSize), gif.value, equipGif.count, equipGif.gif
                    )
                }
            }
        } ?: run {
            gif.value = true
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                IconView(
                    modifier = Modifier.width(itemSize.frameSize),
                    itemSize = itemSize,
                    name = "",
                    frame = R.drawable.battle_choose,
                ) {
                    currentSlotIndex.value = index
                }
            }
        }
        if (index == currentSlotIndex.value) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .size(itemSize.frameSize),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.battlepass_get_reward),
                contentDescription = null
            )
        }
    }
}
