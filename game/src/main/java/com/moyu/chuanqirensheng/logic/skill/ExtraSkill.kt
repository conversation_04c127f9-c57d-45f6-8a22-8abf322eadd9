package com.moyu.chuanqirensheng.logic.skill

import com.moyu.core.logic.skill.SpecialSkillTemplate
import com.moyu.core.model.skill.Skill

// todo
val battleEventIds = (21..32).toList()
val landEventIds = listOf(2,3,4,5,21,22,30)
val seaEventIds = listOf(6,7,8,23,24,31)
val dungeonEventIds = listOf(9,10,11,25,26,32)


fun Skill.isUseResource(num: Int) = id == UseResource.id && this.num >= num
fun Skill.isUseMoney(num: Int) = id == UseMoney.id && this.num >= num



fun Skill.isEnterEvent() = id == -10001
fun Skill.isSucceededEvent() = id == -10101
fun Skill.isFailedEvent() = id == -10201
fun Skill.getEventType() = mainId
fun Skill.isDefeatEnemy(raceId: Int) = id == -10501 && (raceId == 0 || raceId == mainId)

fun Skill.isReputationLevelUp(type: Int) = id == -10301 && (type == 0 || mainId == type)
fun Skill.isReputationLevelDown(type: Int) = id == -10302 && (type == 0 || mainId == type)

fun Skill.isReputationUp(type: Int) = id == -10311 && (type == 0 || mainId == type)
fun Skill.isReputationDown(type: Int) = id == -10312 && (type == 0 || mainId == type)

fun Skill.isLoseAllyCard(raceType: Int) = id == -10802 && (raceType == 0 || mainId == raceType)


fun Skill.isBuyGoods() = id == BuyGoodEvent.id
fun Skill.isBattleEvent() = (isSucceededEvent() || isFailedEvent()) && getEventType() in battleEventIds
fun Skill.isLandEvent() = (isSucceededEvent() || isFailedEvent()) && getEventType() in landEventIds
fun Skill.isSeaEvent() = (isSucceededEvent() || isFailedEvent()) && getEventType() in seaEventIds
fun Skill.isDungeonEvent() = (isSucceededEvent() || isFailedEvent()) && getEventType() in dungeonEventIds
fun Skill.isWinBattleEvent() = isSucceededEvent() && getEventType() in battleEventIds
fun Skill.isFailedBattleEvent() = isFailedEvent() && getEventType() in battleEventIds

// todo
fun Skill.isBattle1Decision(type: Int) = id == -20001 && (type == 0 || mainId == type)
fun Skill.isBattle2Decision(type: Int) = id == -20002 && (type == 0 || mainId == type)
fun Skill.isBattle3Decision(type: Int) = id == -20003 && (type == 0 || mainId == type)
fun Skill.isBattle4Decision(type: Int) = id == -20004 && (type == 0 || mainId == type)
fun Skill.isBattle5Decision(type: Int) = id == -20005 && (type == 0 || mainId == type)
fun Skill.isBattle6Decision(type: Int) = id == -20006 && (type == 0 || mainId == type)
fun Skill.isUseFacilityRecover(type: Int) = id == -20007 && (type == 0 || mainId == type)

fun Skill.isLevelUp() = id == LevelUpEvent.id
fun Skill.isGainExp() = id == GainExp.id

fun Skill.isAgeEvent() = id == -11101

val EnterEvent = SpecialSkillTemplate.copy(id = -10001, name = "进入事件")
val SucceededEvent = SpecialSkillTemplate.copy(id = -10101, name = "事件成功")
val FailedEvent = SpecialSkillTemplate.copy(id = -10201, name = "事件失败")
val AgeEvent = SpecialSkillTemplate.copy(id = -11101, name = "年龄+1")

val ReputationUpEvent = SpecialSkillTemplate.copy(id = -10311, name = "声望提升")
val ReputationDownEvent = SpecialSkillTemplate.copy(id = -10312, name = "声望降低")

val ReputationLevelUpEvent = SpecialSkillTemplate.copy(id = -10301, name = "声望等级提升")
val ReputationLevelDownEvent = SpecialSkillTemplate.copy(id = -10302, name = "声望等级降低")

val StartGame = SpecialSkillTemplate.copy(id = -10731, name = "开始游戏")
val GainEquip = SpecialSkillTemplate.copy(id = -10631, name = "获得装备")
val GainExp = SpecialSkillTemplate.copy(id = -10331, name = "获得经验")
val LevelUpEvent = SpecialSkillTemplate.copy(id = -10321, name = "升级")
val GainResource = SpecialSkillTemplate.copy(id = -10413, name = "获得粮草")
val UseResource = SpecialSkillTemplate.copy(id = -10414, name = "消费粮草")
val GainMoney = SpecialSkillTemplate.copy(id = -10411, name = "获得铜钱")
val UseMoney = SpecialSkillTemplate.copy(id = -10401, name = "消费铜钱")
val BuyGoodEvent = SpecialSkillTemplate.copy(id = -10421, name = "消费铜钱")
val DefeatEnemy = SpecialSkillTemplate.copy(id = -10501, name = "战胜敌人")
val ObtainFatalEnemy = SpecialSkillTemplate.copy(id = -10621, name = "收服宿敌")

val Battle1Decision = SpecialSkillTemplate.copy(id = -20001, name = "野战结果")
val Battle2Decision = SpecialSkillTemplate.copy(id = -20002, name = "xx结果")
val Battle3Decision = SpecialSkillTemplate.copy(id = -20003, name = "xx结果")
val Battle4Decision = SpecialSkillTemplate.copy(id = -20004, name = "xx结果")
val Battle5Decision = SpecialSkillTemplate.copy(id = -20005, name = "xx结果")
val Battle6Decision = SpecialSkillTemplate.copy(id = -20006, name = "xx结果")
val BuildingDecision = SpecialSkillTemplate.copy(id = -20007, name = "设施使用结果")

fun battleDecision(type: Int, decision: Int): Skill {
    return when (type) {
        1 -> Battle1Decision.copy(mainId = decision)
        2 -> Battle2Decision.copy(mainId = decision)
        3 -> Battle3Decision.copy(mainId = decision)
        4 -> Battle4Decision.copy(mainId = decision)
        5 -> Battle5Decision.copy(mainId = decision)
        6 -> Battle6Decision.copy(mainId = decision)
        else -> error("未处理type=$type")
    }
}

fun buildingDecision(decision: Int): Skill {
    return BuildingDecision.copy(mainId = decision)
}

val GetAllyCard = SpecialSkillTemplate.copy(id = -10801, name = "获得军团卡")
val LoseAllyFromGame = SpecialSkillTemplate.copy(id = -10802, name = "失去军团卡")

val GetAdventureSkillInGame = SpecialSkillTemplate.copy(id = -10811, name = "获得冒险卡")
val LoseAdventureSkillInGame = SpecialSkillTemplate.copy(id = -10812, name = "失去冒险卡")

val GetBattleSkillInGame = SpecialSkillTemplate.copy(id = -10821, name = "获得战术卡")
val LoseBattleSkillInGame = SpecialSkillTemplate.copy(id = -10822, name = "失去战术卡")

val GetReputationInGame = SpecialSkillTemplate.copy(id = -10861, name = "获得声望")