package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleLayout
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role

const val PROTECT_BATTLE = 29

class ProtectBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val playId: Int = PROTECT_BATTLE
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.forEachIndexed { index, role ->
            positionMap[positionListEnemy[index]] = role
        }
        repo.setCurrentEnemies(positionMap)
    }
}