package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.getAwardDesc
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.cheatBarHeight
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.property.Property
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM


class SelectAwardHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = -1
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            val buttonText = listOf(0, 1, 2).map { index ->
                awards.getOrNull(index)?.equips?.firstOrNull()?.let { equip ->
                    if (BattleManager.getGameEquips()
                            .any { it.mainId == awards[index].equips.first().mainId }
                    ) {
                        stringResource(id = R.string.do_star_up)
                    } else if (equip.isEquipReplaceable() && BattleManager.getGameEquips()
                            .any { it.type == awards[index].equips.first().type }
                    ) {
                        stringResource(id = R.string.replace)
                    } else {
                        stringResource(id = R.string.gain_title)
                    }
                } ?: stringResource(id = R.string.gain_title)
            }
            EventAward3To1Layout(
                awards = awards,
                buttonTexts = buttonText,
                maxLine = LanguageManager.getLine(),
                minLin = LanguageManager.getLine(),
                softWrap = LanguageManager.canSoftWrap(),
                content = { index ->
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalArrangement = Arrangement.Center,
                        overflow = FlowRowOverflow.Visible,
                        maxItemsInEachRow = 2
                    ) {
                        if (awards.getOrNull(index)?.equips?.firstOrNull()?.isEquipReplaceable() == true) {
                            val currentEquip = BattleManager.getGameEquips()
                                .firstOrNull { it.type == awards[index].equips.first().type }
                            val targetEquip = awards[index].equips.first()
                            val compare = currentEquip?.getProperty() ?: Property()
                            val showStarUp = currentEquip?.mainId == targetEquip.mainId
                            val realTargetEquip = if (currentEquip?.mainId == targetEquip.mainId) {
                                currentEquip.getNextLevelEquip()
                            } else targetEquip
                            realTargetEquip.getProperty().MainPropertyLine(
                                originProperty = compare,
                                showZero = false,
                                showBoost = true,
                                showNegative = true,
                                showIcon = false,
                                textColor = Color.White,
                                textStyle = MaterialTheme.typography.h4
                            )
                            if (showStarUp && !eventFinished.value) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Image(
                                        modifier = Modifier
                                            .size(cheatBarHeight),
                                        painter = painterResource(R.drawable.hero_starup),
                                        contentDescription = null
                                    )
                                    Text(
                                        text = stringResource(id = R.string.do_star_up),
                                        style = MaterialTheme.typography.h4
                                    )
                                }
                            }
                        } else {
                            Text(
                                modifier = Modifier.verticalScroll(rememberScrollState()),
                                text = awards[index].getAwardDesc(),
                                style = MaterialTheme.typography.h4
                            )
                        }
                    }
                },
            ) {
                if (!eventFinished.value) {
                    eventFinished.value = true
                    EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                    EventManager.doEventResult(event, true)
                }
            }
            if (awards.isNotEmpty() && awards.any { !it.isEmpty() }) {
                Spacer(modifier = Modifier.size(padding30))
            } else {
                Column(Modifier.padding(horizontal = padding36)) {
                    if (repo.gameCore.getPoolById(event.playPara1.first()).type.first() == 4) {
                        // todo 4是魔法
                        Text(
                            text = stringResource(R.string.skill_not_able_to_learn1),
                            style = MaterialTheme.typography.h2
                        )
                    } else {
                        Text(
                            text = stringResource(R.string.skill_not_able_to_learn2),
                            style = MaterialTheme.typography.h2
                        )
                    }
                }
            }
            GameButton(text = stringResource(id = R.string.quit)) {
                EventManager.doEventResult(event, true)
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        val gainProps = repo.gameCore.getPoolById(event.playPara1.first()).toAwards().shuffled(
            RANDOM
        ).take(3)
        awards.addAll(gainProps)
    }
}
