package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toConditionAward
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.core.model.Award
import com.moyu.core.model.Event

@Composable
fun EventConditionLayoutAward(
    modifier: Modifier = Modifier,
    award: Award,
    itemSize: ItemSize,
    textColor: Color = Color.White
) {
    AwardList(
        modifier = modifier,
        award = award,
        param = defaultParam.copy(
            showName = false,
            itemSize = itemSize,
            checkAffordable = true,
            showColumn = false,
            noFrameForItem = true,
            showReputationLevel = true,
            numInFrame = false,
            textColor = textColor,
        ),
    )
}

@Composable
fun EventConditionLayout(
    modifier: Modifier = Modifier,
    event: Event,
    itemSize: ItemSize,
    textColor: Color = Color.White,
    callback: (() -> Unit)? = null
) {
    AwardList(
        modifier = modifier,
        award = event.toConditionAward(),
        param = defaultParam.copy(
            showName = false,
            itemSize = itemSize,
            checkAffordable = true,
            showColumn = false,
            noFrameForItem = true,
            numInFrame = false,
            showReputationLevel = true,
            textColor = textColor,
            callback = callback
        ),
    )
}

fun triggerEvent(event: Event, consume: Boolean = true): Boolean {
    if (event.condition == 0 || DebugManager.easyEvent) return true
    val award = event.toConditionAward()
    return AwardManager.isAffordable(award, consume)
}