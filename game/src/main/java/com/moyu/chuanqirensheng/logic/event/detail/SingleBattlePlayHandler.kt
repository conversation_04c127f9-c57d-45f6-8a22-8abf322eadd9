package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleLayout
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role

const val SINGLE_BATTLE_PLAY = 24

class SingleBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val playId: Int = SINGLE_BATTLE_PLAY
): PlayHandler() {
    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event, capacity = 1)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.forEachIndexed { _, role ->
            // 固定站到中间
            positionMap[positionListEnemy[1]] = role
        }
        repo.setCurrentEnemies(positionMap)
    }
}