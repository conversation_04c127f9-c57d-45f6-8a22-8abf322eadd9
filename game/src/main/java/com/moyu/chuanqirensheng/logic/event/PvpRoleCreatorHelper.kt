package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.pvp.pvpMasterIds
import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Race
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

fun createPvpRole(race: Race, talents: Map<Int, Int>, heroAdvSkillId: List<Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
    }
    heroAdvSkillId.forEach {
        repo.gameCore.getSkillById(it).let { skill ->
            resultProperty += skill.doPvpSkillProperty(race, race.skillId)
        }
    }
    if (race.getAlly().isHero()) {
        talents.filterKeys { it in pvpMasterIds }.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.enemy(name = race.name),
        false
    )
}

fun createPvpPlayerRole(race: Race, talents: Map<Int, Int>, heroAdvSkillId: List<Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
    }
    heroAdvSkillId.forEach {
        repo.gameCore.getSkillById(it).let { skill ->
            resultProperty += skill.doPvpSkillProperty(race, race.skillId)
        }
    }
    if (race.getAlly().isHero()) {
        talents.filterKeys { it in pvpMasterIds }.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.player(name = race.name)
    )
}

fun createTowerRole(race: Race, talents: Map<Int, Int>, heroAdvSkillId: List<Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
        resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
    }
    heroAdvSkillId.forEach {
        repo.gameCore.getSkillById(it).let { skill ->
            resultProperty += skill.doPvpSkillProperty(race, race.skillId)
        }
    }
    if (race.getAlly().isHero()) {
        talents.filterKeys { it in pvpMasterIds }.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.player(name = race.name)
    )
}

fun createWorldBossRole(race: Race): Role {
    // 世界Boss没有天赋和高级技能，就是一个固定的敌人
    return DefaultRoleCreator.create(
        race,
        Property(), // 使用默认属性
        emptyList(),
        Identifier.enemy(name = race.name),
        false
    )
}
