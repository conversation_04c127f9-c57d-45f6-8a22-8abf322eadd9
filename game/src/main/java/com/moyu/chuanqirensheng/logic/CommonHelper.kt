package com.moyu.chuanqirensheng.logic

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.text.playRuleMap
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.AppWrapper
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Sell
import com.moyu.core.model.getRaceGroupName
import com.moyu.core.model.skill.isAdventure

val resourcesName = listOf(
    GameApp.instance.getWrapString(R.string.resource1),
    GameApp.instance.getWrapString(R.string.resource2),
    GameApp.instance.getWrapString(R.string.resource3),
    GameApp.instance.getWrapString(R.string.resource4),
    GameApp.instance.getWrapString(R.string.resource5),
    GameApp.instance.getWrapString(R.string.resource6),
    GameApp.instance.getWrapString(R.string.resource7),
    GameApp.instance.getWrapString(R.string.resource8)
)

val resourcesTips = listOf(
    GameApp.instance.getWrapString(R.string.resource_desc_1),
    GameApp.instance.getWrapString(R.string.resource_desc_2),
    GameApp.instance.getWrapString(R.string.resource_desc_3),
    GameApp.instance.getWrapString(R.string.resource_desc_4),
    GameApp.instance.getWrapString(R.string.resource_desc_5),
    GameApp.instance.getWrapString(R.string.resource_desc_6),
    GameApp.instance.getWrapString(R.string.resource_desc_7),
    GameApp.instance.getWrapString(R.string.resource_desc_8),
)

fun Event.playName(): String {
    return when (play) {
        1 -> GameApp.instance.getWrapString(R.string.play_name1)
        2 -> GameApp.instance.getWrapString(R.string.play_name2)
        3 -> GameApp.instance.getWrapString(R.string.play_name3)
        4 -> GameApp.instance.getWrapString(R.string.play_name4)
        5 -> GameApp.instance.getWrapString(R.string.play_name5)
        6 -> GameApp.instance.getWrapString(R.string.play_name6)
        7 -> GameApp.instance.getWrapString(R.string.play_name7)
        8 -> GameApp.instance.getWrapString(R.string.play_name8)
        9 -> GameApp.instance.getWrapString(R.string.play_name9)
        10 -> GameApp.instance.getWrapString(R.string.play_name10)
        11 -> GameApp.instance.getWrapString(R.string.play_name11)
        21 -> GameApp.instance.getWrapString(R.string.play_name21)
        22 -> GameApp.instance.getWrapString(R.string.play_name22)
        23 -> GameApp.instance.getWrapString(R.string.play_name23)
        24 -> GameApp.instance.getWrapString(R.string.play_name24)
        25 -> GameApp.instance.getWrapString(R.string.play_name25)
        26 -> GameApp.instance.getWrapString(R.string.play_name26)
        27 -> GameApp.instance.getWrapString(R.string.play_name27)
        28 -> GameApp.instance.getWrapString(R.string.play_name28)
        29 -> GameApp.instance.getWrapString(R.string.play_name29)
        30 -> GameApp.instance.getWrapString(R.string.play_name30)
        31 -> GameApp.instance.getWrapString(R.string.play_name31)
        32 -> GameApp.instance.getWrapString(R.string.play_name32)
        else -> ""
    }
}

// todo 根据id来简单判定
fun Event.getGroupType() = id - 1006462 + 1

fun Int.indexToResourceName(): String {
    return resourcesName[this]
}

fun Int.indexToResourceIcon(): Int {
    return getImageResourceDrawable("element_${this + 1}")
}

fun Int.indexToResourceTips(): String {
    return resourcesTips[this]
}

fun Sell.getPriceTextWithUnit(): String {
    return if (isAifadian()) {
        if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
            GameApp.instance.getWrapString(R.string.go_and_get)
        } else {
            if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                priceDollar.toString() + GameApp.instance.getWrapString(R.string.real_money_dollar)
            } else {
                price.toString() + GameApp.instance.getWrapString(R.string.real_money_unit)

            }
        }
    } else price.toString()
}

fun Award.getAwardDesc(): String {
    return if (titleId != 0) {
        repo.gameCore.getTitlePool().first { it.id == titleId }.desc
    } else if (resources.any { it != 0 }) {
        resources.mapIndexed { index, i ->
            if (i != 0) {
                when(index) {
                    0-> GameApp.instance.getWrapString(R.string.play_content_1)
                    1-> GameApp.instance.getWrapString(R.string.play_content_2)
                    2-> GameApp.instance.getWrapString(R.string.play_content_3)
                    3-> GameApp.instance.getWrapString(R.string.play_content_4)
                    4-> GameApp.instance.getWrapString(R.string.play_content_5)
                    5-> GameApp.instance.getWrapString(R.string.play_content_6)
                    6-> GameApp.instance.getWrapString(R.string.play_content_7)
                    7-> GameApp.instance.getWrapString(R.string.play_content_8)
                    else -> ""
                }
            } else ""
        }.reduce { acc, s -> acc + s }
    } else if (exp != 0) {
        GameApp.instance.getWrapString(R.string.play_content_exp)
    } else if (reputations.any { it != 0 }) {
        GameApp.instance.getWrapString(R.string.play_content_4)
    } else if (advProperty.getPropertyByTarget(1) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_5)
    } else if (advProperty.getPropertyByTarget(2) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_6)
    } else if (advProperty.getPropertyByTarget(3) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_7)
    } else if (advProperty.getPropertyByTarget(4) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_8)
    } else if (advProperty.getPropertyByTarget(5) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_9)
    } else if (battleProperty.getPropertyByTarget(1) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_10)
    } else if (battleProperty.getPropertyByTarget(2) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_11)
    } else if (battleProperty.getPropertyByTarget(3) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_12)
    } else if (battleProperty.getPropertyByTarget(4) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_13)
    } else if (battleProperty.getPropertyByTarget(5) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_14)
    } else if (battleProperty.getPropertyByTarget(6) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_15)
    } else if (battleProperty.getPropertyByTarget(7) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_16)
    } else if (battleProperty.getPropertyByTarget(8) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_17)
    } else if (battleProperty.getPropertyByTarget(9) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_18)
    } else if (battleProperty.getPropertyByTarget(10) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_19)
    } else if (battleProperty.getPropertyByTarget(11) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_20)
    } else if (skills.isNotEmpty()) {
        skills.firstOrNull { it.isAdventure() }?.getRealDesc() ?: skills.first().getRealDesc()
    } else if (equips.isNotEmpty()) {
        repo.gameCore.getSkillById(equips.first().skillEffect).getRealDesc()
    } else if (allies.isNotEmpty()) {
        // 专属技能
        allies.first().getRace().skillId.firstOrNull()?.let {
            repo.gameCore.getSkillById(it).getRealDesc()
        } ?: ""
    } else ""
}

fun Int.getQualityName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.blue_quality)
        2 -> GameApp.instance.getWrapString(R.string.purple_quality)
        else -> GameApp.instance.getWrapString(R.string.orange_quality)
    }
}

fun Int.levelFrame() = getImageResourceDrawable(
    "level_frame${this}"
)

fun Int.getQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.skill_frame_orange_new
        2 -> R.drawable.skill_frame_orange_new
        3 -> R.drawable.skill_frame_orange_new
        else -> R.drawable.skill_frame_orange_new
    }
}

fun Int.getQualityFrameCircle(): Int {
    return when (this) {
        1 -> R.drawable.ally_frame
        2 -> R.drawable.ally_frame
        3 -> R.drawable.hero_frame
        else -> R.drawable.hero_frame
    }
}

fun Ally.canStarUp(): Boolean {
    val maxStar = star >= starLimit
    return starUpNum != 0 && num - 1 >= starUpNum && !maxStar && AwardManager.diamond.value >= starUpRes
}

fun Ally.getTouchInfo(): String {
    return "$star" + GameApp.instance.getWrapString(R.string.star) + quality.getQualityName()
        .take(2) + name + GameApp.instance.getWrapString(
        R.string.number
    ) + num + "，" + getRaceTips().take(2)
}

fun Ally.getRaceTips(): String {
    return when (type) {
        1 -> GameApp.instance.getWrapString(R.string.race_desc_1)
        2 -> GameApp.instance.getWrapString(R.string.race_desc_2)
        3 -> GameApp.instance.getWrapString(R.string.race_desc_3)
        4 -> GameApp.instance.getWrapString(R.string.race_desc_4)
        else -> GameApp.instance.getWrapString(R.string.race_desc_5)
    }
}

fun Ally.getQualityFrame(): Int {
    return if (isHero()) getImageResourceDrawable("hero_level_frame_$quality") else getImageResourceDrawable(
        "level_frame${quality}"
    )
}

fun Int.getEquipTypeTips(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.itemtype_desc_1)
        2 -> AppWrapper.getString(R.string.itemtype_desc_2)
        3 -> AppWrapper.getString(R.string.itemtype_desc_3)
        4 -> AppWrapper.getString(R.string.itemtype_desc_4)
        5 -> AppWrapper.getString(R.string.itemtype_desc_5)
        6 -> AppWrapper.getString(R.string.itemtype_desc_6)
        7 -> AppWrapper.getString(R.string.itemtype_desc_7)
        8 -> AppWrapper.getString(R.string.itemtype_desc_8)
        else -> ""
    }
}

fun Int.toMagicElementName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.mofa_element1)
        2 -> GameApp.instance.getWrapString(R.string.mofa_element2)
        3 -> GameApp.instance.getWrapString(R.string.mofa_element3)
        4 -> GameApp.instance.getWrapString(R.string.mofa_element4)
        5 -> GameApp.instance.getWrapString(R.string.mofa_element5)
        else -> ""
    }
}


fun Int.toBuildingElementName(): String {
    return getRaceGroupName()
}


fun Int.toEquipTypeRes() = getImageResourceDrawable("equipment_type${this}")

fun Int.playToName(): String {
    return playNameMap[this] ?: ""
}

fun Int.playToTips(): String {
    return playRuleMap[this] ?: ""
}

fun Int.skillTag(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.skill_tag1)
        2 -> GameApp.instance.getWrapString(R.string.skill_tag2)
        3 -> GameApp.instance.getWrapString(R.string.skill_tag3)
        4 -> GameApp.instance.getWrapString(R.string.skill_tag4)
        5 -> GameApp.instance.getWrapString(R.string.skill_tag5)
        6 -> GameApp.instance.getWrapString(R.string.skill_tag6)
        7 -> GameApp.instance.getWrapString(R.string.skill_tag7)
        8 -> GameApp.instance.getWrapString(R.string.skill_tag8)
        9 -> GameApp.instance.getWrapString(R.string.skill_tag9)
        10 -> GameApp.instance.getWrapString(R.string.skill_tag10)
        11 -> GameApp.instance.getWrapString(R.string.skill_tag11)
        12 -> GameApp.instance.getWrapString(R.string.skill_tag12)
        else -> GameApp.instance.getWrapString(R.string.skill_tag13)
    }
}