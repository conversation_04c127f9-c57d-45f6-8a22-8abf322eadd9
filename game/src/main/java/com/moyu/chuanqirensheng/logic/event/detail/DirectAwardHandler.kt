package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.core.model.Event

class DirectAwardHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = -1
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        LaunchedEffect(Unit) {
            EventManager.doEventResult(event, true)
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}
