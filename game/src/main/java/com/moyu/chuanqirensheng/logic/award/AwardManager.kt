package com.moyu.chuanqirensheng.logic.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.toMutableStateList
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_NUM
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.onTaskGetItem
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.gainResources
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkAlly
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.anticheat.GuardedList
import com.moyu.chuanqirensheng.sub.datastore.KEY_AD_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_COUPON_ALLY
import com.moyu.chuanqirensheng.sub.datastore.KEY_COUPON_HERO
import com.moyu.chuanqirensheng.sub.datastore.KEY_COUPON_HISTORY
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_CHARGE_DONE
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_COST
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_REAL_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATIONS
import com.moyu.chuanqirensheng.sub.datastore.KEY_TCG_CARD_REWARD
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS3
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.share.ShareManager.shareCount
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.AdUtil.decodeText
import com.moyu.chuanqirensheng.util.AdUtil.encodeText
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.perBiggerI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AwardManager {
    val tcgCardRewardRecords = mutableStateListOf<Int>()

    val battlePassBought = mutableMapOf<Int, MutableState<Boolean>>()
    val battlePass2Bought = mutableMapOf<Int, MutableState<Boolean>>()
    val diamond = Guarded(KEY_DIAMOND)
    val diamondAll = Guarded(KEY_DIAMOND_ALL)
    val keyCost = Guarded(KEY_KEY_COST)
    val key = Guarded(KEY_KEY)
    val adNum = Guarded(KEY_AD_NUM)
    val couponAlly = Guarded(KEY_COUPON_ALLY)
    val couponHero = Guarded(KEY_COUPON_HERO)
    val couponHistory = Guarded(KEY_COUPON_HISTORY)
    val keyAll = Guarded(KEY_KEY_ALL)
    val electric = Guarded(KEY_ELECTRIC)
    val pvpDiamond = Guarded(KEY_PVP_DIAMOND)
    val pvpDiamondAll = Guarded(KEY_PVP_DIAMOND_ALL)
    val warPass = Guarded(KEY_WAR_PASS)
    val warPass2 = Guarded(KEY_WAR_PASS2)
    val warPass3 = Guarded(KEY_WAR_PASS3)
    val reputations = GuardedList(KEY_REPUTATIONS, EMPTY_REPUTATION.toMutableStateList())
    val lotteryMoney = Guarded(KEY_LOTTERY_MONEY)
    val holidayMoney = Guarded(KEY_HOLIDAY_MONEY)
    val realMoney = Guarded(KEY_REAL_MONEY)


    fun init() {
        (KEY_WAR_PASS_UNLOCK_EVIDENCE..KEY_WAR_PASS_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePassBought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }

        (KEY_WAR_PASS2_UNLOCK_EVIDENCE..KEY_WAR_PASS2_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePass2Bought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }

        tcgCardRewardRecords.addAll(
            getListObject(
                KEY_TCG_CARD_REWARD
            )
        )
    }

    suspend fun gainAward(award: Award) {
        withContext(Dispatchers.Main) {
            onTaskGetItem(award)
            MusicManager.playSound(SoundEffect.GainAward)
            award.diamond.takeIf { it != 0 }?.let {
                gainDiamond(it)
            }
            award.pvpDiamond.takeIf { it != 0 }?.let {
                gainPvpDiamond(it)
            }
            award.pvpScore.takeIf { it != 0 }?.let {
                PvpManager.pvpScore.value += it
            }
            award.pvp2Score.takeIf { it != 0 }?.let {
                Pvp2Manager.pvpScore.value += it
            }
            if (award.resources.any { it != 0 }) {
                gainResources(award.resources)
            }
            award.key.takeIf { it != 0 }?.let {
                gainKey(it)
            }
            award.lotteryMoney.takeIf { it != 0 }?.let {
                lotteryMoney.value += it
            }
            award.couponAlly.takeIf { it != 0 }?.let {
                couponAlly.value += it
            }
            award.couponHero.takeIf { it != 0 }?.let {
                couponHero.value += it
            }
            award.electric.takeIf { it != 0 }?.let {
                gainElectric(it)
            }
            award.realMoney.takeIf { it != 0 }?.let {
                realMoney.value += it
            }
            award.holidayMoney.takeIf { it != 0 }?.let {
                holidayMoney.value += it
            }
            award.warPass.takeIf { it != 0 }?.let {
                warPass.value += it
            }
            award.warPass2.takeIf { it != 0 }?.let {
                warPass2.value += it
            }
            award.warPass3.takeIf { it != 0 }?.let {
                warPass3.value += it
            }
            award.exp.takeIf { it != 0 }?.let { rawExp ->
                BattleManager.gainExp(rawExp)
            }
            award.allHeal.takeIf { it > 0 }?.let {
                BattleManager.healAllAllyInGame(it)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_heal_all),
                    BattleInfoType.Battle
                )
            }
            award.battleHeal.takeIf { it > 0 }?.let {
                BattleManager.healBattleAllyInGame(it)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_heal_battle),
                    BattleInfoType.Battle
                )
            }
            award.titleId.takeIf { it > 0 }?.let {
                BattleManager.gainTitle(it)
            }
            award.upgradeSkills.forEach { pair ->
                val allyUuid = pair.first
                val skill = pair.second
                BattleManager.troopSkill(allyUuid, skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_skill_upgrade) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.upgradeAllies.forEach {
                BattleManager.upgradeAlly(it)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_skill_upgrade) + it.name,
                    BattleInfoType.Battle
                )
            }
            award.allies.forEach { ally ->
                BattleManager.gainInGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_ally, ally.name, ally.num),
                    BattleInfoType.Battle
                )
            }
            award.equips.forEach { equip ->
                BattleManager.gainEquip(equip)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_equip) + equip.name,
                    BattleInfoType.Battle
                )
            }

            award.loseAllies.forEach { ally ->
                BattleManager.dropFromGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_ally) + ally.name,
                    BattleInfoType.Battle
                )
            }
            award.outAllies.forEach { ally ->
                checkAlly(ally)
                repo.allyManager.gain(ally.copy(new = true))
            }
            award.skills.forEach { skill ->
                BattleManager.gainSkillInGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_title) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.loseSkills.forEach { skill ->
                BattleManager.dropFromGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_skill_card) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.advProperty.takeIf { it.isNotEmpty() }?.let { property ->
                BattleManager.gainAdventureProp(property)
            }
            award.battleProperty.takeIf { it.isNotEmpty() }?.let { property ->
                BattleManager.gainBattleProp(property)
            }
            award.unlockList.forEach {
                UnlockManager.unlockCode(it)
            }
        }
    }

    fun doNetAward(text: String) {
        GameApp.globalScope.launch {
            // 确认是否是推广码
            if (AdUtil.isShareCode(text)) {
                GameApp.instance.getObjectId()?.let {
                    encodeText(it)?.let { encryptedUserId ->
                        val realId = AdUtil.simpleDecryptionText(text)
                        val flow = getLongFlowByKey(realId)
                        if (flow != 0L) {
                            GameApp.instance.getWrapString(R.string.share_code_already_used_by_you)
                                .toast()
                        } else if (text == GameApp.instance.getShareCode()) {
                            GameApp.instance.getWrapString(R.string.share_code_can_not_use_your_own)
                                .toast()
                        } else {
                            val result = RetrofitModel.useShareCode(
                                it,
                                text,
                                encryptedUserId,
                                getVersionCode()
                            )
                            if (!result.valid) {
                                GameApp.instance.getWrapString(R.string.code_error_tips2).toast()
                            } else {
                                if (shareCount.value >= repo.gameCore.getMaxUseOtherCount()) {
                                    GameApp.instance.getWrapString(
                                        R.string.top_share_award_tips,
                                        repo.gameCore.getMaxUseOtherCount()
                                    ).toast()
                                } else {
                                    setLongValueByKey(realId, 1)
                                    shareCount.value += 1
                                    if (!result.isEmpty()) {
                                        gainAward(result)
                                        // 显示
                                        Dialogs.awardDialog.value = result
                                    }
                                }
                            }
                        }
                    } ?: GameApp.instance.getWrapString(R.string.code_error).toast()
                } ?: GameApp.instance.getWrapString(R.string.confirm_login).toast()
            } else {
                // 确认是否是网络兑换码
                val netCode = decodeText(text)
                if (netCode?.startsWith("u_") == true) {
                    val flow = getLongFlowByKey(text)
                    if (flow != 0L) {
                        GameApp.instance.getWrapString(R.string.code_already_used_by_you).toast()
                    } else {
                        GameApp.instance.getObjectId()?.let {
                            encodeText(it)?.let { encodedUserId ->
                                val result =
                                    RetrofitModel.getCodeAwards(it, netCode, encodedUserId, getVersionCode())
                                if (!result.valid) {
                                    GameApp.instance.getWrapString(R.string.code_error_tips2).toast()
                                } else {
                                    // 标记已领取
                                    setLongValueByKey(text, 1)
                                    if (result.unlockList.isNotEmpty()) {
                                        GameApp.instance.getWrapString(R.string.unlocked_tips).toast()
                                    } else if (result.message.isNotEmpty()) {
                                        result.message.toast()
                                    }
                                    if (!result.isEmpty()) {
                                        if (result.isMonthCard()) {
                                            repo.gameCore.getSellPool().firstOrNull { it.id == result.sellId }?.let {
                                                MonthCardManager.openPackage(it)
                                            }
                                        } else {
                                            gainAward(result)
                                            // 显示
                                            Dialogs.awardDialog.value = result
                                        }
                                        GiftManager.onGiftBought(result.sellId)
                                    }
                                }
                            } ?: GameApp.instance.getWrapString(R.string.code_error).toast()
                        } ?: GameApp.instance.getWrapString(R.string.confirm_login).toast()
                    }
                } else {
                    awardList.find { text == it.code && (isNetTimeValid()) }
                        ?.let {
                            val flow = getLongFlowByKey(it.code)
                            if (flow == 0L) {
                                val award = it
                                // 标记已领取
                                setLongValueByKey(it.code, 1)
                                // 领取奖励
                                gainAward(award)
                                // 显示
                                Dialogs.awardDialog.value = award
                            } else {
                                GameApp.instance.getWrapString(R.string.code_used).toast()
                            }
                        } ?: GameApp.instance.getWrapString(R.string.code_not_found).toast()
                }
            }
        }
    }

    fun gainDiamond(gain: Int) {
        diamond.value += gain
        diamondAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }


    fun gainPvpDiamond(gain: Int) {
        pvpDiamond.value += gain
        pvpDiamondAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_pvp_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainRealMoney(gain: Int) {
        realMoney.value += gain
        GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
    }

    fun gainKey(gain: Int) {
        key.value += gain
        keyAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_keys) + gain, BattleInfoType.ExtraSkill
            )
        } else {
            keyCost.value += -gain
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    suspend fun gainElectric(gain: Int) {
        withContext(Dispatchers.Main) {
            electric.value += gain
            SevenDayManager.gainElectric(gain)
            HolidayManager.gainElectric(gain)
            setBooleanValueByKey(KEY_FIRST_CHARGE_DONE, true)

            GameApp.globalScope.launch(Dispatchers.IO) {
                delay(2000)
                GameApp.instance.uploadCurrentSave()
            }
        }
    }

    fun gainHolidayMoney(gain: Int) {
        holidayMoney.value += gain
    }

    fun isAffordable(award: Award, consume: Boolean = false): Boolean {
        if (award.diamond > 0 && award.diamond > diamond.value) {
            return false
        }
        if (award.pvpDiamond > 0 && award.pvpDiamond > pvpDiamond.value) {
            return false
        }
        if (award.key > 0 && award.key > key.value) {
            return false
        }
        award.resources.forEachIndexed { index, i ->
            if (i != 0) {
                if (BattleManager.resources[index] < i) {
                    return false
                }
            }
        }
        if (award.titleLevel != 0) {
            if (BattleManager.yourTitle.value.level + 1 < award.titleLevel) {
                return false
            }
        }
        if (award.reputations.any { it != 0 } && !reputations.perBiggerI(award.reputations)) {
            return false
        }
        if (!BattleManager.adventureProps.value.perBiggerI(award.advProperty)) {
            return false
        }
        if (award.skills.isNotEmpty()) {
            award.skills.forEach { target ->
                if (BattleManager.skillGameData.none {
                        it.id == target.id
                    }) {
                    return false
                }
            }
        }
        if (consume) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                gainResources(award.resources.map { -it })
                gainDiamond(-award.diamond)
                gainKey(-award.key)
            }
        }
        return true
    }
}