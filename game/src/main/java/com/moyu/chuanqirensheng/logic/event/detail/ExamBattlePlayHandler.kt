package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleLayout
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role

const val EXAM_BATTLE_PLAY = 26

class ExamBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val playId: Int = EXAM_BATTLE_PLAY
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event, capacity = 1)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.forEachIndexed { index, role ->
            // 固定站到中间
            positionMap[positionListEnemy[index]] = role
        }
        repo.setCurrentEnemies(positionMap)

        // 执行两次可以保证把主角放到中间，第一次卸下，第二次上阵
        if (BattleManager.isAllyInBattle(BattleManager.getGameMaster())) {
            BattleManager.selectAllyToBattle(
                BattleManager.getGameMaster(),
                1
            )
        }
        BattleManager.selectAllyToBattle(
            BattleManager.getGameMaster(),
            1
        )
    }
}