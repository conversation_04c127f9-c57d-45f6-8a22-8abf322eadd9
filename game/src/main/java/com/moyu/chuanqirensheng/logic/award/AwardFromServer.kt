package com.moyu.chuanqirensheng.logic.award

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlinx.serialization.Serializable

@Serializable
data class AwardFromServer(
    val code: String = "",
    val message: String = "",
    val allies: List<Int> = emptyList(),
    val skins: List<Int> = emptyList(),
    val diamond: Int = 0,
    val pvpDiamond: Int = 0,
    val key: Int = 0,
    val electric: Int = 0,
    val realMoney: Int = 0,
    val lotteryMoney: Int = 0,
    val couponAlly: Int = 0,
    val couponHero: Int = 0,
    val unlockList: List<Int> = emptyList(),
    val poolIds: List<Int> = emptyList(),
    val sellId: Int = 0,
    val valid: Boolean = true,
) {
    fun toAward(): Award {
        val realAllies = allies.map {
            repo.gameCore.getAllyPool().first { ally -> ally.id == it }
        }
        val poolAwards = poolIds.map {
            repo.gameCore.getPoolById(it).toAward()
        }.reduceOrNull { acc, award -> acc + award }?: Award()
        val sellAward = if (sellId != 0) {
            repo.gameCore.getSellPool().firstOrNull { it.id == sellId }?.toAward() ?: Award()
        } else Award()
        return Award(
            message = message,
            valid = valid,
            outAllies = realAllies,
            electric = electric,
            diamond = diamond,
            realMoney = realMoney,
            lotteryMoney = lotteryMoney,
            pvpDiamond = pvpDiamond,
            couponAlly = couponAlly,
            couponHero = couponHero,
            key = key,
            unlockList = unlockList,
            sellId = sellId
        ) + poolAwards + sellAward
    }
}
