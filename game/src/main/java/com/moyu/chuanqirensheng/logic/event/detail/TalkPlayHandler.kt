package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.buttonWidth
import com.moyu.chuanqirensheng.ui.theme.eventAwardHeight
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award

@Composable
fun EventAward3To1Layout(
    awards: List<Award>,
    replaceIcons: List<Int> = emptyList(),
    replaceTexts: List<String> = emptyList(),
    content: @Composable (BoxScope.(Int) -> Unit)? = null,
    buttonTexts: List<String> = listOf(
        stringResource(id = R.string.gain_title),
        stringResource(id = R.string.gain_title),
        stringResource(id = R.string.gain_title)
    ),
    buttonEnables: List<Boolean> = listOf(
        true,
        true,
        true
    ),
    buttonConditions: List<Award> = emptyList(),
    showNum: Boolean = true,
    textColor: Color = Color.White,
    solidFrameDrawable: Int? = null,
    maxLine: Int = 1,
    minLin: Int = 1,
    softWrap: Boolean = true,
    tips: (() -> Unit)? = null,
    select: (Int) -> Unit = {}
) {
    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
        awards.take(3).forEachIndexed { index, award ->
            if (!award.isEmpty()) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    AwardList(
                        award = award,
                        param = defaultParam.copy(
                            solidFrameDrawable = solidFrameDrawable,
                            showNum = showNum,
                            textColor = textColor,
                            maxLine = maxLine,
                            minLine = minLin,
                            softWrap = softWrap,
                            textFrameDrawableYPadding = padding0,
                            replaceIcon = replaceIcons.getOrNull(index),
                            replaceText = replaceTexts.getOrNull(index),
                            callback = tips
                        ),
                    )
                    content?.let {
                        Box(
                            Modifier
                                .size(buttonWidth, eventAwardHeight)
                                .graphicsLayer {
                                    translationY = -padding6.toPx()
                                }, contentAlignment = Alignment.Center
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.common_frame),
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = padding10, vertical = padding14)
                            ) {
                                it(index)
                            }
                        }
                    }
                    Box(contentAlignment = Alignment.Center) {
                        if (buttonConditions.isNotEmpty() && !buttonConditions[index].isEmpty()) {
                            GameButton(
                                buttonStyle = ButtonStyle.Orange,
                                enabled = buttonEnables[index],
                                text = ""
                            ) {
                                select(index)
                            }
                            AwardList(
                                award = buttonConditions[index],
                                param = defaultParam.copy(
                                    showName = false,
                                    itemSize = ItemSize.Small,
                                    checkAffordable = true,
                                    showColumn = false,
                                    noFrameForItem = true,
                                    numInFrame = false,
                                    showReputationLevel = true,
                                    textColor = Color.White,
                                    callback = {
                                        select(index)
                                    }
                                ),
                            )
                        } else {
                            GameButton(
                                buttonStyle = ButtonStyle.Orange,
                                enabled = buttonEnables[index],
                                text = buttonTexts[index]
                            ) {
                                select(index)
                            }
                        }
                    }
                }
            }
        }
    }
}