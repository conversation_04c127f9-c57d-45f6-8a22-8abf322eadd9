package com.moyu.chuanqirensheng.logic.basic

import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.core.model.GameItem
import kotlinx.serialization.KSerializer

class BasicItemHolder<T : GameItem>(
    override val saveKey: String,
    override val elementSerializer: KSerializer<T>,
    override val data: SnapshotStateList<T>,
    override val sameGroup: (T, T) -> Boolean,
    override val increase: (current: T, add: T) -> T,
) : ItemHolder<T> {

    override suspend fun init() {
        val savedList = getListObject(
            saveKey, elementSerializer
        ).map { item ->
            item.create() as T
        }
        gain(savedList)
    }
    private fun gain(value: T, save: Boolean) {
        data.indexOfFirst { sameGroup(value, it) }.takeIf { it >= 0 }?.let {
            data[it] = increase(data[it], value)
        } ?: run {
            data.add(value)
        }
        if (save) {
            save()
        }
    }

    override fun gain(value: T) {
        gain(value, true)
    }

    override fun gain(value: List<T>) {
        value.forEach { gain(it, false) }
        save()
    }

    override fun remove(value: T) {
        data.indexOfFirst { sameGroup(value, it) }.takeIf { it != -1 }?.let {
            data.removeAt(it)
            save()
        }
    }

    override fun save() {
        setListObject(saveKey, data, elementSerializer)
    }

    override fun reset() {
        val copy = data.toList()
        data.clear()
        data.addAll(copy.map {
            it.create() as T
        })
    }

    override fun haveNew(): Boolean {
        return data.any { it.new }
    }

    override fun setUnNew(filter: (T) -> Boolean) {
        val temp = data.filter { filter(it) }.toList()
        data.removeAll { filter(it) }
        data.addAll(temp.map { it.setUnNew() as T })
        save()
    }
}