package com.moyu.chuanqirensheng.logic.award

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.limit.GameLimitManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.GameCore
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.GameMap
import com.moyu.core.model.Mission
import com.moyu.core.model.Pool
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlin.math.abs
import kotlin.math.max

fun Mission.toAward(): Award {
    return repo.gameCore.getPoolById(reward).toAward()
}

fun GameMap.toLootAward(): Award {
    val diamondLevel = repo.gameCore.getEndingDiamondLevel(this.time)
    val difficult = max(0, DifficultManager.maxDoneDifficultLevel.value - 1)
    val diamond = repo.gameCore.getEndingAwardDiamond(difficult, diamondLevel)
    val mapAward = if (!GameLimitManager.mapAwarded(this)) {
            GameLimitManager.setMapAwarded(this)
            repo.gameCore.getPoolById(this.winReward).toAward()
        } else Award()
    return mapAward +  Award(diamond = diamond)
}

fun Ending.toAward(): Award {
    val firstEndingAward = (if (!getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
        if (repo.gameCore.getFirstEndingAwardPoolId() != 0) {
            repo.gameCore.getPoolById(repo.gameCore.getFirstEndingAwardPoolId()).toAward()
        } else Award()
    } else {
        Award()
    })
    val level = repo.gameCore.getEndingAwardLevel(age)
    val diamondLevel = repo.gameCore.getEndingDiamondLevel(age)
    val levelAward = if (level != -1) {
        // 钻石
        val difficult = DifficultManager.getSelected().id - 1
        val diamond = repo.gameCore.getEndingAwardDiamond(difficult, diamondLevel)
        val quality1AllyNum = repo.gameCore.getQuality1AllyNum(difficult, level)
        val quality2AllyNum = repo.gameCore.getQuality2AllyNum(difficult, level)
        val quality3AllyNum = repo.gameCore.getQuality3AllyNum(difficult, level)
        val quality4AllyNum = repo.gameCore.getQuality4AllyNum(difficult, level)
        val quality5AllyNum = repo.gameCore.getQuality5AllyNum(difficult, level)
        val allies = distributeAllies(repo.gameCore.getQuality1AllyPool().map {
            repo.gameCore.getAllyById(it)
        }, quality1AllyNum) + distributeAllies(repo.gameCore.getQuality2AllyPool().map {
            repo.gameCore.getAllyById(it)
        }, quality2AllyNum) + distributeAllies(repo.gameCore.getQuality3AllyPool().map {
            repo.gameCore.getAllyById(it)
        }, quality3AllyNum) + distributeAllies(repo.gameCore.getQuality4AllyPool().map {
            repo.gameCore.getAllyById(it)
        }, quality4AllyNum) + distributeAllies(repo.gameCore.getQuality5AllyPool().map {
            repo.gameCore.getAllyById(it)
        }, quality5AllyNum)
        Award(
            diamond = diamond,
            outAllies = allies
        )
    } else Award()
    val mapAward = if (this.dieReason == GameApp.instance.getWrapString(R.string.die_good)) {
        if (!GameLimitManager.mapAwarded(DifficultManager.selectedMap.value)) {
            GameLimitManager.setMapAwarded(DifficultManager.selectedMap.value)
            repo.gameCore.getPoolById(DifficultManager.getSelectedMap().winReward).toAward()
        } else Award()
    } else Award()
    return levelAward + firstEndingAward + mapAward
}

fun distributeAllies(allyPool: List<Ally>, numNeeded: Int): List<Ally> {
    val results = mutableListOf<Ally>()
    var totalNum = 0

    while (totalNum < numNeeded) {
        allyPool.shuffled().forEach { ally ->
            val numToAdd = RANDOM.nextIntClosure(1, numNeeded - totalNum)
            val newAlly = ally.copy(num = numToAdd)
            results.add(newAlly)
            totalNum += numToAdd
            if (totalNum >= numNeeded) return results
        }
    }

    return results
}

fun Event.toConditionAward(): Award {
    return GameCore.instance.getPoolById(condition).toAward().let {
        getRealRequireByDifficult(it)
    }
}

fun getRealRequireByDifficult(it: Award): Award {
    val difficultLevel = DifficultManager.getSelected()
    var resultProperty = it
    // 难度影响条件
    if (it.advProperty.science > 0) {
        resultProperty += Award(advProperty = AdventureProps(science = difficultLevel.adv1))
    }
    if (it.advProperty.politics > 0) {
        resultProperty += Award(advProperty = AdventureProps(politics = difficultLevel.adv2))
    }
    if (it.advProperty.military > 0) {
        resultProperty += Award(advProperty = AdventureProps(military = difficultLevel.adv3))
    }
    if (it.advProperty.religion > 0) {
        resultProperty += Award(advProperty = AdventureProps(religion = difficultLevel.adv4))
    }
    if (it.advProperty.commerce > 0) {
        resultProperty += Award(advProperty = AdventureProps(commerce = difficultLevel.adv5))
    }
//    if (it.gold > 0) {
//        resultProperty += Award(gold = difficultLevel.resource1)
//    }
//    if (it.resource > 0) {
//        resultProperty += Award(resource = + difficultLevel.resource2)
//    }
    return resultProperty
}

fun Event.toAward(win: Boolean): Award {
    return toAwards(win).toAward().let {
        if (this.winReward.first() != 0) {
            val pool = GameCore.instance.getPoolById(this.winReward.first())
            // todo 魔法行会的奖励是随机的，所以要显示问号
            // 局内资源type=3的pool=0不是随机。
            if (pool.type.size > pool.totalNum || (pool.type.none { it == 3 } && pool.pool.any { it == 0 }) ) {
                // 如果奖励是多个里随机一个，事件的详情里则可能要显示问号
                it.copy(showQuestion = true)
            } else it
        } else it
    }
}

fun Event.toAwards(win: Boolean): List<Award> {
    return try {
        if (win) winReward.map { GameCore.instance.getPoolById(it).toAward() }
        else loseReward.map {
            // 这里要小心处理
            val pool = GameCore.instance.getPoolById(it)
            // 这里是局内event可能要丢弃卡牌，我先分割pool为单个pool，一个一个算
            val pools = pool.split()
            val awards = pools.map { singlePool ->
                if (singlePool.singlePoolNeedLoseCard()) {
                    // 如果是需要丢弃卡牌，把数值改正
                    val award = singlePool.copy(num = listOf(abs(singlePool.num.first()))).toAwards(
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData },
                    ).toAward()
                    // 转负
                    -award
                } else {
                    singlePool.toAward()
                }
            }
            awards.toAward()
        }
    } catch (e: Exception) {
        emptyList()
    }
}

// todo 太麻烦了，这里是局内可能失去的卡牌枚举
fun Int.isInGameCard(): Boolean {
    return this == 1 || this == 2 || this == 3 || this == 4 || this == 11
}

fun Pool.singlePoolNeedLoseCard(): Boolean {
    return type.first().isInGameCard() && this.num.first() < 0
}
