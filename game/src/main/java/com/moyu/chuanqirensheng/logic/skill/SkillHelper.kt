package com.moyu.chuanqirensheng.logic.skill

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_TEXT_FIXED_COLOR
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.getTextColor
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill

fun Skill.canStarUp(): Boolean {
    return !repo.inGame.value && repo.gameCore.getSkillPool()
        .filter { it.mainId == mainId }.size > 1
}

fun Skill.getFrameDrawable(): Int {
    return repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.quality?.getQualityFrame()
        ?: (2.getQualityFrame())
}

fun getSkillFrame() = R.drawable.skill_frame_orange

fun Skill.story(): String {
    return repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.story ?: "0"
}

private val damageDisplayList = DamageType.entries.map { it.display }
private val damageDefenseList = DamageType.entries.map { it.defenseName }

// 正则表达式 匹配中括号、【】括号 以及 damageTypeDisplayList
private val regexStr =
    """(?<=\[)(.+?)(?=])|(?<=【)(.+?)(?=】)|""" + damageDisplayList.joinToString("|") + damageDefenseList.joinToString(
        "|"
    ) + GameApp.instance.getWrapString(R.string.gold) + "|" + GameApp.instance.getWrapString(
        R.string.resource
    )

private val regexEventStr =
    """(?<=\[)(.+?)(?=])|(?<=【)(.+?)(?=】)|"""


@Composable
fun Skill.getRealDescColorful(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    return getRealDesc().toSkillAnnotatedString(spanStyle = spanStyle)
}


@Composable
fun String.toAnnotatedEventText(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) {
        return buildAnnotatedString { append(this@toAnnotatedEventText) }
    }
    return buildAnnotatedString {
        append(this@toAnnotatedEventText)
        Regex(regexEventStr).findAll(this@toAnnotatedEventText).forEach { matchResult: MatchResult ->
            addStyle(
                style = spanStyle.copy(DarkGreen),
                start = matchResult.range.first,
                end = matchResult.range.last + 1
            )
        }
    }
}


@Composable
fun String.toSkillAnnotatedString(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) {
        return buildAnnotatedString { append(this@toSkillAnnotatedString) }
    }
    return buildAnnotatedString {
        append(this@toSkillAnnotatedString)
        Regex(regexStr).findAll(this@toSkillAnnotatedString).forEach { matchResult: MatchResult ->
            if (damageDisplayList.any { damageDisplay -> damageDisplay == matchResult.value }) {
                addStyle(
                    style = spanStyle.copy(
                        color = DamageType.fromDisplayValue(matchResult.value)?.getTextColor()
                            ?: Color.White
                    ), start = matchResult.range.first, end = matchResult.range.last + 1
                )
            } else {
                addStyle(
                    style = spanStyle.copy(DarkGreen),
                    start = matchResult.range.first,
                    end = matchResult.range.last + 1
                )
            }
        }
    }
}