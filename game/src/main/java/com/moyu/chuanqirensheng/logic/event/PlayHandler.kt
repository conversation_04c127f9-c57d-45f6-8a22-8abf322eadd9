package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.core.model.Award
import com.moyu.core.model.Event


abstract class PlayHandler {
    abstract val playId: Int
    abstract val skipWin: Boolean // 跳过事件，判定为事件成功还是失败
    val eventFinished: MutableState<Boolean> = mutableStateOf(false) // 事件是否结束
    val eventResult: MutableState<Boolean> = mutableStateOf(false) // 事件结果
    private val eventAward: MutableState<Award> = mutableStateOf(Award()) // 事件奖励

    @Composable
    abstract fun Layout(event: Event)

    fun eventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = skipWin
        eventAward.value = Award()
        onEventSelect(event)
    }

    abstract fun onEventSelect(event: Event)

    fun setEventAward(award: Award) {
        eventAward.value = award
    }

    fun getEventAward(event: Event): Award {
        return event.toAward(true)
            .copy(showQuestion = false) + eventAward.value
    }

    open fun getEventFailAward(event: Event): Award {
        return event.toAward(false).copy(showQuestion = false)
    }
}
