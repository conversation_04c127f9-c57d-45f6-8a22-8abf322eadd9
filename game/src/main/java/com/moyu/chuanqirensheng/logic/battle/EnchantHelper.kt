package com.moyu.chuanqirensheng.logic.battle

import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEnhancement
import com.moyu.core.model.skill.SkillEnhancementType


fun Skill.enchant(enhancementId: SkillEnhancementType, value: Double): Skill {
    val current = enhancementOneGame
    val enhancement = SkillEnhancement(
        enhancementId = enhancementId, enhancementValue = value
    )
    val newList =
        if (current.isEmpty()) mutableListOf(enhancement) else current.toMutableList()
            .apply {
                add(enhancement)
            }
    return copy(
        enhancementOneGame = newList
    )
}