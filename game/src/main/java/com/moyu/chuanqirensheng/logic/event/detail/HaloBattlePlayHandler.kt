package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleLayout
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM

const val HALO_BATTLE = 21
const val HALO2_BATTLE = 22
const val HALO3_BATTLE = 23
const val HALO4_BATTLE = 24
const val HALO5_BATTLE = 25
const val HALO6_BATTLE = 26
const val HALO7_BATTLE = 27
const val HALO8_BATTLE = 30
const val HALO9_BATTLE = 31
const val HALO10_BATTLE = 32

class HaloBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val playId: Int = HALO_BATTLE
) : PlayHandler() {

    private val halo = mutableStateOf<Skill?>(null)

    @Composable
    override fun Layout(event: Event) {
        Box(Modifier.fillMaxSize()) {
            halo.value?.let {
                // todo 特殊处理，战场效果直接写死
                Image(
                    modifier = Modifier
                        .fillMaxSize()
                        .scale(1.2f),
                    painter = painterResource(id = getImageResourceDrawable("environment_${it.id - 4000}")),
                    contentDescription = null
                )
                Spacer(modifier = Modifier
                    .fillMaxSize()
                    .scale(1.2f)
                    .background(B65))
            }
            BattleLayout(event = event) {
                halo.value?.let {
                    if (repo.inBattle.value) {
                        TextLabel(Modifier.graphicsLayer {
                            translationX = padding42.toPx()
                        }, text = stringResource(R.string.check_halo)) {
                            Dialogs.skillDetailDialog.value = it.copy(peek = true)
                        }
                    } else {
                        Box(
                            modifier = Modifier
                                .width(padding180)
                                .graphicsLayer {
                                    translationX = -padding42.toPx()
                                    translationY = -padding14.toPx()
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Image(
                                modifier = Modifier
                                    .fillMaxWidth().graphicsLayer {
                                        scaleY = 1.3f
                                    },
                                contentScale = ContentScale.FillWidth,
                                painter = painterResource(id = R.drawable.common_frame_long),
                                contentDescription = null
                            )
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(text = stringResource(id = R.string.current_halo) + it.name, style = MaterialTheme.typography.h5)
                                Spacer(modifier = Modifier.size(padding4))
                                Text(
                                    modifier = Modifier.padding(horizontal = padding6),
                                    text = it.getRealDescColorful(MaterialTheme.typography.body1.toSpanStyle()),
                                    style = MaterialTheme.typography.body1
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.forEachIndexed { index, role ->
            positionMap[positionListEnemy[index]] = role
        }
        repo.setCurrentEnemies(positionMap)

        // 天气
        val haloPool = repo.gameCore.getPoolById(event.playPara1[1])
        haloPool.pool.shuffled(RANDOM).first().takeIf { it != 0 }?.let { haloSkillId ->
            halo.value = repo.gameCore.getSkillById(haloSkillId).apply {
                positionMap.values.firstNotNullOf { it }.let {
                    it.learnSkill(this, it)
                }
            }
        }
    }
}