package com.moyu.chuanqirensheng.text

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill

val SKILL_LABELS = hashMapOf(
    1 to GameApp.instance.getWrapString(R.string.skill_label1),
    2 to GameApp.instance.getWrapString(R.string.skill_label2),
    3 to GameApp.instance.getWrapString(R.string.skill_label3),
    4 to GameApp.instance.getWrapString(R.string.skill_label4),
    5 to GameApp.instance.getWrapString(R.string.skill_label5),
    6 to GameApp.instance.getWrapString(R.string.skill_label6),
    7 to GameApp.instance.getWrapString(R.string.skill_label7),
    8 to GameApp.instance.getWrapString(R.string.skill_label8),
)



fun Skill.getTypeName(): String {
    return skillType.getSkillTypeName()
}

fun Int.getSkillTypeName(): String {
    return when (this) {
        1 -> DamageType.DamageType1.getDamageName()
        2 -> DamageType.DamageType2.getDamageName()
        3 -> DamageType.DamageType3.getDamageName()
        4 -> DamageType.DamageType4.getDamageName()
        5 -> DamageType.DamageType5.getDamageName()
        else -> ""
    }
}

fun Int.getSkillElementName(): String {
    return SKILL_LABELS[this] ?: ""
}
