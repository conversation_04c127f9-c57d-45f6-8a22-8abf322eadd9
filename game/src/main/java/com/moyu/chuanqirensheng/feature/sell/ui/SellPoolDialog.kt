package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.core.model.Award

@Composable
fun SellPoolDialog(show: MutableState<Award?>) {
    show.value?.let {
        PanelDialog(onDismissRequest = {
            show.value = null
        }, contentBelow = {
            GameButton(
                text = stringResource(id = R.string.confirm),
                buttonStyle = ButtonStyle.Orange,
                onClick = {
                    show.value = null
                })
        }) {
            Column(
                Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.pool_title),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Column(
                    Modifier
                        .verticalScroll(rememberScrollState())
                ) {
                    AwardList(
                        award = it,
                        mainAxisAlignment = Arrangement.spacedBy(padding22),
                        param = defaultParam.copy(textColor = Color.Black)
                    )
                }
            }
        }
    }
}