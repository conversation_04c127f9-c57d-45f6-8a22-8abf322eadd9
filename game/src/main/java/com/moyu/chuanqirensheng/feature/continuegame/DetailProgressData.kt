package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.logic.battle.BattleManager
import kotlinx.serialization.Serializable

@Serializable
data class RecordItem(
    val type: Int,
    val addAge: Int,
    val number: Int = 1
) {
    companion object {
        fun create(type: Int) = RecordItem(type, BattleManager.getAge())
        fun create(type: Int, number: Int) = RecordItem(type, BattleManager.getAge(), number)
    }
}

@Serializable
data class DetailProgressData(
    val defeatEnemyRecord: MutableList<RecordItem> = mutableListOf(), // raceType

    val specialDecisionRecord1: MutableList<RecordItem> = mutableListOf(), // 野战
    val specialDecisionRecord2: MutableList<RecordItem> = mutableListOf(), // 攻城
    val specialDecisionRecord3: MutableList<RecordItem> = mutableListOf(), // 守城
    val specialDecisionRecord4: MutableList<RecordItem> = mutableListOf(), // 单挑
    val specialDecisionRecord5: MutableList<RecordItem> = mutableListOf(), // 增援
    val specialDecisionRecord6: MutableList<RecordItem> = mutableListOf(), // 仙人
    val specialDecisionRecord7: MutableList<RecordItem> = mutableListOf(), // 使用设施

    val lostAllyRecord: MutableList<RecordItem> = mutableListOf(), // raceType
    val getAllyRecord: MutableList<RecordItem> = mutableListOf(), // raceType

    val getAdventureCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
    val dropAdventureCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp

    val getBattleSkillCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
    val dropBattleSkillCardRecord: MutableList<RecordItem> = mutableListOf(), // elementTyp
)