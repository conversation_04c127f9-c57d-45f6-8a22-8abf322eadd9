package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.rankIndexOneDigitHeight
import com.moyu.chuanqirensheng.ui.theme.rankIndexOneDigitWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.getRankNumber


@Composable
fun SingleRecord(
    deathRole: RankData,
    index: Int,
    content: @Composable BoxScope.(RankData, Int) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding110)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_long),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    contentAlignment = Alignment.Center, modifier = Modifier.size(imageLarge)
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(R.drawable.level_frame),
                        contentDescription = null
                    )
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        getRankNumber(index).toList().forEach {
                            it?.let { numberDrawable ->
                                Image(
                                    contentScale = ContentScale.Fit,
                                    modifier = Modifier.size(
                                        rankIndexOneDigitWidth,
                                        rankIndexOneDigitHeight
                                    ),
                                    painter = painterResource(numberDrawable),
                                    contentDescription = index.toString(),
                                )
                            }
                        }
                    }
                }
                Box(modifier = Modifier.size(padding80)) {
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding8)
                            .clip(RoundedCornerShape(50)),
                        painter = if (deathRole.userPic.startsWith("http")) rememberAsyncImagePainter(deathRole.userPic) else painterResource(
                            id = getImageResourceDrawable(deathRole.userPic)
                        ),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(id = R.drawable.hero_frame),
                        contentDescription = null
                    )
                }
                Text(
                    modifier = Modifier.weight(1f),
                    text = deathRole.userName,
                    style = MaterialTheme.typography.h2
                )
                Box {
                    content(deathRole, index)
                }
            }
        }
    }
}
