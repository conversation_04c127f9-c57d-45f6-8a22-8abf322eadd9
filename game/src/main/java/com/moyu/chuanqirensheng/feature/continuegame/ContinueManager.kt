package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.NO_TITLE
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_RECORD
import com.moyu.chuanqirensheng.sub.datastore.KEY_RECORD_EVENT
import com.moyu.chuanqirensheng.sub.datastore.getObject
import com.moyu.chuanqirensheng.sub.datastore.getObjectFromStore2
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setObjectToStore2
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Event
import com.moyu.core.util.perPlusI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 注意，局内的未完成游戏，独立一个存档，和云存档隔离
 */
object ContinueManager {
    fun haveSaver(): Boolean {
        return getObjectFromStore2<ContinueData>(KEY_RECORD) != null
    }

    fun migrate() {
        mapData[KEY_RECORD]?.let {
            setObjectToStore2(KEY_RECORD, getObject<ContinueData>(KEY_RECORD))
            mapData.remove(KEY_RECORD)
        }
        mapData[KEY_RECORD_EVENT]?.let {
            setObjectToStore2(KEY_RECORD_EVENT, getObject<Event>(KEY_RECORD_EVENT))
            mapData.remove(KEY_RECORD_EVENT)
        }
    }

    fun recreateGame() {
        getObjectFromStore2<ContinueData>(KEY_RECORD)?.let { recordData ->
            BattleManager.you.value = recordData.you

            EventManager.resetEventRecorder(
                recordData.usedEvents.map { it.create() },
                recordData.winEvents.map { it.create() },
                recordData.loseEvents.map { it.create() }
            )

            EventManager.selectionEvents.clear()
            EventManager.selectionEvents.addAll(recordData.selectionEvents.map {
                it.create().createUUID()
            })

            DetailProgressManager.detailProgressData = recordData.records

            BattleManager.yourExp.value = recordData.yourExp
            BattleManager.yourTitle.value =
                repo.gameCore.getTitlePool().firstOrNull { it.id == recordData.yourTitle.id }
                    ?: NO_TITLE

            BattleManager.skillGameData.clear()
            BattleManager.skillGameData.addAll(recordData.skillGameData.map {
                it.create()
                    .copy(life = it.life, extraInfo = it.extraInfo)
            })
            BattleManager.battleEnchants.clear()
            BattleManager.battleEnchants.addAll(recordData.battleEnchants.map { it.create() })

            BattleManager.allyGameData.clear()
            BattleManager.allyGameData.addAll(recordData.allyGameData.filter { !it.temp }
                .map { it.create() })

            BattleManager.equipGameData.clear()
            BattleManager.equipGameData.addAll(recordData.equipGameData.map { it.create() })

            BattleManager.adventureProps.value = recordData.adventureProps
            BattleManager.battleProp.value = recordData.battleProp
            BattleManager.masterProp.value = recordData.masterProp

            BattleManager.battleRaceProps.clear()
            BattleManager.battleRaceProps.addAll(recordData.battleRaceProps)

            BattleManager.battleSkillPropMap.clear()
            BattleManager.battleSkillPropMap.putAll(recordData.battleSkillPropMap)

            BattleManager.masterSkills.clear()
            BattleManager.masterSkills.addAll(recordData.troopSkills.map { it.create() })

            BattleManager.resources.clear()
            BattleManager.resources.addAll(EMPTY_RESOURCES.perPlusI(recordData.resources))

            BattleManager.currentBgMusic.intValue = MusicManager.getRandomDungeonMusic()

            getObjectFromStore2<Event>(KEY_RECORD_EVENT)?.createOrNull()?.let { event ->
                goto(EVENT_SELECT_SCREEN)
                GameApp.globalScope.launch(Dispatchers.Main) {
                    EventManager.selectEvent(event)
                    EventManager.doEventResult(
                        event, EventManager.getOrCreateHandler(event).skipWin
                    )
                }
            } ?: kotlin.run {
                goto(EVENT_SELECT_SCREEN)
            }
        }
    }

    fun selectEvent(event: Event) {
        setObjectToStore2(KEY_RECORD_EVENT, event)
    }

    fun clearSave() {
        setObjectToStore2(KEY_RECORD_EVENT, Event(-1))
        setObjectToStore2(KEY_RECORD, null)
    }

    fun onSelections(events: List<Event>) {
        setObjectToStore2(KEY_RECORD_EVENT, Event(-1))
        setObjectToStore2(
            KEY_RECORD, ContinueData(
                you = BattleManager.you.value,
                usedEvents = EventManager.getUsedEvents(),
                winEvents = EventManager.getSucceededEvents(),
                loseEvents = EventManager.getFailedEvents(),
                selectionEvents = events,
                records = DetailProgressManager.detailProgressData,
                skillGameData = BattleManager.skillGameData,
                battleEnchants = BattleManager.battleEnchants,
                allyGameData = BattleManager.allyGameData,
                equipGameData = BattleManager.equipGameData,
                masterProp = BattleManager.masterProp.value,
                adventureProps = BattleManager.adventureProps.value,
                battleProp = BattleManager.battleProp.value,
                battleRaceProps = BattleManager.battleRaceProps,
                battleSkillPropMap = BattleManager.battleSkillPropMap,
                troopSkills = BattleManager.masterSkills,
                resources = BattleManager.resources,
                yourExp = BattleManager.yourExp.value,
                yourTitle = BattleManager.yourTitle.value
            )
        )
    }
}