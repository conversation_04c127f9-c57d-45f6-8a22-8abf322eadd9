package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.core.model.Ally

@Composable
fun IllustrationAllyPage(allies: List<Ally>) {
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Ally>>()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(allyOrderList.first())
    }
    val list = allies.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedByDescending { order.value.order?.invoke(it) }
    Box {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
        ) {
            Spacer(modifier = Modifier.size(padding12))
            Text(text = stringResource(
                R.string.discovered,
                GameIllustrationManager.unlockAllies.size,
                allies.size
            ), style = MaterialTheme.typography.h3)
            LazyVerticalGrid(
                modifier = Modifier.weight(1f),
                columns = GridCells.Fixed(3)
            ) {
                items(list.size) { index ->
                    if (GameIllustrationManager.unlockAllies.contains(list[index].mainId)) {
                        SingleAllyView(ally = list[index].copy(peek = true), textColor = Color.White)
                    } else {
                        SingleAllyView(ally = list[index].copy(peek = true), hide = true, textColor = Color.White) {
                            GameApp.instance.getWrapString(R.string.undiscovered).toast()
                        }
                    }
                }
            }
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End
            ) {
                CommonOrderView(
                    Modifier.padding(start = padding10), showOrder
                )
                CommonFilterView(
                    Modifier.padding(end = padding10), showFilter
                )
                Spacer(modifier = Modifier.size(padding10))
            }
        }
        OrderLayout(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = padding72, end = padding10),
            show = showOrder,
            filter = order,
            filterList = allyOrderList
        )
        FilterLayout(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = padding72, end = padding10),
            show = showFilter,
            filter = filter,
            filterList = allyFilterList
        )
    }
}