package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding6


val questListTabItems = mutableStateOf(
        listOf(
            GameApp.instance.getWrapString(R.string.routine_quest),
            GameApp.instance.getWrapString(R.string.one_time_quest),
        )
    )

@Composable
fun QuestAllScreen() {
    val questPagerState = rememberPagerState{
        questListTabItems.value.size
    }
    GameBackground(title = stringResource(R.string.quest)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier.weight(1f).fillMaxWidth(),
                state = questPagerState,
            ) { page ->
                when (page) {
                    0 -> DailyQuestScreen()
                    else -> OneTimeQuestScreen()
                }
            }
            NavigationTab(modifier = Modifier.graphicsLayer {
                translationY = -padding6.toPx()
            }, questPagerState, questListTabItems.value, listOf(QuestManager.dailyTasks.any {
                QuestManager.getTaskDoneFlow(it)
                        && !it.opened
            }, QuestManager.oneTimeTasks.filter { !it.isEndingTask() }.any {
                QuestManager.getTaskDoneFlow(it)
                        && !it.opened
            }))
        }
    }
}