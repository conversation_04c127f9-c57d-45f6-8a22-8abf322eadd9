package com.moyu.chuanqirensheng.feature.story

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.core.model.Event
import com.moyu.core.model.Story

object StoryManager {
    val stories = mutableStateListOf<Story>()

    fun eventInStoryBag(event: Event): <PERSON><PERSON>an {
        return true//event.storyBag == getSelectStory().unlockId
    }

    fun getUnlockedStoryIds(): List<Int> {
        return emptyList()
    }

    fun selectedEndless(): Boolean {
        return false // getSelectStory().id == STORY_ENDLESS
    }

    fun getMaxAge(): Int {
        return DifficultManager.getSelectedMap().time
    }
}