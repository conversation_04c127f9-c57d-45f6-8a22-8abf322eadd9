package com.moyu.chuanqirensheng.feature.quest

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.holiday.ui.holidaySolidRanks
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvp2Ranks
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvpRanks
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.feature.worldboss.ui.lastCrossWorldBossRanks
import com.moyu.chuanqirensheng.feature.worldboss.ui.lastWorldBossRanks
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkQuest
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHARGE_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COLLECT_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COST_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP2_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WORLD_BOSS_CROSS_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WORLD_BOSS_CROSS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WORLD_BOSS_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WORLD_BOSS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_HOLIDAY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TIME_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS_TASK
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Award
import com.moyu.core.model.Quest
import com.moyu.core.util.RANDOM
import com.moyu.core.util.subListOrEmpty
import timber.log.Timber
import java.lang.Integer.min
import kotlin.math.max

const val FOREVER = "k_)"

object QuestManager {
    val dailyTasks = mutableStateListOf<Quest>()
    val pvpTasks = mutableStateListOf<Quest>()
    val pvp2Tasks = mutableStateListOf<Quest>()
    val worldBossTasks = mutableStateListOf<Quest>()
    val worldBossCrossTasks = mutableStateListOf<Quest>()
    val warPassTasks = mutableStateListOf<Quest>()
    val warPass2Tasks = mutableStateListOf<Quest>()
    val newTasks = mutableStateListOf<Quest>()
    val oneTimeTasks = mutableStateListOf<Quest>()

    fun init() {
        createTasks()
        createPvpTasks()
        createPvp2Tasks()
        createWorldBossTasks()
        createWorldBossCrossTasks()
        createWarPassTasks()
        createWarPass2Tasks()
        createNewTasks()
        createOneTimeTasks()
        // 任务中,启动游戏可以直接标记已完成
        onTaskStartGameTime()
    }

    fun createOneTimeTasks() {
        if (oneTimeTasks.isEmpty()) {
            getListObject<Quest>(KEY_ONE_TIME_GAME_TASK).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }
                        ?.copy(done = task.done, opened = task.opened)
                }
                oneTimeTasks.addAll(tasks)
            }
        }
        val filteredTasks = repo.gameCore.getGameTaskPool().filter { it.isOneTimeTask() }
            .filterNot { pool -> oneTimeTasks.any { it.id == pool.id } }
        oneTimeTasks.addAll(filteredTasks)
        setListObject(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
    }

    fun createWarPassTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPassTasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPassTasks.addAll(tasks)
            }
        }
        if (!isSameDay(
                getLongFlowByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            // 通行证任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPassTasks.clear()
            warPassTasks.addAll(filteredTasks.filter { it.taskType == 3 }
                .take(repo.gameCore.getWarPassQuestCount()))
            setLongValueByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_WAR_PASS_TASK, warPassTasks)
    }

    fun createWarPass2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPass2Tasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS2_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass2Tasks.addAll(tasks)
            }
        }
        if (warPass2Tasks.isEmpty()) {
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPass2Tasks.clear()
            warPass2Tasks.addAll(filteredTasks.filter { it.taskType == 7 })
            setLongValueByKey(KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
        }
    }

    fun createNewTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (newTasks.isEmpty()) {
            getListObject<Quest>(KEY_NEW_TASK).let { taskList ->
                val tasks = taskList.map { task ->
                    repo.gameCore.getGameTaskById(task.id)
                        .copy(done = task.done, opened = task.opened, needRemoveCount = task.needRemoveCount)
                }
                newTasks.addAll(tasks)
            }
        }
        val canGenerateNewTasks = newTasks.isEmpty() || newTasks.all { getTaskDoneFlow(it) }
        if (canGenerateNewTasks && !isSameDay(
                getLongFlowByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            ) || DebugManager.questDone
        ) {
            // 新手任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            val showedMaxId = newTasks.maxOfOrNull { it.id } ?: 0
            newTasks.addAll(filteredTasks.filter { it.isNewTask() }.filter {
                it.id > showedMaxId
            }.take(if (DebugManager.questDone) 999 else repo.gameCore.getNewQuestCount()).map { task ->
                // 要用永久计数，但是又要移除之前的计数
                val needRemove = if (task.isMaxRecordQuest()) {
                    0
                } else {
                    val postFix = task.subType.map {
                        if (it == 0) "" else "_$it"
                    }.reduce { acc, s -> acc + s }
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
                }
                task.copy(needRemoveCount = needRemove)
            })
            // 特殊逻辑，只有刷新了，才更新，什么时候完成任务，进行刷新，什么时候才更新这个时间戳
            setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        } else {
            // 如果没有刷新，也要更新一下时间戳，保证当天完成第一章任务，无法直接刷出第二章，要等第二天才能刷出来
            setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_NEW_TASK, newTasks)
    }

    fun createTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (dailyTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_TASK).let {
                        dailyTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            // 任务：如果已经过了一天，清理任务记录
            clearDayTasks()
            onTaskStartGameDay()
        }

        // 强制设置最少1天签到
        if (getIntFlowByKey(KEY_GAME_LOGIN_DAY) == 0) {
            setIntValueByKey(KEY_GAME_LOGIN_DAY, 1)
        }
        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (dailyTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            dailyTasks.addAll(filteredTasks.filter { it.isDailyTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM)
                .take(VipManager.getExtraDailyQuest() + repo.gameCore.getDailyQuestCount())
            )

            setLongValueByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_TASK, dailyTasks)
        }
    }

    fun createPvpTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvpTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_PVP_TASK).let {
                        pvpTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            pvpTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvpTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            pvpTasks.addAll(filteredTasks.filter { it.isPvpTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP_TASK, pvpTasks)
        }
    }

    fun createPvp2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvp2Tasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_PVP2_TASK).let {
                        pvp2Tasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        } else {
            pvp2Tasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvp2Tasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            pvp2Tasks.addAll(filteredTasks.filter { it.isPvp2Task() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP2_TASK, pvp2Tasks)
        }
    }

    fun createWorldBossTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_WORLD_BOSS_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (worldBossTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_WORLD_BOSS_TASK).let {
                        worldBossTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        } else {
            worldBossTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (worldBossTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            worldBossTasks.addAll(filteredTasks.filter { it.isWorldBossTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_WORLD_BOSS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_WORLD_BOSS_TASK, worldBossTasks)
        }
    }

    fun createWorldBossCrossTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_WORLD_BOSS_CROSS_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (worldBossCrossTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_WORLD_BOSS_CROSS_TASK).let {
                        worldBossCrossTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        } else {
            worldBossCrossTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (worldBossCrossTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            worldBossCrossTasks.addAll(filteredTasks.filter { it.isCrossWorldBossTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_WORLD_BOSS_CROSS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_WORLD_BOSS_CROSS_TASK, worldBossCrossTasks)
        }
    }

    fun getTaskDoneFlow(task: Quest): Boolean {
        val taskNum = task.num
        when (task.type) {
            QuestEvent.ENDING.id -> {
                return getBooleanFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENDING.id + "_${task.subType.first()}" + "_${task.num}")
            }

            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() == 1) {
                    repo.allyManager.data.filter { it.isHero() }.size >= taskNum
                } else {
                    repo.allyManager.data.filter { !it.isHero() }.size >= taskNum
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() / 10 == 1) {
                    val star = task.subType.first() % 10
                    repo.allyManager.data.filter { it.star >= star }.size >= taskNum
                } else {
                    false
                }
            }

            QuestEvent.STAR_UP.id -> {
                val preFix = if (task.isForever()) FOREVER else ""
                return  if (task.subType.first() == 1) {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_1") >= taskNum
                } else {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_2") >= taskNum
                }
            }

            QuestEvent.TALENT_UP.id -> {
                val preFix = if (task.isForever()) FOREVER else ""
                return  if (task.subType.first() == 1) {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_1") >= taskNum
                } else {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_2") >= taskNum
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value >= taskNum
                } else {
                    PvpManager.pkWinToday.value >= taskNum
                }
            }

            QuestEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isLotteryTaskDone(task.num)
            }

            QuestEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDone(task.num)
            }

            QuestEvent.PVP_RANK.id -> {
                val realRank = lastPvpRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                    }
                    2 -> {
                        realRank.getOrNull(1)?.userId == GameApp.instance.getObjectId()
                    }
                    3 -> {
                        realRank.getOrNull(2)?.userId == GameApp.instance.getObjectId()
                    }
                    4 -> {
                        realRank.getOrNull(3)?.userId == GameApp.instance.getObjectId()
                    }
                    5 -> {
                        realRank.getOrNull(4)?.userId == GameApp.instance.getObjectId()
                    }
                    6 -> {
                        realRank.getOrNull(5)?.userId == GameApp.instance.getObjectId()
                    }
                    7 -> {
                        realRank.getOrNull(6)?.userId == GameApp.instance.getObjectId()
                    }
                    8 -> {
                        realRank.getOrNull(7)?.userId == GameApp.instance.getObjectId()
                    }
                    9 -> {
                        realRank.getOrNull(8)?.userId == GameApp.instance.getObjectId()
                    }
                    10 -> {
                        realRank.getOrNull(9)?.userId == GameApp.instance.getObjectId()
                    }
                    11 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    12 -> {
                        // 21-50名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    13 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    else -> {
                        false
                    }
                }
            }

            QuestEvent.PVP2_RANK.id -> {
                val realRank = lastPvp2Ranks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                    }
                    2 -> {
                        // 2-5名
                        val userIds = if (realRank.size > 1) {
                            realRank.subList(1, min(realRank.size, 5)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    3 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    4 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    5 -> {
                        // 21-50名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    6 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    else -> {
                        false
                    }
                }
            }

            /**
             *  24：1=第1名；2=第2名；3=第3名；4=4-5名；5=6-10名；6=11-50名；7=51-100名
             */
            QuestEvent.HOLIDAY_RANK.id -> {
                val realRank = holidaySolidRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == GameApp.instance.getObjectId()
                                || realRank.getOrNull(4)?.userId == GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10)
                            .lastOrNull()?.holidayNum ?: 9999)
                    }

                    6 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum
                            ?: 9999)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum
                            ?: 9999)
                    }

                    else -> {
                        false
                    }
                }
            }

            QuestEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDone(task.num)
            }

            QuestEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isLotteryTaskDone(task.num)
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value >= task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDone(task.num)
            }

            QuestEvent.WORLD_BOSS_RANK.id -> {
                // 世界BOSS本服排行榜任务
                val rankList = lastWorldBossRanks.value
                val currentUserId = GameApp.instance.getObjectId() ?: return false
                val userRankIndex = rankList.indexOfFirst { it.userId == currentUserId }

                if (userRankIndex == -1) return false
                val userRank = userRankIndex + 1 // Convert to 1-based ranking

                return when (task.subType.firstOrNull() ?: 0) {
                    1 -> userRank == 1 // 第1名
                    2 -> userRank == 2 // 第2名
                    3 -> userRank == 3 // 第3名
                    4 -> userRank in 4..10 // 第4~10名
                    5 -> userRank in 11..20 // 第11~20名
                    else -> false
                }
            }

            QuestEvent.WORLD_BOSS_CROSS_RANK.id -> {
                // 世界BOSS跨服排行榜任务
                val rankList = lastCrossWorldBossRanks.value
                val currentUserId = GameApp.instance.getObjectId() ?: return false
                val userRankIndex = rankList.indexOfFirst { it.userId == currentUserId }

                if (userRankIndex == -1) return false
                val userRank = userRankIndex + 1 // Convert to 1-based ranking

                return when (task.subType.firstOrNull() ?: 0) {
                    1 -> userRank == 1 // 第1名
                    2 -> userRank == 2 // 第2名
                    3 -> userRank == 3 // 第3名
                    4 -> userRank == 4 // 第4名
                    5 -> userRank == 5 // 第5名
                    6 -> userRank in 6..10 // 第6~10名
                    7 -> userRank in 11..50 // 第11~50名
                    else -> false
                }
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix) - task.needRemoveCount >= taskNum
            }
        }
    }

    fun getTaskProgressFlow(task: Quest): String {
        val taskNum = task.num
        when (task.type) {
            QuestEvent.ENDING.id -> {
                return ""
            }

            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return  if (task.subType.first() == 1) {
                    repo.allyManager.data.filter { it.isHero() }.size.toString() + "/" + taskNum
                } else {
                    repo.allyManager.data.filter { !it.isHero() }.size.toString() + "/" + taskNum
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() / 10 == 1) {
                    val star = task.subType.first() % 10
                    repo.allyManager.data.filter { it.star >= star }.size.toString() + "/" + taskNum
                } else {
                    "0/$taskNum"
                }
            }

            QuestEvent.STAR_UP.id -> {
                val preFix = if (task.isForever()) FOREVER else ""
                return  if (task.subType.first() == 1) {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_1").toString() + "/" + taskNum
                } else {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_2").toString() + "/" + taskNum
                }
            }

            QuestEvent.TALENT_UP.id -> {
                val preFix = if (task.isForever()) FOREVER else ""
                return  if (task.subType.first() == 1) {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_1").toString() + "/" + taskNum
                } else {
                    getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_2").toString() + "/" + taskNum
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    (PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value).toString() + "/" + taskNum
                } else {
                    (PvpManager.pkWinToday.value).toString() + "/" + taskNum
                }
            }

            QuestEvent.PVP_RANK.id -> {
                return ""
            }

            QuestEvent.PVP2_RANK.id -> {
                return ""
            }

            QuestEvent.HOLIDAY_RANK.id -> {
                return ""
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value.toString() + "/" + task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDoneString(task.num)
            }

            QuestEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDoneString(task.num)
            }

            QuestEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isLotteryTaskDoneString(task.num)
            }

            QuestEvent.WORLD_BOSS_RANK.id -> {
                return ""
            }

            QuestEvent.WORLD_BOSS_CROSS_RANK.id -> {
                return ""
            }
            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix).let {
                    "${it - task.needRemoveCount}/${taskNum}"
                }
            }
        }
    }

    suspend fun questReward(quest: Quest, award: Award) {
        checkQuest(quest)
        if (quest.isDailyTask()) {
            val index = dailyTasks.indexOfFirst { it.id == quest.id }
            if (!dailyTasks[index].opened) {
                dailyTasks[index] = dailyTasks[index].copy(opened = true)
                setListObject(KEY_GAME_TASK, dailyTasks)
                val realAward = if (VipManager.isDoubleQuestAward()) {
                    award + award
                } else award
                AwardManager.gainAward(realAward)
                Dialogs.awardDialog.value = realAward
            }
        } else if (quest.isNewTask()) {
            val index = newTasks.indexOfFirst { it.id == quest.id }
            if (!newTasks[index].opened) {
                newTasks[index] = newTasks[index].copy(opened = true)
                setListObject(KEY_NEW_TASK, newTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPassTask()) {
            val index = warPassTasks.indexOfFirst { it.id == quest.id }
            if (!warPassTasks[index].opened) {
                warPassTasks[index] = warPassTasks[index].copy(opened = true)
                setListObject(KEY_WAR_PASS_TASK, warPassTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPass2Task()) {
            val index = warPass2Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass2Tasks[index].opened) {
                warPass2Tasks[index] = warPass2Tasks[index].copy(opened = true)
                setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isOneTimeTask()) {
            val index = oneTimeTasks.indexOfFirst { it.id == quest.id }
            if (!oneTimeTasks[index].opened) {
                oneTimeTasks[index] = oneTimeTasks[index].copy(opened = true)
                setListObject(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isPvpTask()) {
            val index = pvpTasks.indexOfFirst { it.id == quest.id }
            if (!pvpTasks[index].opened) {
                pvpTasks[index] = pvpTasks[index].copy(opened = true)
                setListObject(KEY_GAME_PVP_TASK, pvpTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isPvp2Task()) {
            val index = pvp2Tasks.indexOfFirst { it.id == quest.id }
            if (!pvp2Tasks[index].opened) {
                pvp2Tasks[index] = pvp2Tasks[index].copy(opened = true)
                setListObject(KEY_GAME_PVP2_TASK, pvp2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWorldBossTask()) {
            val index = worldBossTasks.indexOfFirst { it.id == quest.id }
            if (!worldBossTasks[index].opened) {
                worldBossTasks[index] = worldBossTasks[index].copy(opened = true)
                setListObject(KEY_GAME_WORLD_BOSS_TASK, worldBossTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCrossWorldBossTask()) {
            val index = worldBossCrossTasks.indexOfFirst { it.id == quest.id }
            if (!worldBossCrossTasks[index].opened) {
                worldBossCrossTasks[index] = worldBossCrossTasks[index].copy(opened = true)
                setListObject(KEY_GAME_WORLD_BOSS_CROSS_TASK, worldBossCrossTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCollectTask()) {
            val index = SevenDayManager.collectTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.collectTasks[index].opened) {
                SevenDayManager.collectTasks[index] = SevenDayManager.collectTasks[index].copy(opened = true)
                setListObject(KEY_COLLECT_TASK, SevenDayManager.collectTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCostTask()) {
            val index = SevenDayManager.costTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.costTasks[index].opened) {
                SevenDayManager.costTasks[index] = SevenDayManager.costTasks[index].copy(opened = true)
                setListObject(KEY_COST_TASK, SevenDayManager.costTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        }  else if (quest.isHolidayTask()) {
            val index = HolidayManager.holidayTasks.indexOfFirst { it.id == quest.id }
            if (!HolidayManager.holidayTasks[index].opened) {
                HolidayManager.holidayTasks[index] = HolidayManager.holidayTasks[index].copy(opened = true)
                setListObject(KEY_HOLIDAY_GAME_TASK, HolidayManager.holidayTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isChargeTask()) {
            val index = SevenDayManager.chargeTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.chargeTasks[index].opened) {
                SevenDayManager.chargeTasks[index] = SevenDayManager.chargeTasks[index].copy(opened = true)
                setListObject(KEY_CHARGE_TASK, SevenDayManager.chargeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else {
            error("未知任务类型${quest.taskType}")
        }
    }

    private fun clearDayTasks() {
        dailyTasks.clear()
        mapData.keys.filter {
            it.startsWith(
                KEY_GAME_TASK_PROGRESS
            )
        }.forEach {
            setIntValueByKey(it, 0)
        }
        // todo 每日清理数据放这里
        GiftManager.onRefreshNumTheNextDay()
    }

    fun getNewQuestByPageIndex(tasks: List<Quest>, page: Int): List<Quest> {
        return tasks.subListOrEmpty(
            (page) * repo.gameCore.getNewQuestCount(),
            (page + 1) * repo.gameCore.getNewQuestCount()
        )
    }

    fun getNewQuestPageCount(tasks: List<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        if (DebugManager.questDone) return pageMax
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.done }) {
                // 当前页面有任务没完成，那就显示这一页
                return min(pageMax, page + 1)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return min(pageMax, page)
            }
        }
        return pageMax
    }

    fun getInitPageIndex(tasks: SnapshotStateList<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.opened }) {
                // 当前页面有任务没领完，那就显示这一页
                return min(pageMax, page)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return max(0, page - 1)
            }
        }
        return 0
    }

    fun canAwardNewQuestByPageIndex(tasks: List<Quest>, pageIndex: Int): Boolean {
        val subTask = getNewQuestByPageIndex(tasks, pageIndex)
        return subTask.isNotEmpty() && subTask.all { it.done }
    }

    fun getNewQuestsByIndex(index: Int): List<Quest> {
        createNewTasks()
        // 完成的任务排前面，已领取的排最后
        return getNewQuestByPageIndex(newTasks, index).map {
            it.copy(done = getTaskDoneFlow(it))
        }.sortedBy { it.order }
    }
}