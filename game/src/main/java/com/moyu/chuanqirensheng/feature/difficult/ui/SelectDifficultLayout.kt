package com.moyu.chuanqirensheng.feature.difficult.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager.maxDoneDifficultLevel
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.selectDifficultPanelHeight
import com.moyu.core.model.Difficult

@Composable
fun SelectDifficultLayout() {
    Box {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(selectDifficultPanelHeight)
                .paint(
                    painterResource(id = R.drawable.common_frame_long),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = padding10)
                .horizontalScroll(rememberScrollState()),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DifficultManager.getShowDifficulties().forEach {
                SingleDifficultCard(modifier = Modifier.graphicsLayer {
                    translationY = padding6.toPx()
                }, difficult = it)
            }
        }
        TextLabel2(modifier = Modifier.graphicsLayer {
            translationY = -padding16.toPx()
        }, text = stringResource(R.string.difficult), labelSize = LabelSize.Medium)
    }
}

@Composable
fun SingleDifficultCard(modifier: Modifier = Modifier, difficult: Difficult) {
    EffectButton(modifier = modifier.size(bottomItemSize), onClick = {
        if (DifficultManager.isDifficultLocked(difficult.id)) {
            GameApp.instance.getWrapString(R.string.unlock_difficult_tips).toast()
        } else {
            DifficultManager.select(difficult)
        }
    }) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(
                modifier = Modifier.weight(1f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = DifficultManager.getIcon(difficult.id)),
                contentDescription = null
            )
            Text(text = difficult.name, style = MaterialTheme.typography.h3, textAlign = TextAlign.Center)
        }
        if (DifficultManager.getSelected() == difficult) {
            Image(
                painter = painterResource(id = R.drawable.common_choose),
                modifier = Modifier
                    .size(imageMedium)
                    .align(Alignment.BottomEnd),
                contentDescription = null
            )
        }
        if (DifficultManager.isDifficultLocked(difficult.id)) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(imageMedium),
                painter = painterResource(R.drawable.common_lock),
                contentDescription = null
            )
        }
        if (maxDoneDifficultLevel.value + 1 == difficult.id) {
            StrokedText(
                text = stringResource(R.string.not_pass),
                style = MaterialTheme.typography.h3,
                textColor = SkillLevel5Color,
                strokeColor = Color.White,
            )
        }
    }
}
