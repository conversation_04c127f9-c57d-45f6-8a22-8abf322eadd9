package com.moyu.chuanqirensheng.feature.ending.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.share.ui.ShareEndingLayout
import com.moyu.chuanqirensheng.ui.theme.padding10

@Composable
fun EndingDetailDialog(show: MutableState<Ending?>) {
    show.value?.let { ending ->
        PanelDialog(
            onDismissRequest = {
                show.value = null
            }, contentBelow = {
                if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                    ShareEndingLayout(ending)
                }
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState()),
                    text = ending.endingText,
                    style = MaterialTheme.typography.h3, color = Color.Black
                )
                Spacer(modifier = Modifier.weight(1f))
                GameButton(text = stringResource(id = R.string.delete)) {
                    EndingManager.delete(ending)
                    show.value = null
                }
                Spacer(modifier = Modifier.size(padding10))
            }
        }
    }
}