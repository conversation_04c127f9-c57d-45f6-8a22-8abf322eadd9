package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.model.BattlePass
import com.moyu.core.model.toAward

@Composable
fun BattlePassDialog(show: MutableState<BattlePass?>) {
    show.value?.let {
        PanelDialog(onDismissRequest = {
            show.value = null
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                BattlePassItem(vip = it)
            }
        }
    }
}

@Composable
fun BattlePassItem(vip: BattlePass, itemSize: ItemSize = ItemSize.Large, callback: (() -> Unit)? = null) {
    val award = vip.toAward()
    AwardList(
        Modifier,
        award = award,
        param = defaultParam.copy(
            peek = true,
            textColor = Color.White,
            itemSize = itemSize,
            showName = false,
            frameDrawable = 3.getQualityFrame(),
            callback = callback,
        ),
    )
}
