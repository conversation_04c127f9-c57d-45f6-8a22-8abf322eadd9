package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision1
import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision2
import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision3
import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision4
import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision5
import com.moyu.chuanqirensheng.feature.quest.onTaskSpecialDecision6
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.skill.DefeatEnemy
import com.moyu.chuanqirensheng.logic.skill.GetAdventureSkillInGame
import com.moyu.chuanqirensheng.logic.skill.GetAllyCard
import com.moyu.chuanqirensheng.logic.skill.GetBattleSkillInGame
import com.moyu.chuanqirensheng.logic.skill.LoseAdventureSkillInGame
import com.moyu.chuanqirensheng.logic.skill.LoseAllyFromGame
import com.moyu.chuanqirensheng.logic.skill.LoseBattleSkillInGame
import com.moyu.chuanqirensheng.logic.skill.battleDecision
import com.moyu.chuanqirensheng.logic.skill.isUseMoney
import com.moyu.chuanqirensheng.logic.skill.isUseResource
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Pool
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleTree


object DetailProgressManager {
    var detailProgressData = DetailProgressData()

    fun onNewGame() {
        detailProgressData = DetailProgressData()
    }

    suspend fun addDefeatEnemies(enemies: List<Role>) {
        enemies.forEach {
            detailProgressData.defeatEnemyRecord.add(RecordItem.create(it.getRace().raceType))
            adventureSkillTrigger(DefeatEnemy.copy(mainId = it.getRace().raceType))
        }
    }

    suspend fun specialDecision1(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord1.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision1(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(1, typeFrom1))
    }

    suspend fun specialDecision2(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord2.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision2(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(2, typeFrom1))
    }

    suspend fun specialDecision3(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord3.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision3(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(3, typeFrom1))
    }

    suspend fun specialDecision4(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord4.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision4(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(4, typeFrom1))
    }

    suspend fun specialDecision5(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord5.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision5(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(5, typeFrom1))
    }

    suspend fun specialDecision6(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord6.add(RecordItem.create(typeFrom1))
        onTaskSpecialDecision6(typeFrom1 - 1)
        adventureSkillTrigger(battleDecision(6, typeFrom1))
    }

    suspend fun buildingDecision(typeFrom1: Int) {
        detailProgressData.specialDecisionRecord7.add(RecordItem.create(typeFrom1 - 1))
        adventureSkillTrigger(com.moyu.chuanqirensheng.logic.skill.buildingDecision(typeFrom1))
    }

    suspend fun dropInGame(target: Skill) {
        if (target.isAdventure()) {
            detailProgressData.dropAdventureCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(LoseAdventureSkillInGame.copy(mainId = target.elementType))
        } else if (target.isBattleTree()) {
            detailProgressData.dropBattleSkillCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(LoseBattleSkillInGame.copy(mainId = target.elementType))
        }
    }

    suspend fun gainInGame(target: Skill) {
        if (target.isAdventure()) {
            detailProgressData.getAdventureCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(GetAdventureSkillInGame.copy(mainId = target.elementType))
        } else if (target.isBattleTree()) {
            detailProgressData.getBattleSkillCardRecord.add(RecordItem.create(target.elementType))
            adventureSkillTrigger(GetBattleSkillInGame.copy(mainId = target.elementType))
        }
    }

    suspend fun gainInGame(target: Ally) {
        detailProgressData.getAllyRecord.add(RecordItem.create(target.getRaceType(), number = target.num))
        val race = repo.gameCore.getRaceById(target.id)
        adventureSkillTrigger(GetAllyCard.copy(mainId = race.raceType))
    }

    suspend fun dropFromGame(target: Ally) {
        detailProgressData.lostAllyRecord.add(RecordItem.create(target.getRaceType()))
        val race = repo.gameCore.getRaceById(target.id)
        adventureSkillTrigger(LoseAllyFromGame.copy(mainId = race.raceType))
    }

    fun gainedMoreThanRepeatable(pool: Pool, startAge: Int): Boolean {
        return false
    }

    fun gainedMoreThanOneTime(pool: Pool, startAge: Int): Boolean {
        return false
    }

    fun consumedMoreThanRepeatable(skill: Skill?, pool: Pool): Boolean {
        if (pool.type.first() == 14) {
            return skill?.isUseResource(pool.num.first()) == true
        } else if (pool.type.first() == 13) {
            return skill?.isUseMoney(pool.num.first()) == true
        }
        return false // 未使用，暂时空
    }

    fun consumedMoreThanOneTime(award: Award): Boolean {
        return false // 未使用，暂时空
    }
}