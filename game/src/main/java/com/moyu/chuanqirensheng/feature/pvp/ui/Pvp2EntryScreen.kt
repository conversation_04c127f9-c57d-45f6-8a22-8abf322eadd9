package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP2_NUM
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP2_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.chuanqirensheng.util.isNetTimeValid
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber

@Composable
fun Pvp2EntryScreen() {
    LaunchedEffect(Unit) {
        // 进这个页面就需要刷一下排行榜，需要领取排名任务
        Pvp2Manager.init()
        try {
            if (!isBetween23_45And00_15(getCurrentTime())) {
                if (lastPvp2Ranks.value.isEmpty()) {
                    delay(200)
                    getRanks(
                        GameApp.instance.resources.getString(
                            R.string.platform_channel
                        ), LAST_PVP2_TYPE
                    ).let {
                        lastPvp2Ranks.value =
                            json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    GameBackground(
        title = stringResource(R.string.pvp2),
        bgMask = B65,
        background = R.drawable.bg_3
    ) {
        Pvp2TopDataRow(Modifier
            .fillMaxWidth()
            .padding(top = padding6)
            .background(W30))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding12, vertical = padding100),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding22),
                stringResource(R.string.pvp2),
                R.drawable.pvp_entrance_icon,
                red = {
                    Pvp2Manager.pkNumToday.value < MAX_PVP2_NUM
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                } else {
                    pvp2Ranks.value = emptyList()
                    goto(PVP2_CHOOSE_ENEMY_SCREEN)
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.Start)
                    .padding(start = padding0),
                stringResource(R.string.pvp_quest2), R.drawable.pvp_quest_icon,
                red = {
                    QuestManager.pvp2Tasks.any {
                        QuestManager.getTaskDoneFlow(it)
                                && !it.opened
                    }
                }
            ) {
                if (isNetTimeValid()) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                    } else {
                        goto(PVP2_QUEST_SCREEN)
                    }
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding3),
                stringResource(R.string.pvp2_rank),
                R.drawable.pvp_rank_icon,
                red = {
                    false
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                } else {
                    goto(PVP2_RANK_SCREEN)
                }
            }
        }
    }
}