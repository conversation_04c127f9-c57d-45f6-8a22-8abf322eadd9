package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding16

@Composable
fun WorldBossRankAwardDialog(show: MutableState<Boolean>) {
    if (show.value) {
        PanelDialog(
            onDismissRequest = { show.value = false }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding16),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.world_boss_rank_awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(padding16))

                // 获取世界Boss排名任务
                val worldBossRankQuests = remember {
                    repo.gameCore.getGameTaskPool()
                        .filter { it.isWorldBossTask() }
                        .sortedBy { it.subType.firstOrNull() ?: 0 }
                }

                LazyColumn {
                    items(worldBossRankQuests) { quest ->
                        // TODO: 显示任务奖励信息
                        Text(
                            text = "${quest.subType.firstOrNull() ?: 0}: ${quest.name}",
                            style = MaterialTheme.typography.h3,
                            modifier = Modifier.padding(vertical = padding16)
                        )
                    }

                    // 临时显示说明
                    item {
                        Text(
                            text = stringResource(R.string.world_boss_rank_award_desc),
                            style = MaterialTheme.typography.h3,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(padding16)
                        )
                    }
                }
            }
        }
    }
}
