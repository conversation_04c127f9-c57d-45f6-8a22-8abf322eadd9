package com.moyu.chuanqirensheng.feature.ending

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.postRankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.scoreRanks
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_ENDINGS
import com.moyu.chuanqirensheng.sub.datastore.KEY_ENDING_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAX_LEVEL
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueIfBiggerByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.model.Event
import com.moyu.core.model.role.Role
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

object EndingManager {
    val endings = mutableStateListOf<Ending>()

    fun init() {
        endings.addAll(
            getListObject(KEY_ENDINGS)
        )
    }

    fun ending(ending: Ending?) {
        // 游戏结束，关闭弹窗
        Dialogs.levelUpDialog.value = false

        ending?.let {
            Dialogs.endingDialog.value = ending
            repo.inGame.value = false
            playerMusicByScreen() // 音乐
            // todo 简单判断通关
            if (it.dieReason == GameApp.instance.getWrapString(R.string.die_good)) {
                increaseIntValueByKey(KEY_ENDING_NUM)
                setIntValueIfBiggerByKey(
                    KEY_MAX_LEVEL,
                    (DifficultManager.getSelected().id - 1) * 1000 + BattleManager.adventureProps.value.age
                )
            }
        }
        uploadRank()
    }

    fun uploadRank() {
        // todo 放开lite包上传
        if ((DebugManager.uploadRank || !BuildConfig.FLAVOR.contains("Lite"))
            && (BattleManager.adventureProps.value.age >= 30)) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                try {
                    postRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                                ?: GameApp.instance.getWrapString(R.string.not_login),
                            userId = GameApp.instance.getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "skill_7019" else GameApp.instance.getAvatarUrl() ?: "0",
                            tcgValue = 0,
                            level = getIntFlowByKey(KEY_MAX_LEVEL),
                            pvpScore = PvpManager.pvpScore.value,
                            pvpLastScore = 0, // 废弃
                            pvpData = PvpManager.getPvpData(),
                            endingNum = getIntFlowByKey(KEY_ENDING_NUM),
                            talentLevel = TalentManager.talents.values.sum(),
                            electric = AwardManager.electric.value,
                            platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                            serverId = ServerManager.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        }
    }

    fun saveEnding(
        role: Role, usedEvents: List<Event>, pass: Boolean
    ): Ending? {
        if (usedEvents.isEmpty()) {
            return null
        }
        endings.removeAll { it.uuid == role.extraInfo.allyUuid }
        val lastEvent = usedEvents.last()
        val endingRank = scoreRanks(
            (DifficultManager.getSelected().id - 1) * 1000 + BattleManager.adventureProps.value.age,
            GameApp.instance.loginData.value.top50Record.take(10)
        )
        val dieReason = if (pass) GameApp.instance.getWrapString(R.string.die_good) else GameApp.instance.getWrapString(R.string.die_bad)

        val endingAge =
            if (pass) BattleManager.adventureProps.value.age else BattleManager.adventureProps.value.age - 1

        val endingTitle =
            if (BattleManager.getAge() >= StoryManager.getMaxAge() && pass) GameApp.instance.getWrapString(
                R.string.ending_quest
            ) else GameApp.instance.getWrapString(
                R.string.game_over_title
            )
        val endingLast = GameApp.instance.getWrapString(
            R.string.lasted_stage, endingAge, DifficultManager.getSelectedMap().name + "-" + DifficultManager.getSelected().name
        )
        val endingLastEvent = GameApp.instance.getWrapString(R.string.last_event) + lastEvent.name
        val rankText =
            GameApp.instance.getWrapString(R.string.ending_rank, endingRank.toString())
        val maxTitle =
            GameApp.instance.getWrapString(R.string.best_title) + (BattleManager.yourTitle.value.level + 1)
        val kill =
            GameApp.instance.getWrapString(R.string.kill_count) + DetailProgressManager.detailProgressData.defeatEnemyRecord.size
        val getAlly =
            GameApp.instance.getWrapString(R.string.gain_ally_count) + DetailProgressManager.detailProgressData.getAllyRecord.size
        val endingText =
            endingTitle + "\n" + dieReason + "\n" + endingLast + "\n" + endingLastEvent + "\n" + rankText + "\n" + maxTitle + "\n" + kill + "\n" + getAlly

        return Ending(
            uuid = role.extraInfo.allyUuid,
            ending = endingTitle,
            storyId = 0,
            pic = SkinManager.getGameMasterWithSkinDrawable(),
            dieReason = dieReason,
            rank = endingRank,
            lastEvent = lastEvent.name,
            difficult = DifficultManager.getSelected().id,
            shareTitle = if (pass) lastEvent.winText else lastEvent.loseText,
            age = endingAge,
            kill = DetailProgressManager.detailProgressData.defeatEnemyRecord.size,
            title = "Lv" + (BattleManager.yourTitle.value.level + 1)+ BattleManager.getGameMaster().name,
            endingText = endingText
        ).apply {
            if (endings.size >= 199) {
                GameApp.instance.getWrapString(R.string.story_records_max_tips).toast()
            } else {
                endings.add(this)
                setListObject(KEY_ENDINGS, endings)
            }
        }
    }

    fun delete(ending: Ending) {
        endings.removeAll { it.uuid == ending.uuid }
        setListObject(KEY_ENDINGS, endings)
    }

    fun deleteAll() {
        endings.clear()
        setListObject(KEY_ENDINGS, endings)
    }
}