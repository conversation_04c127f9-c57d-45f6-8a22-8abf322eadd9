package com.moyu.chuanqirensheng.feature.unlock

import com.moyu.chuanqirensheng.application.Dialogs.warPass2UnlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPassUnlockDialog
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_NUM
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_ALLY_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_HERO_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_DRAW_ID_PREFIX
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKeySync
import com.moyu.core.model.Unlock


const val INIT_ALLY_UNLOCK_ID = 101

const val UNLOCK_SEVEN_DAY = 17
const val UNLOCK_MENU_HERO = 2
const val UNLOCK_MENU_ALLY = 4
const val UNLOCK_MENU_TALENT = 3
const val UNLOCK_MENU_TASK = 6
const val UNLOCK_MENU_SELL = 5
const val UNLOCK_MENU_MORE = 1
const val UNLOCK_SIGN = 7
const val UNLOCK_BATTLE_PASS = 8
const val UNLOCK_BATTLE_PASS2 = 16
const val UNLOCK_NEW_QUEST = 10
const val UNLOCK_SHARE_CODE = 9
const val UNLOCK_TCG = 11
const val UNLOCK_PVP = 15
const val UNLOCK_PVP2 = 20
const val UNLOCK_LOTTERY = 18
const val UNLOCK_TOWER = 21
const val UNLOCK_RANK = 27
const val UNLOCK_WORLD_BOSS = 31


fun Unlock.isUnlockType(type: Int, subType: Int = -1): Boolean {
    return conditionType.zip(conditionNum).any {
        it.first == type && (subType == -1 || it.second == subType)
    }
}

object UnlockManager {
    val drawUnlockIds = repo.gameCore.getUnlockPool().filter { it.conditionType.first() == UnlockId.DrawId.id }.map { it.conditionNum.first() }

    fun init() {
        // null
    }

    fun getUnlockedFlow(unlock: Unlock): Boolean {
        if (DebugManager.unlockAll || unlock.initialLock == 0) return true
        val result = unlock.conditionType.mapIndexed { index, conditionType ->
            when (conditionType) {
                UnlockId.Age.id ->
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1") >= unlock.conditionNum[index]

                UnlockId.LoginDay.id -> {
                    getLoginDays() >= unlock.conditionNum[index]
                }

                UnlockId.Talent.id -> {
                    val talentLevels = TalentManager.talents.values.sum()
                    talentLevels >= unlock.conditionNum[index]
                }

                UnlockId.Code.id ->
                    // 根据本地记录的是否已解锁来判定
                    getBooleanFlowByKey(
                        KEY_UNLOCK_EVIDENCE + unlock.conditionNum[index]
                    )

                UnlockId.VipLevel.id ->
                    // 根据本地记录的是否已解锁来判定
                    VipManager.getVipLevel() >= unlock.conditionNum[index]

                UnlockId.GiftId.id -> {
                    getBooleanFlowByKey(KEY_GIFT_AWARDED + unlock.conditionNum[index])
                }
                UnlockId.Charge.id -> {
                    // 玩家是否充值过（参数1=是，0=否）
                    if (unlock.conditionNum[index] == 0) {
                        AwardManager.electric.value <= 0
                    } else {
                        AwardManager.electric.value > 0
                    }
                }
                UnlockId.Die.id -> {
                    getIntFlowByKey(KEY_DIED_IN_GAME) >= unlock.conditionNum[index]
                }
                UnlockId.Ad.id -> {
                    AwardManager.adNum.value > 0
                }
                UnlockId.NoMoney.id -> {
                    //  11=使用货币提示不足（参数1=非付费货币 2=付费货币, 3=pvp货币）
                    if (unlock.conditionNum[index] == 3) {
                        getIntFlowByKey(KEY_PVP_DIAMOND_NOT_ENOUGH) > 0
                    } else if (unlock.conditionNum[index] == 2) {
                        getIntFlowByKey(KEY_KEY_NOT_ENOUGH) > 0
                    } else {
                        getIntFlowByKey(KEY_DIAMOND_NOT_ENOUGH) > 0
                    }
                }
                UnlockId.PvpDie.id -> {
                    //  12=玩家竞技场战斗失败（参数N=连续失败N次）
                    getIntFlowByKey(KEY_DIED_IN_PVP) >= unlock.conditionNum[index]
                }
                UnlockId.ElectricMoreOrEqual.id -> {
                    AwardManager.electric.value >= unlock.conditionNum[index]
                }
                UnlockId.ElectricLess.id -> {
                    AwardManager.electric.value < unlock.conditionNum[index]
                }
                UnlockId.Coupon.id -> {
                    val isAlly = unlock.conditionNum[index] / 1000 == 1
                    val num = unlock.conditionNum[index] % 1000
                    if (isAlly) {
                        getIntFlowByKey(KEY_CONSUME_ALLY_COUPON) >= num / 10
                    } else {
                        getIntFlowByKey(KEY_CONSUME_HERO_COUPON) >= num / 10
                    }
                }
                UnlockId.DrawFailed.id -> {
                    if (unlock.conditionNum[index] == 1) {
                        // 抽兵种失败
                        getIntFlowByKey(KEY_DRAW_ALLY_FAILED) >= 1
                    } else {
                        // 抽宝物失败
                        getIntFlowByKey(KEY_DRAW_HERO_FAILED) >= 1
                    }
                }
                UnlockId.DrawId.id -> {
                    getBooleanFlowByKey(KEY_UNLOCK_DRAW_ID_PREFIX + unlock.conditionNum[index])
                }
                UnlockId.Tower.id -> {
                    TowerManager.maxLevel.value >= unlock.conditionNum[index]
                }
                else -> false
            }
        }
        return result.all { it }
    }

    suspend fun unlockCode(id: Int) {
        if (id in KEY_WAR_PASS_UNLOCK_EVIDENCE..KEY_WAR_PASS_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM) {
            // season 从1开始
            AwardManager.battlePassBought[id - KEY_WAR_PASS_UNLOCK_EVIDENCE + 1]!!.value = true
        }
        if (id in KEY_WAR_PASS2_UNLOCK_EVIDENCE..KEY_WAR_PASS2_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM) {
            // season 从1开始
            AwardManager.battlePass2Bought[id - KEY_WAR_PASS2_UNLOCK_EVIDENCE + 1]!!.value = true
        }
        setBooleanValueByKeySync(KEY_UNLOCK_EVIDENCE + id, true)
        // 【军团通行证】【英雄征服】购买解锁后该界面没有消失，并且还可以重复购买
        warPassUnlockDialog.value = false
        warPass2UnlockDialog.value = false
    }

    fun getInitAllyUnlockByIndex(index: Int): Unlock {
        return repo.gameCore.getUnlockById(INIT_ALLY_UNLOCK_ID + index)
    }

    fun getInitAllyNum(): Int {
        return (0..25).count {
            getUnlockedFlow(getInitAllyUnlockByIndex(25 - it))
        }
    }

    fun getMaxAge(): Int {
        return getIntFlowByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
            0
        )
    }
}