package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP_TYPE
import com.moyu.chuanqirensheng.feature.rank.PVP_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.RankPage
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding6

val pvpRanks = mutableStateOf(emptyList<RankData>())
val lastPvpRanks = mutableStateOf(emptyList<RankData>())

val pvpRankTabs = listOf(
    GameApp.instance.getWrapString(R.string.pvp_rank1),
    GameApp.instance.getWrapString(R.string.pvp_rank2)
)

@Composable
fun PvpRankScreen() {
    LaunchedEffect(Unit) {
        pvpRanks.value = emptyList()
        lastPvpRanks.value = emptyList()
    }
    val pagerState = rememberPagerState {
        pvpRankTabs.size
    }
    GameBackground(
        title = stringResource(R.string.pvp_rank),
        bgMask = B65, background = R.drawable.bg_force4
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(R.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h3
                )
                Spacer(modifier = Modifier.weight(1f))
                GameButton(text = stringResource(R.string.awards), buttonSize = ButtonSize.MediumMinus) {
                    Dialogs.pvpRankAwardDialog.value = true
                }
            }
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> RankPage(type = PVP_TYPE, data = pvpRanks) { rankData, rankIndex ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = stringResource(R.string.pvp_score) + "：" + (rankData.pvpScore),
                                style = MaterialTheme.typography.h3
                            )
                            Spacer(modifier = Modifier.size(padding12))
                            Text(
                                text = stringResource(
                                    R.string.today_pk,
                                    rankData.pvpData.win,
                                    rankData.pvpData.lose
                                ),
                                style = MaterialTheme.typography.h5
                            )
                        }
                    }

                    else -> {
                        RankPage(type = LAST_PVP_TYPE, data = lastPvpRanks) { rankData, rankIndex ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Text(
                                    text = stringResource(R.string.pvp_score) + "：" + (rankData.pvpScore),
                                    style = MaterialTheme.typography.h3
                                )
                                Spacer(modifier = Modifier.size(padding12))
                                Text(
                                    text = stringResource(
                                        R.string.today_pk,
                                        rankData.pvpData.win,
                                        rankData.pvpData.lose
                                    ),
                                    style = MaterialTheme.typography.h5
                                )
                            }
                        }
                    }
                }
            }
            NavigationTab(modifier = Modifier.graphicsLayer {
                translationY = -padding6.toPx()
            }, pageState = pagerState, titles = pvpRankTabs)
        }
    }
}