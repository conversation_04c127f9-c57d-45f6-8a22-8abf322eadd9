package com.moyu.chuanqirensheng.feature.limit

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_ENDING_DIAMOND_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TIME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAP_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Award
import com.moyu.core.model.GameMap
import kotlin.math.max


object GameLimitManager {
    private val gameCount = Guarded(KEY_GAME_COUNT)
    val endingDiamondGainedToday = Guarded(KEY_GAME_ENDING_DIAMOND_COUNT)

    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_GAME_TIME_COUNT), getCurrentTime())) {
                setLongValueByKey(KEY_GAME_TIME_COUNT, getCurrentTime())
                gameCount.value = 0
                endingDiamondGainedToday.value = 0

                repo.gameCore.getMapPool().forEach {
                    setBooleanValueByKey(KEY_MAP_AWARDED + it.id, false)
                }
            }
        }
    }

    fun tryPlay(callback: () -> Unit) {
        init()
        gameCount.value += 1
        callback()
    }

    fun getEndingAward(
        award: Award,
        toast: String = GameApp.instance.getWrapString(R.string.one_day_ending_diamond_limitation)
    ): Award {
        val realLimitation = if (VipManager.isDoubleEndingAward()) {
            repo.gameCore.getMaxOneDayDiamondLimit() * 2
        } else {
            repo.gameCore.getMaxOneDayDiamondLimit()
        }
        val realAward =
            if (award.diamond + endingDiamondGainedToday.value > realLimitation) {
                if (toast.isNotEmpty()) {
                    toast.toast()
                }
                val leftToGet = realLimitation - endingDiamondGainedToday.value
                endingDiamondGainedToday.value = realLimitation
                if (VipManager.isDoubleEndingAward()) {
                    award.copy(
                        diamond = max(0, leftToGet),
                        extraDiamond = max(0, leftToGet / 2)
                    )
                } else {
                    award.copy(diamond = max(0, leftToGet))
                }
            } else {
                endingDiamondGainedToday.value += award.diamond
                award
            }
        setBooleanValueByKey(KEY_FIRST_ENDING_AWARD, true)
        return realAward
    }

    fun mapAwarded(map: GameMap): Boolean {
        return getBooleanFlowByKey(KEY_MAP_AWARDED + map.id)
    }

    fun setMapAwarded(map: GameMap) {
        setBooleanValueByKey(KEY_MAP_AWARDED + map.id, true)
    }
}