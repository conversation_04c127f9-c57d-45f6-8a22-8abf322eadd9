package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.core.model.skill.Skill

@Composable
fun IllustrationSkillPage(skills: List<Skill>, unlockedSkillMainIds: List<Int>) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(padding12))
        Text(text = stringResource(
            R.string.discovered,
            unlockedSkillMainIds.size,
            skills.size
        ), style = MaterialTheme.typography.h3)
        LazyVerticalGrid(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(padding12),
            columns = GridCells.Fixed(5)
        ) {
            items(skills.size) { index ->
                if (unlockedSkillMainIds.contains(skills[index].mainId)) {
                    SingleSkillView(skill = skills[index].copy(peek = true), textColor = Color.White)
                } else {
                    SingleSkillView(skill = skills[index].copy(peek = true), hide = true, textColor = Color.White) {
                        GameApp.instance.getWrapString(R.string.undiscovered).toast()
                    }
                }
            }
        }
    }
}