package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import kotlinx.coroutines.delay

@Composable
fun NewDayCostTaskScreen() {
    val tasks = SevenDayManager.costTasks
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val leftUpdateTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(refresh.intValue) {
        SevenDayManager.createCostTasks()
        // 完成的任务排前面，已领取的排最后
        SevenDayManager.costTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }.apply {
            tasks.clear()
            tasks.addAll(this)
        }
    }
    LaunchedEffect(Unit) {
        val firstDayRewardItem = repo.gameCore.getDayRewardPool().filter { !it.isHoliday() }.firstOrNull { it.value == tasks.firstOrNull()?.id }
        if (isNetTimeValid() && firstDayRewardItem != null) {
            while (true) {
                leftUpdateTime.longValue = timeLeft(
                    getCurrentTime(), getLongFlowByKey(
                        KEY_INIT_GAME_TIME
                    ), firstDayRewardItem.getKeepDays()
                )
                if (leftUpdateTime.longValue <= 1000) {
                    delay(1000)
                    // 修改这个，上面的LauncherEffect会刷新任务
                    refresh.intValue += 1
                }
                delay(500)
            }
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (leftUpdateTime.longValue >= 0) {
            Text(
                modifier = Modifier.background(B50).padding(horizontal = padding8),
                text = stringResource(R.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),
                style = MaterialTheme.typography.h3
            )
        }
        Spacer(modifier = Modifier.size(padding4))
        tasks.forEach {
            SingleQuest(it) {
                refresh.intValue += 1
            }
        }
    }
}