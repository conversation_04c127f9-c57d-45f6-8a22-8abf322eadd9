package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.resource.CurrentPass2Point
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun BattlePass2Page() {
    val currentSeason = BattlePass2Manager.getPassSeason()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ).padding(top = padding19, bottom = padding10)
    ) {
        CurrentPass2Point(Modifier.align(Alignment.CenterHorizontally))
        Box(modifier = Modifier.fillMaxSize()) {
            CurrentPass2Item(modifier = Modifier
                .align(Alignment.CenterEnd)
                .graphicsLayer {
                    translationY = -gapMedium.toPx()
                })

            val pool = repo.gameCore.getBattlePass2Pool().filter { it.season == currentSeason }
            LazyColumn(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(bottom = padding8).padding(start = padding19),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(padding10))
                    }
                    Column {
                        Spacer(modifier = Modifier.size(padding10))
                        OnePass2Item(pool[index]) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val unlocked = (BattlePass2Manager.getCurrentWarPass()?.id
                                    ?: 0) >= pool[index].id
                                if (unlocked) {
                                    if (pool[index].unlockType == 2 && !AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                                        (GameApp.instance.getWrapString(R.string.battlepass_unlock1) + BattlePass2Manager.getPassSeason() + GameApp.instance.getWrapString(
                                            R.string.battlepass2_unlock2
                                        )).toast()
                                    } else {
                                        BattlePass2Manager.gain(pool[index])
                                    }
                                } else {
                                    Dialogs.warPassDetailDialog.value = pool[index]
                                }
                            }
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}