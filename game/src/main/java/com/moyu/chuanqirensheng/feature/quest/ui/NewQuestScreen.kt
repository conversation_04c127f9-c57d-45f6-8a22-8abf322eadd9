package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.draw.ui.DrawLevel
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.packageGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageHugeLiteFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.min


@Composable
fun NewQuestScreen() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val pagerState = rememberPagerState {
        QuestManager.getNewQuestPageCount(tasks)
    }
    val scrollToTarget = remember {
        mutableIntStateOf(-1)
    }
    LaunchedEffect(refresh.intValue.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        QuestManager.createNewTasks()
        // 完成的任务排前面，已领取的排最后
        QuestManager.newTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }.apply {
            tasks.clear()
            tasks.addAll(this)
        }
        delay(100)
        val newQuestInitPageIndex = QuestManager.getInitPageIndex(tasks)
        val missionInitPageIndex = MissionManager.getInitPageIndex()
        if (scrollToTarget.intValue == -1) {
            // 只有首次才滚动，其他时候，不要乱跳
            scrollToTarget.intValue = min(newQuestInitPageIndex, missionInitPageIndex)
            pagerState.animateScrollToPage(scrollToTarget.intValue)
        }
    }
    val scope = rememberCoroutineScope()
    GameBackground(
        background = getImageResourceDrawable("environment_${pagerState.currentPage + 1}"),
        bgMask = B50,
        title = stringResource(R.string.new_quest_title) + (pagerState.currentPage + 1) + "/" + pagerState.pageCount.toString()
    ) {
        HorizontalPager(
            modifier = Modifier.fillMaxSize(),
            state = pagerState,
        ) { page ->
            val innerTask = QuestManager.getNewQuestByPageIndex(tasks, page)
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = padding180)
                    .verticalScroll(rememberScrollState())
            ) {
                innerTask.forEach { task ->
                    SingleQuest(task) {
                        refresh.intValue += 1
                    }
                }
            }
        }
        if (pagerState.currentPage > 0) {
            EffectButton(modifier = Modifier.align(Alignment.CenterStart), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage - 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(id = R.drawable.common_arrow_left),
                    contentDescription = stringResource(
                        R.string.prev_page
                    )
                )
            }
        }
        if (pagerState.currentPage < pagerState.pageCount - 1) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage + 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(id = R.drawable.common_arrow_right),
                    contentDescription = stringResource(
                        R.string.next_page
                    )
                )
            }
        }
        Box(
            modifier = Modifier
                .height(padding180)
                .fillMaxWidth()
                .paint(
                    painter = painterResource(id = R.drawable.common_frame_long),
                    contentScale = ContentScale.FillBounds,
                )
                .padding(horizontal = padding22)
        ) {
            DrawLevel(
                modifier = Modifier
                    .align(Alignment.TopStart),
                cheatLevel = stringResource(R.string.new_task_chapter, pagerState.currentPage + 1),
            )
            val currentPageTasks =
                QuestManager.getNewQuestByPageIndex(tasks, pagerState.currentPage)
            Text(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(bottom = padding12),
                text = stringResource(
                    R.string.mission_progress_title,
                    pagerState.currentPage + 1
                ) + stringResource(
                    R.string.new_task_progress,
                    currentPageTasks.count { it.done },
                    currentPageTasks.size
                ),
                style = MaterialTheme.typography.h2
            )
            EffectButton(
                Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = padding12),
                onClick = {
                    Dialogs.newQuestAwardDialog.value = pagerState.currentPage
                }) {
                ForeverGif(
                    modifier = Modifier
                        .size(imageHugeLiteFrame)
                        .scale(1.5f),
                    resource = packageGif.gif,
                    num = packageGif.count,
                    needGap = false
                )
                Image(
                    modifier = Modifier.size(imageHugeLiteFrame),
                    painter = painterResource(id = R.drawable.chest_mission),
                    contentDescription = stringResource(R.string.mission_page_award)
                )
                if (QuestManager.canAwardNewQuestByPageIndex(
                        tasks, pagerState.currentPage
                    )
                ) {
                    if (!MissionManager.isMissionGainedByIndex(
                            pagerState.currentPage
                        )
                    ) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .size(imageSmall)
                                .graphicsLayer {
                                    translationX = padding6.toPx()
                                },
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    } else {
                        Image(
                            painter = painterResource(id = R.drawable.common_choose),
                            modifier = Modifier
                                .size(imageMedium)
                                .align(Alignment.BottomEnd),
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}