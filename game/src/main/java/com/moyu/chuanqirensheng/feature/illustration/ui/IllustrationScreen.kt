package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


val illustrationListTabItems = mutableStateOf(
    listOf(
        GameApp.instance.getWrapString(R.string.illustation_sheet1),
        GameApp.instance.getWrapString(R.string.illustation_sheet2),
        GameApp.instance.getWrapString(R.string.illustation_sheet3),
        GameApp.instance.getWrapString(R.string.illustation_sheet4),
        GameApp.instance.getWrapString(R.string.illustation_sheet5),
    )
)

@Composable
fun IllustrationScreen() {
//    val pagerState = rememberPagerState {
//        illustrationListTabItems.value.size
//    }
//    GameBackground(title = stringResource(R.string.skill_illustration)) {
//        Column(horizontalAlignment = Alignment.CenterHorizontally) {
//            HorizontalPager(
//                modifier = Modifier
//                    .weight(1f)
//                    .fillMaxWidth(),
//                state = pagerState,
//            ) { page ->
//                when (page) {
//                    0 -> IllustrationAllyPage(repo.gameCore.getAllyPool().filter { it.star == 0 })
//                    1 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isBattleTree() }, GameIllustrationManager.unlockSkill1)
//
//                    2 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isJinNang() }, GameIllustrationManager.unlockSkill2)
//
//                    3 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isTalentSpecial() }, GameIllustrationManager.unlockSkill3)
//
//                    else -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isZhenLing() }, GameIllustrationManager.unlockSkill4)
//                }
//            }
//            NavigationTab(modifier = Modifier.graphicsLayer {
//                translationY = -padding6.toPx()
//            }, pagerState, illustrationListTabItems.value)
//        }
//    }
}