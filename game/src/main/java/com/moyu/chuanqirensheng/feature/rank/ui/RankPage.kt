package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber


@Composable
fun RankPage(type: Int, data: MutableState<List<RankData>>, filter: (RankData)->Boolean = { true }, content: @Composable BoxScope.(RankData, Int) -> Unit) {
    LaunchedEffect(Unit) {
        try {
            delay(200)
            if (data.value.isEmpty()) {
                getRanks(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    ), type
                ).let {
                    data.value = json.decodeFromString(ListSerializer(RankData.serializer()), it.message).filter(filter)
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
//            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
            .fillMaxSize().padding(vertical = padding6),
        content = {
            items(data.value.size) { index ->
                Spacer(modifier = Modifier.size(padding10))
                SingleRecord(data.value[index], index + 1,
                    content = content)
            }
        }
    )
}