package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding14


@Composable
fun SellAllScreen(initTab: Int = 0) {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.sell_pool),
            GameApp.instance.getWrapString(R.string.free_goods),
            GameApp.instance.getWrapString(R.string.packages),
            GameApp.instance.getWrapString(R.string.special_goods),
            GameApp.instance.getWrapString(R.string.key_goods),
        )
    }
    val pagerState = rememberPagerState(initialPage = initTab) {
        listTabItems.size
    }
    GameBackground(title = stringResource(id = R.string.menu4)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> DrawPage()
                    1 -> SellPage(listOf(1, 31))
                    2 -> SellPage(listOf(2))
                    3 -> SellPage(listOf(3))
                    else -> SellPage(listOf(4))
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding14.toPx()
                },
                pageState = pagerState,
                titles = listTabItems,
                redIcons = List(listTabItems.size) { index ->
                    SellManager.getRedFree(index)
                }
            )
        }
    }
}
