package com.moyu.chuanqirensheng.feature.worldboss

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.postWorldBossRankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_WORLD_BOSS
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.WORLD_BOSS_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_WORLD_BOSS
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.event.createWorldBossRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_CHALLENGE_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_DAMAGE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_TIME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.model.Ally
import com.moyu.core.model.WorldBoss
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.launch
import timber.log.Timber

object WorldBossManager {
    val challengeNumToday = Guarded(KEY_WORLD_BOSS_CHALLENGE_NUM)
    val damageToday = Guarded(KEY_WORLD_BOSS_DAMAGE_TODAY)
    
    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_WORLD_BOSS_TIME_COUNT), getCurrentTime())) {
                setLongValueByKey(KEY_WORLD_BOSS_TIME_COUNT, getCurrentTime())
                challengeNumToday.value = 0
                damageToday.value = 0
            }
        }
    }
    
    fun getMaxChallengeNum(): Int {
        return try {
            repo.gameCore.getValueById(87).toInt()
        } catch (e: Exception) {
            5 // 默认每日5次挑战
        }
    }

    fun getForbiddenAllyIds(): List<Int> {
        return try {
            repo.gameCore.getValueById(88).split(",").map { it.toInt() }
        } catch (e: Exception) {
            emptyList() // 默认没有禁止上阵的英雄
        }
    }
    
    fun getTodayWorldBoss(): WorldBoss? {
        val currentDay = getCurrentDay()
        return repo.gameCore.getWorldBossByDay(currentDay)
    }
    
    private fun getCurrentDay(): Int {
        // 获取开服天数，从第1天开始，超过365天后循环
        // 使用loginData中的时间作为开服时间基准
        val serverStartTime = GameApp.instance.loginData.value.time
        val daysSinceStart = if (serverStartTime > 0) {
            ((getCurrentTime() - serverStartTime) / (24 * 60 * 60 * 1000)).toInt() + 1
        } else {
            1 // 默认第1天
        }
        return ((daysSinceStart - 1) % 365) + 1
    }
    
    fun getWorldBossRace() = repo.gameCore.getRaceById(118201)
    
    fun canChallenge(): Boolean {
        return challengeNumToday.value < getMaxChallengeNum()
    }
    
    fun addDamage(damage: Int) {
        damageToday.value += damage
    }
    
    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_WORLD_BOSS)
        return UnlockManager.getUnlockedFlow(unlock)
    }
    
    fun hasRed(): Boolean {
        return unlocked() && canChallenge()
    }
    
    fun isServerDay8OrLater(): Boolean {
        val serverStartTime = GameApp.instance.loginData.value.time
        val daysSinceStart = if (serverStartTime > 0) {
            ((getCurrentTime() - serverStartTime) / (24 * 60 * 60 * 1000)).toInt() + 1
        } else {
            1
        }
        return daysSinceStart >= 8
    }

    fun hasMultipleServers(): Boolean {
        return GameApp.instance.loginData.value.serverList.size > 1
    }
    
    fun shouldShowCrossServerRank(): Boolean {
        return isServerDay8OrLater() && hasMultipleServers()
    }

    fun pk() {
        if (!canChallenge()) {
            GameApp.instance.getWrapString(R.string.world_boss_no_challenge_left).toast()
            return
        }

        challengeNumToday.value += 1

        val worldBossRace = getWorldBossRace()
        repo.gameMode.value = GameMode(MODE_WORLD_BOSS)

        // 创建世界Boss角色
        val roleHashMap = mutableMapOf<Int, Role?>()
        roleHashMap[ENEMY_ROW1_FIRST] = createWorldBossRole(worldBossRace)

        repo.battleRoles.clear()
        repo.battleRoles.putAll(roleHashMap)
        goto(WORLD_BOSS_BATTLE_SCREEN)
    }

    fun WorldBoss.filter(ally: Ally): Boolean {
        return this.type.map {
            when (it) {
                in 101..107 -> {
                    val quality = it - 100
                    ally.isHero() || ally.quality == quality
                }

                in 201..220 -> {
                    val raceType2 = it - 200
                    ally.getRaceType2() == raceType2
                }

                in 301..304 -> {
                    val raceType = it - 300
                    ally.getRaceType() == raceType
                }

                else -> {
                    false
                }
            }
        }.any { it }
    }

    fun battleWin(allies: List<Role>, enemies: List<Role>) {
        // 计算对世界Boss造成的伤害
        val worldBoss = enemies.firstOrNull()
        if (worldBoss != null) {
            val maxHp = worldBoss.getOriginProperty().hp
            val currentHp = worldBoss.getCurrentProperty().hp
            val damage = maxHp - currentHp

            if (damage > 0) {
                addDamage(damage)
            }
        }

        // 上报排行榜数据
        uploadWorldBossRank()

        // 显示战斗胜利界面
        GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        Dialogs.gameWinDialog.value = allies + enemies
        restartEffect(dialogEffectState, winBattleEffect)
    }

    fun battleFailed(allies: List<Role>, enemies: List<Role>) {
        // 计算对世界Boss造成的伤害
        val worldBoss = enemies.firstOrNull()
        if (worldBoss != null) {
            val maxHp = worldBoss.getOriginProperty().hp
            val currentHp = worldBoss.getCurrentProperty().hp
            val damage = maxHp - currentHp

            if (damage > 0) {
                addDamage(damage)
            }
        }

        // 上报排行榜数据
        uploadWorldBossRank()

        // 显示战斗失败界面
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
    }

    private fun uploadWorldBossRank() {
        if (isNetTimeValid()) {
            GameApp.globalScope.launch {
                try {
                    postWorldBossRankData(
                        RankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                                ?: GameApp.instance.getWrapString(R.string.not_login),
                            userId = GameApp.instance.getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "skill_7019" else GameApp.instance.getAvatarUrl()
                                ?: "0",
                            worldBossDamage = damageToday.value,
                            platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                            serverId = ServerManager.getSavedServerId(),
                        )
                    )
                } catch (e: Exception) {
                    Timber.e(e, "Failed to upload world boss rank")
                }
            }
        }
    }
}
