package com.moyu.chuanqirensheng.feature.gift.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.login.UpdateTimeText
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.W10
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding780
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Gift
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun GiftDetailDialog(show: MutableState<Gift?>) {
    show.value?.takeIf { !getBooleanFlowByKey(KEY_GIFT_AWARDED + it.id) || it.limitBuy == 0 }
        ?.let { gift ->
            val gifts = mutableStateOf(GiftManager.getDisplayGifts())
            EmptyDialog(onDismissRequest = {
                GiftManager.setShowed(gift)
                show.value = null
            }) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(padding780),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Spacer(modifier = Modifier.height(padding10))
                    Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                        EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                            GiftManager.setShowed(gift)
                            show.value = null
                        }) {
                            Image(
                                modifier = Modifier
                                    .height(backIconHeight)
                                    .scale(1.2f),
                                contentScale = ContentScale.FillHeight,
                                painter = painterResource(id = R.drawable.common_exit),
                                contentDescription = stringResource(R.string.quit_page)
                            )
                        }
                        TextLabel2(
                            labelSize = LabelSize.Huge,
                            text = gift.name,
                            frame = R.drawable.common_frame4,
                            color = Color.Black
                        )
                    }
                    Text(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .padding(start = padding30)
                            .zIndex(999f),
                        text = gift.content.replace("\\n", "\n"),
                        style = MaterialTheme.typography.h1,
                        color = Color.White
                    )
                    Image(
                        modifier = Modifier
                            .size(padding300)
                            .graphicsLayer {
                                translationY = padding19.toPx()
                            },
                        painter = painterResource(id = getImageResourceDrawable(gift.pic)),
                        contentDescription = null
                    )
                    gift.label_pic.takeIf { it != "0" }?.let {
                        Image(
                            modifier = Modifier
                                .align(Alignment.Start)
                                .height(imageLarge)
                                .scale(2f)
                                .graphicsLayer {
                                    translationY = -padding19.toPx()
                                    translationX = padding22.toPx()
                                },
                            contentScale = ContentScale.FillHeight,
                            painter = painterResource(id = getImageResourceDrawable(gift.label_pic)),
                            contentDescription = null
                        )
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .background(B50)
                            .clip(RoundedCornerShape(padding10)),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                        AwardList(
                            award = sell.toAward(),
                            param = defaultParam.copy(itemSize = ItemSize.Large),
                            paddingHorizontalInDp = padding0,
                            paddingVerticalInDp = padding0,
                            mainAxisAlignment = Arrangement.SpaceEvenly
                        )
                    }
                    Spacer(modifier = Modifier.height(padding8))
                    val hasBilling = GameApp.instance.resources.getBoolean(R.bool.has_billing)
                    val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                    GameButton(
                        text = if (hasBilling) {
                            sell.getPriceTextWithUnit()
                        } else stringResource(R.string.go_and_get)
                    ) {
                        if (getBooleanFlowByKey(KEY_GIFT_AWARDED + gift.id) && gift.limitBuy != 0) {
                            // 已经买了，并且是只能买一次的东西，就不给再买
                            GameApp.instance.getWrapString(R.string.sold_out).toast()
                        } else {
                            GiftManager.setShowed(gift)
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                if (hasBilling) {
                                    BillingManager.prepay(sell) {
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            setBooleanValueByKey(KEY_GIFT_AWARDED + sell.id, true)
                                            setBooleanValueByKey(SELL_FOREVER + sell.id, true)
                                            SellManager.openGiftSell(sell)
                                            if (repo.inGame.value && !repo.gameMode.value.isAnyPvpMode()) {
                                                // todo 礼包奖励，如果在局内，直接局内也要获得盟友卡
                                                AwardManager.gainAward(Award(allies = sell.toAward().outAllies))
                                            }

                                            GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
                                        }
                                    }
                                } else {
                                    if (GameApp.instance.canShowAifadian()) {
                                        val uri: Uri = Uri.parse(sell.desc)
                                        val intent = Intent(Intent.ACTION_VIEW, uri)
                                        ContextCompat.startActivity(
                                            GameApp.instance.activity,
                                            intent,
                                            Bundle()
                                        )
                                    } else {
                                        GameApp.instance.getWrapString(R.string.cant_get_yet)
                                            .toast()
                                    }
                                }
                            }
                        }
                    }
                    if (gifts.value.isNotEmpty() && gifts.value.size > 1) {
                        Spacer(Modifier.weight(1f))
                        Row(
                            Modifier
                                .padding(horizontal = padding19)
                                .horizontalScroll(rememberScrollState()),
                            horizontalArrangement = Arrangement.spacedBy(padding6),
                            verticalAlignment = Alignment.Bottom
                        ) {
                            gifts.value.forEach {
                                val selected = show.value?.id == it.id
                                EffectButton(onClick = {
                                    Dialogs.giftDetailDialog.value = it
                                }) {
                                    Column(
                                        modifier = Modifier
                                            .clip(
                                                RoundedCornerShape(
                                                    padding8
                                                )
                                            )
                                            .background(W10)
                                            .padding(vertical = padding6),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Image(
                                            painter = painterResource(getImageResourceDrawable(it.icon)),
                                            modifier = Modifier.size(
                                                imageLargeFrame
                                            ),
                                            contentDescription = null,
                                        )
                                        if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                                            UpdateTimeText(it) { }
                                        } else {
                                            Text(
                                                text = "",
                                                style = MaterialTheme.typography.h6,
                                            )
                                        }
                                        Text(
                                            modifier = Modifier.width(imageLargeFrame * 1.4f),
                                            maxLines = LanguageManager.getLine(),
                                            minLines = LanguageManager.getLine(),
                                            textAlign = TextAlign.Center,
                                            text = it.name,
                                            color = if (selected) SkillLevel5Color else Color.White,
                                            style = MaterialTheme.typography.h4
                                        )
                                    }
                                    if (selected) {
                                        Image(
                                            modifier = Modifier
                                                .size(imageMedium)
                                                .align(Alignment.BottomCenter)
                                                .graphicsLayer {
                                                    rotationX = 180f
                                                    translationY = padding30.toPx()
                                                },
                                            painter = painterResource(R.drawable.common_arrow_down),
                                            contentDescription = null
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
}