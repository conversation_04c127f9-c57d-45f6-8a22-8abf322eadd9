package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.lottery.MAX_RESET_LOTTERY_NUM
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.button.MEDIA_GAP
import com.moyu.chuanqirensheng.screen.button.RefreshButton
import com.moyu.chuanqirensheng.screen.resource.HolidayPoint
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding69
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun HolidayLotteryLayout() {
    Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Column(Modifier.align(Alignment.End), horizontalAlignment = Alignment.CenterHorizontally) {
            RefreshButton(text = stringResource(id = R.string.reset_lottery)) {
                if (HolidayManager.canShowHoliday()) {
                    if (HolidayLotteryManager.canResetCheap()) {
                        HolidayLotteryManager.resetCheapLottery()
                    } else {
                        GameApp.instance.getWrapString(
                            R.string.reset_lottery_tips,
                            MAX_RESET_LOTTERY_NUM
                        ).toast()
                    }
                }
            }
            Text(
                text = stringResource(id = R.string.reset_lottery),
                style = MaterialTheme.typography.h3
            )
        }
        val spinning = remember {
            mutableStateOf(false)
        }
        val singleItemAngle = 360 / 8
        val targetAngle = remember {
            mutableFloatStateOf(0f)
        }
        val currentAngle by animateFloatAsState(
            targetValue = if (spinning.value) targetAngle.floatValue else targetAngle.floatValue % 360,
            animationSpec = TweenSpec(
                durationMillis = if (spinning.value) 2000 else 0,
                easing = FastOutSlowInEasing
            ),
            finishedListener = {
                spinning.value = false
            }, label = ""
        )
        Spacer(Modifier.padding(padding36))
        Box(Modifier.align(Alignment.CenterHorizontally).zIndex(-99f), contentAlignment = Alignment.Center) {
//            Image(
//                modifier = Modifier.align(Alignment.TopCenter).fillMaxWidth().graphicsLayer {
//                    translationY = -padding110.toPx()
//                    scaleX = 1.2f
//                    scaleY = 1.2f
//                },
//                painter = painterResource(id = R.drawable.lottery_decor_1),
//                contentDescription = null
//            )
            HolidayLotteryView(
                Modifier
                    .size(padding380).graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = 360 / 8f
                    }, HolidayLotteryManager.getCheapLotteryAwards(),
                HolidayLotteryManager.getCheapTurnTables(),
                HolidayLotteryManager.holidayBoughtIndex
            )
            Image(
                modifier = Modifier.align(Alignment.BottomCenter).size(padding165).graphicsLayer {
                    translationY = padding165.toPx()
                },
                painter = painterResource(id = R.drawable.lottery_decor_2),
                contentDescription = null
            )
            Image(
                modifier = Modifier.size(padding380)
                    .graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = currentAngle + 360 / 8f
                        scaleY = lotteryScale
                        scaleX = lotteryScale
                    },
                painter = painterResource(id = R.drawable.holiday_lottery_top),
                contentDescription = null
            )
        }
        Spacer(modifier = Modifier.size(padding69))
        HolidayPoint(cost = if (HolidayLotteryManager.isCheapFreeLottery()) 0 else HolidayLotteryManager.getCheapCost())
        Spacer(modifier = Modifier.size(padding6))
        GameButton(clickGap = MEDIA_GAP,
            text = stringResource(R.string.do_lottery),
            enabled = (HolidayLotteryManager.isCheapFreeLottery() || AwardManager.holidayMoney.value >= HolidayLotteryManager.getCheapCost()) && HolidayLotteryManager.canDoCheap() && HolidayManager.canShowHoliday(),
            onClick = {
                if (HolidayLotteryManager.spinning.value) {
                    // 正在转动
                } else if (!HolidayLotteryManager.isCheapFreeLottery() && AwardManager.holidayMoney.value < HolidayLotteryManager.getCheapCost()) {
                    GameApp.instance.getWrapString(R.string.holiday_money_not_enough).toast()
                } else if (!HolidayLotteryManager.canDoCheap()) {
                    GameApp.instance.getWrapString(R.string.lottery_run_out).toast()
                } else if (!HolidayManager.canShowHoliday()) {
                    GameApp.instance.getWrapString(R.string.holiday_over_time).toast()
                } else {
                    HolidayLotteryManager.spinning.value = true
                    if (!HolidayLotteryManager.isCheapFreeLottery()) {
                        AwardManager.gainHolidayMoney(-HolidayLotteryManager.getCheapCost())
                    }
                    val (award, index) = HolidayLotteryManager.lotteryCheatRandomAward()
                    targetAngle.floatValue = index * singleItemAngle.toFloat() + 360 * 10
                    spinning.value = true
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        delay(2100)
                        HolidayLotteryManager.gainCheapLotteryAward(index)
                        AwardManager.gainAward(award)
                        Dialogs.awardDialog.value = award
                        HolidayLotteryManager.spinning.value = false
                    }
                }
            })
    }
}