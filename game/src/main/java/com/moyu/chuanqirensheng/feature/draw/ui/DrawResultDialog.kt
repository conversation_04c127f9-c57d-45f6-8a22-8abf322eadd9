package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.effect.FlippableBox
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.singleDrawGif
import com.moyu.chuanqirensheng.screen.resource.AllyCouponPoint
import com.moyu.chuanqirensheng.screen.resource.HeroCouponPoint
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun DrawResultDialog(show: MutableState<Award?>) {
    show.value?.let {
        val buttonVisible = remember {
            mutableStateOf(false)
        }
        val showSize = remember {
            mutableStateOf(10)
        }
        EmptyDialog(onDismissRequest = {
            if (buttonVisible.value) {
                show.value = null
            }
        }) {
            Column(Modifier.fillMaxSize()) {
                val scrollState = rememberLazyGridState()
                LaunchedEffect(it.outAllies.size) {
                    try {
                        if (scrollState.layoutInfo.totalItemsCount > 0) {
                            scrollState.animateScrollToItem(scrollState.layoutInfo.totalItemsCount - 1)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace() // 这里你可以记录日志或处理异常
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Spacer(modifier = Modifier.size(padding6))
                    Text(
                        stringResource(R.string.saving_tips),
                        style = MaterialTheme.typography.h3,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.size(padding10))
                    LazyVerticalGrid(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = padding28, horizontal = padding36)
                            .weight(1f),
                        columns = GridCells.Fixed(3),
                        verticalArrangement = Arrangement.spacedBy(padding28),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        state = scrollState
                    ) {
                        items(it.outAllies.size) { index ->
                            Box(
                                Modifier.size(ItemSize.LargePlus.frameSize),
                                contentAlignment = Alignment.Center
                            ) {
                                FlippableBox(if (showSize.value > 10) 0 else 600, front = {
                                    Image(
                                        modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                                        painter = painterResource(
                                            id = if (it.outAllies[index].isHero()) R.drawable.hero_coupon
                                            else R.drawable.coupon_ally
                                        ),
                                        contentDescription = null
                                    )
                                }) {
                                    SingleAllyView(
                                        ally = it.outAllies[index].copy(peek = true), itemSize = ItemSize.LargePlus,
                                        showName = true,
                                        showRed = false,
                                        extraInfo = if (it.outAllies[index].num != 1) "x${it.outAllies[index].num}" else "",
                                        showNum = false,
                                        showEffect = true,
                                        textColor = Color.White
                                    )
                                }
                                GifView(
                                    modifier = Modifier.size(ItemSize.LargePlus.frameSize / 1.6f),
                                    enabled = true,
                                    gifCount = singleDrawGif.count,
                                    gifDrawable = singleDrawGif.gif,
                                    pace = 3
                                )
                            }
                        }
                    }
                }
                LaunchedEffect(it.outAllies.size) {
                    delay(1000)
                    buttonVisible.value = it.outAllies.size == showSize.value
                }
                val cost = 100
//                if (it.outAllies.firstOrNull()?.isHero() == true) {
//                    if (AwardManager.couponHero.value >= 10000) 1000 else 100
//                } else {
//                    if (AwardManager.couponAlly.value >= 10000) 1000 else 100
//                }
                AnimatedVisibility(visible = buttonVisible.value) {
                    // 底部按钮
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(bottom = padding100),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            if (it.outAllies.firstOrNull()?.isHero() == true) {
                                HeroCouponPoint(cost = cost)
                            } else {
                                AllyCouponPoint(cost = cost)
                            }
                            Spacer(modifier = Modifier.height(padding4))
                            GameButton(
//                                text = stringResource(if (cost == 100) R.string.draw_100 else R.string.draw_1000),
                                text = stringResource(R.string.draw_100),
                                enabled = if (it.outAllies.firstOrNull()?.isHero() == true) {
                                    (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= cost
                                } else {
                                    (AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) >= cost
                                },
                                buttonStyle = ButtonStyle.Orange,
                                onClick = {
                                    if (it.outAllies.firstOrNull()?.isHero() == true) {
                                        if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < cost) {
                                            GameApp.instance.getWrapString(R.string.hero_coupon_not_enough)
                                                .toast()
                                        } else {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 100
                                                DrawManager.buyHeroCoupon(10, multiple = if (cost == 100) 1 else 10)
                                            }
                                        }
                                    } else {
                                        if ((AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) < cost) {
                                            GameApp.instance.getWrapString(R.string.ally_coupon_not_enough)
                                                .toast()
                                        } else {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 100
                                                DrawManager.buyAllyCoupon(10, multiple = if (cost == 100) 1 else 10)
                                            }
                                        }
                                    }
                                })
                            if (DrawManager.canDrawQuality7()) {
                                if (it.outAllies.any { !it.isHero() }) {
                                    Text(
                                        text = stringResource(
                                            R.string.left_draw_orange,
                                            DrawManager.getAllyLeftDraw()
                                        ),
                                        style = MaterialTheme.typography.h5,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            if (it.outAllies.firstOrNull()?.isHero() == true) {
                                HeroCouponPoint()
                            } else {
                                AllyCouponPoint()
                            }
                            Spacer(modifier = Modifier.height(padding4))
                            GameButton(
                                text = stringResource(R.string.continue_draw),
                                buttonStyle = ButtonStyle.Orange,
                                enabled = if (it.outAllies.firstOrNull()?.isHero() == true) {
                                    (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= 10
                                } else {
                                    (AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) >= 10
                                },
                                onClick = {
                                    if (it.outAllies.firstOrNull()?.isHero() == true) {
                                        if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < 10) {
                                            GameApp.instance.getWrapString(R.string.hero_coupon_not_enough)
                                                .toast()
                                        } else {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 10
                                                DrawManager.buyHeroCoupon()
                                            }
                                        }
                                    } else {
                                        if ((AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) < 10) {
                                            GameApp.instance.getWrapString(R.string.ally_coupon_not_enough)
                                                .toast()
                                        } else {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                buttonVisible.value = false
                                                showSize.value = 10
                                                DrawManager.buyAllyCoupon()
                                            }
                                        }
                                    }
                                })
                            if (DrawManager.canDrawQuality7()) {
                                if (it.outAllies.any { !it.isHero() }) {
                                    Text(
                                        text = stringResource(
                                            R.string.left_draw_orange,
                                            DrawManager.getAllyLeftDraw()
                                        ),
                                        style = MaterialTheme.typography.h5,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
