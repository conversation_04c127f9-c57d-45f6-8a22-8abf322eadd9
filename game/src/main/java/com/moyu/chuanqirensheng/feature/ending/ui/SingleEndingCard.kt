package com.moyu.chuanqirensheng.feature.ending.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.logic.event.ageToProgress
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun SingleEndingCard(ending: Ending) {
    EffectButton(onClick = {
        Dialogs.endingDetailDialog.value = ending
    }) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    painter = painterResource(getImageResourceDrawable(ending.pic)),
                    modifier = Modifier.size(padding96).clip(RoundedCornerShape(50)),
                    contentScale = ContentScale.FillWidth,
                    contentDescription = null
                )
            }
            Box(modifier = Modifier.graphicsLayer {
                translationY = -padding10.toPx()
            }, contentAlignment = Alignment.Center) {
                Text(
                    modifier = Modifier.graphicsLayer {
                        translationY = padding6.toPx()
                    },
                    text = ending.title,
                    style = MaterialTheme.typography.h2,
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    textAlign = TextAlign.Center
                )
            }
            TextLabel2(text = ending.getDifficult() + ending.getDisplayEnding())
            TextLabel2(text = ending.age.ageToProgress())
        }
    }
}