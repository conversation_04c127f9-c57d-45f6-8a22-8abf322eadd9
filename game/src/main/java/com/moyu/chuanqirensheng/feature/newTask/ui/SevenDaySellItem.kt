package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.sell.ui.SellButton
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import kotlinx.coroutines.delay


@Composable
fun OneNewTaskSellItem(sell: Sell, leftUpdateTime: () -> Long) {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                delay(500)
            }
        }
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding150)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_long),
            contentDescription = null
        )
        Row(
            Modifier
                .fillMaxSize()
                .padding(horizontal = padding16),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Box(Modifier.width(padding80)) {
                Image(
                    modifier = Modifier
                        .size(padding80, padding66)
                        .graphicsLayer {
                            translationY = -padding8.toPx()
                        },
                    contentScale = ContentScale.FillWidth,
                    painter = painterResource(id = getImageResourceDrawable(sell.pic)),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier
                        .width(padding80)
                        .align(Alignment.BottomCenter)
                        .graphicsLayer {
                            translationY = padding28.toPx()
                        },
                    text = sell.name,
                    style = MaterialTheme.typography.h5,
                    color = Color.White,
                    minLines = 2,
                    textAlign = TextAlign.Center
                )
                if (sell.storage < 5000) {
                    Text(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .background(B50)
                            .padding(horizontal = padding4, vertical = padding2)
                            .clip(
                                RoundedCornerShape(50f)
                            ),
                        text = stringResource(
                            R.string.time_left
                        ) + sell.storage,
                        style = MaterialTheme.typography.h5,
                        color = Color.White
                    )
                }
                if (sell.desc2 != "0") {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .align(Alignment.TopStart).graphicsLayer {
                                translationY = -padding14.toPx()
                            }
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.shop_discount),
                            contentDescription = null,
                            modifier = Modifier.size(padding42)
                        )
                        Text(
                            text = sell.desc2,
                            style = MaterialTheme.typography.h5,
                            maxLines = 2,
                            modifier = Modifier.offset(y = -padding7)
                        )
                    }
                }
            }
            Column(
                Modifier
                    .weight(1f)
                    .graphicsLayer {
                        translationY = padding12.toPx()
                    }, horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (sell.storage < 5000) {
                    Text(
                        text = stringResource(
                            R.string.time_left
                        ) + leftUpdateTime().toDayHourMinuteSecond(),
                        style = MaterialTheme.typography.h3,
                        color = Color.White
                    )
                }
                Row(Modifier.graphicsLayer {
                    translationY = -padding4.toPx()
                }.horizontalScroll(rememberScrollState())) {
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = sell.toAward().copy(electric = 0),
                        param = defaultParam
                    )
                }

            }
            SellButton(sell = sell)
        }
    }
}