package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getPvp2ByScore
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP2_NUM
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager.filter
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager.getMockPvpData
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager.targetsFromServer
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.getStar
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber


@Composable
fun Pvp2ChooseEnemyScreen() {
    GameBackground(
        title = stringResource(R.string.pvp_choose_enemy),
        bgMask = B65,
        background = R.drawable.bg_2
    ) {
        LaunchedEffect(Pvp2Manager.pvpScore.value) {
            try {
                delay(200)
                // 拉取pk对手列表
                getPvp2ByScore(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    ), Pvp2Manager.pvpScore.value
                ).let {
                    val result =
                        json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                            .filter { it.userId != GameApp.instance.getObjectId() }.filter {
                                it.userId !in Pvp2Manager.pkTargetList
                            }.filter {
                                it.pvpData.allyIds.isNotEmpty()
                            }.distinctBy { it.userId }          // ← 这里按 userId 去重
                            .toMutableList()
                    val targetStar = listOf(1, 2, 3, 4, 5).shuffled().first()
                    if (result.size <= 30) {
                        result += (getMockPvpData {
                            it == targetStar
                        }.shuffled(RANDOM)
                            .filter {
                                it.userId !in Pvp2Manager.pkTargetList
                            })
                    }

                    val mockAllies = getMockPvpData {
                        it == targetStar
                    }
                    // 统一改成符合要求的兵种，这里保留玩家的星级
                    targetsFromServer.value = result.take(50).mapIndexed { indexOut, it ->
                        it.copy(pvpData = it.pvpData.copy(
                            allyIds = it.pvpData.allyIds.mapIndexed { index, allyId ->
                                // 保留玩家兵种星级
                                mockAllies[indexOut].pvpData.allyIds[index] + allyId.getStar() - 1
                            }
                        ))
                    }
                    Pvp2Manager.refreshTargets()
                }
            } catch (e: Exception) {
                Timber.e(e)
                GameApp.instance.getWrapString(R.string.net_error_retry).toast()
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(vertical = padding6)
        ) {
            Pvp2TopDataRow(
                Modifier
                    .fillMaxWidth()
                    .padding(top = padding6)
                    .background(W30)
            )
            Spacer(modifier = Modifier.size(padding12))
            Text(
                text = stringResource(R.string.pvp2_rule_desc),
                style = MaterialTheme.typography.h3
            )
            Spacer(modifier = Modifier.size(padding4))
            StrokedText(
                text = Pvp2Manager.getCurrentArena().desc,
                textColor = SkillLevel5Color,
                style = MaterialTheme.typography.h2
            )
            val allyList = repo.gameCore.getAllyPool()
                .filter { !it.isHero() && it.star == 1 }
                .filter { Pvp2Manager.getCurrentArena().filter(it) }
                .sortedByDescending { it.quality }
            LazyRow(
                modifier = Modifier.fillMaxWidth()  // 根据需求增减
            ) {
                items(allyList) { ally ->
                    SingleAllyView(
                        ally.copy(peek = true),
                        showName = true,
                        showNum = false,
                        itemSize = ItemSize.Medium,
                        textColor = Color.White
                    )
                }
            }
            Pvp2Manager.currentTargets.value.forEach {
                SinglePvpRecord(it) { deathRole ->
                    if (!Pvp2Manager.pkTargetList.contains(deathRole.userId)) {
                        Pvp2Manager.pk(deathRole)
                    } else {
                        GameApp.instance.getWrapString(R.string.duplicated_pk_target_tips).toast()
                    }
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            if (targetsFromServer.value.isNotEmpty()) {
                GameButton(
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Orange,
                    text = stringResource(id = R.string.refresh)
                ) {
                    Pvp2Manager.refreshTargets()
                }
                Spacer(modifier = Modifier.size(padding8))
                Text(
                    text = stringResource(
                        R.string.today_pvp_num,
                        minOf(Pvp2Manager.pkNumToday.value, MAX_PVP2_NUM),
                        MAX_PVP2_NUM
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
            }
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}