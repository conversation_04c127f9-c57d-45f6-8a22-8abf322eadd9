package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun NewQuestAwardDialog(show: MutableState<Int>) {
    show.value.takeIf { it >= 0 }?.let {
        val mission = MissionManager.getMissionByIndex(it)
        val award = mission.toAward()
        PanelDialog(onDismissRequest = {
            show.value = -1
        }, contentBelow = {
            val missionsAllDone = QuestManager.getNewQuestsByIndex(it).all { it.done }
            val awardGained = MissionManager.isMissionGainedByIndex(it)
            val buttonText = if (awardGained) {
                stringResource(id = R.string.already_got)
            } else {
                stringResource(id = R.string.gain_award)
            }
            GameButton(
                text = buttonText,
                enabled = !awardGained && missionsAllDone,
                buttonStyle = ButtonStyle.Orange,
                onClick = {
                    if (!missionsAllDone) {
                        GameApp.instance.getWrapString(R.string.quest_not_done_toast).toast()
                    } else if (awardGained) {
                        GameApp.instance.getWrapString(R.string.already_got).toast()
                    } else {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            MissionManager.gain(mission)
                        }
                        show.value = -1
                    }
                })
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    stringResource(R.string.new_task_award),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                AwardList(award = award, param = defaultParam.copy(textColor = Color.Black))
            }
        }
    }
}