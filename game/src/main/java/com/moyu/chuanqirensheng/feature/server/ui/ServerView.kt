package com.moyu.chuanqirensheng.feature.server.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus


@Composable
fun ServerView(showServerSelector: MutableState<Boolean>) {
    EffectButton(onClick = {
        showServerSelector.value = !showServerSelector.value
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            StrokedText(
                modifier = Modifier,
                text = stringResource(R.string.server) + (ServerManager.getSavedServerId() + 1),
                style = MaterialTheme.typography.h2,
            )
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(R.drawable.icon_setting),
                contentDescription = stringResource(R.string.server),
            )
        }
    }
}