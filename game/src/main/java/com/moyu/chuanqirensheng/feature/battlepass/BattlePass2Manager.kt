package com.moyu.chuanqirensheng.feature.battlepass

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkWarPass2
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.KEY_BATTLE_PASS2_GAINED
import com.moyu.chuanqirensheng.sub.datastore.KEY_BATTLE_PASS2_SEASON
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.model.BattlePass
import com.moyu.core.model.toAward

const val KEY_WAR_PASS2_UNLOCK_EVIDENCE = 10011

object BattlePass2Manager {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()
    private val season = Guarded(KEY_BATTLE_PASS2_SEASON, mutableIntStateOf(1))

    fun init() {
        repo.gameCore.getBattlePass2Pool().forEach {
            gainedMap[it.id] = GuardedB(KEY_BATTLE_PASS2_GAINED + it.id)
        }
        val needGainItems = if (AwardManager.battlePass2Bought[getPassSeason()]!!.value) {
            repo.gameCore.getBattlePass2Pool().filter { it.season == season.value }
        } else {
            repo.gameCore.getBattlePass2Pool().filter { it.season == season.value }.filter { it.unlockType == 1 }
        }
        if (needGainItems.map { gainedMap[it.id]!! }.all { it.value }) {
            if (season.value < repo.gameCore.getBattlePass2Pool().maxOf { it.season }) {
                season.value += 1
                "恭喜您已完成本赛季通行证，赛季已自动切换至${repo.gameCore.getBattlePass2Pool().first { it.season == season.value }.seasonName}赛季".toast()
            }
        }
    }

    fun getCurrentWarPass(): BattlePass? {
        return repo.gameCore.getBattlePass2Pool().lastOrNull { it.expRealTotal <= AwardManager.warPass2.value && it.season == season.value}
    }

    fun getPassSeason(): Int {
        return season.value
    }

    fun switchSeason() {
        val maxSeason = repo.gameCore.getBattlePass2Pool().maxOf { it.season }
        if (season.value >= maxSeason) {
            season.value = 1
        } else {
            season.value += 1
        }
    }
    suspend fun gain(cheat: BattlePass) {
        val gained = gainedMap[cheat.id]!!
        if (gained.value) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
            return
        }
        checkWarPass2(cheat)
        gained.value = true
        setBooleanValueByKey(KEY_BATTLE_PASS2_GAINED + cheat.id, true)
        val award = cheat.toAward()
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
        val needGainItems = if (AwardManager.battlePass2Bought[getPassSeason()]!!.value) {
            repo.gameCore.getBattlePass2Pool().filter { it.season == season.value }
        } else {
            repo.gameCore.getBattlePass2Pool().filter { it.season == season.value }.filter { it.unlockType == 1 }
        }
        if (needGainItems.map { gainedMap[it.id]!! }.all { it.value }) {
            if (season.value < repo.gameCore.getBattlePass2Pool().maxOf { it.season }) {
                season.value += 1
                "恭喜您已完成本赛季通行证，赛季已自动切换至${repo.gameCore.getBattlePass2Pool().first { it.season == season.value }.seasonName}赛季".toast()
            }
        }
    }

    fun isThisLevelGained(cheat: BattlePass): Boolean {
        return gainedMap[cheat.id]?.value ?: false
    }

    fun getCurrentLevelWarPass(): Int {
        val lastSeason = season.value - 1
        val lastSeasonExpTotal = repo.gameCore.getBattlePass2Pool().filter { it.season == lastSeason }.lastOrNull()?.expRealTotal ?: 0
        val total = AwardManager.warPass2.value
        val pre = getCurrentWarPass()?.expRealTotal ?: lastSeasonExpTotal
        return total - pre
    }

    fun hasRed(): Boolean {
        return repo.gameCore.getBattlePass2Pool()
            .filter { it.season == getPassSeason() }.any {
                val unlocked = (getCurrentWarPass()?.id ?: 0) >= it.id
                if (unlocked && !isThisLevelGained(it)) {
                    !(it.unlockType == 2 && !AwardManager.battlePass2Bought[getPassSeason()]!!.value)
                } else {
                    false
                }
            }
    }
}