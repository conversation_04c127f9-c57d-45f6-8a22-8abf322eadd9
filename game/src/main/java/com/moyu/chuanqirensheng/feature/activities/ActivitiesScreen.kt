package com.moyu.chuanqirensheng.feature.activities

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.WORLD_BOSS_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_QUEST
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_WORLD_BOSS
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.B15
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

data class ActivityItem(
    val name: () -> String,
    val route: () -> Unit,
    val show: () -> Boolean = { true },
    val subText: () -> String = { "" },
    val frame: String = "shop_label",
    val lockedToast: String = "",
    val unlock: () -> Boolean = { true },
    val red: () -> Boolean = { false }
)

val activityItems = listOf(
    ActivityItem(
        name = { GameApp.instance.getWrapString(R.string.new_task_tab2) },
        route = { goto(SEVEN_DAY_SCREEN) },
        frame = "seven_day_activity_frame",
        show = {
            SevenDayManager.show()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY).desc,
        unlock = {
            SevenDayManager.unlocked()
        },
        red = {
            SevenDayManager.hasRed()
        }
    ),
    ActivityItem(
        name = {
            if (LotteryManager.showCheap()) GameApp.instance.getWrapString(R.string.lottery_title1) else GameApp.instance.getWrapString(
                R.string.lottery_title2
            )
        },
        route = { if (LotteryManager.showCheap()) goto(LOTTERY_SCREEN1) else goto(LOTTERY_SCREEN2) },
        frame = "lottery_frame",
        unlock = {
            LotteryManager.unlocked()
        },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_LOTTERY).desc,
        red = {
            LotteryManager.hasRed()
        }
    ),
    ActivityItem(
        name = { GameApp.instance.getWrapString(R.string.new_quest) },
        route = { goto(NEW_TASK_SCREEN) },
        frame = "new_task_frame",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_NEW_QUEST).desc,
        unlock = {
            MissionManager.unlocked()
        },
        red = {
            MissionManager.hasRed()
        }
    ),
    ActivityItem(
        name = { GameApp.instance.getWrapString(R.string.world_boss) },
        route = { goto(WORLD_BOSS_SCREEN) },
        frame = "world_boss_frame",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_WORLD_BOSS).desc,
        unlock = {
            WorldBossManager.unlocked()
        },
        red = {
            WorldBossManager.hasRed()
        }
    ),
)

@Composable
fun ActivitiesScreen() {
    LaunchedEffect(Unit) {
        SevenDayManager.init()
        LotteryManager.refresh()
    }
    GameBackground(title = stringResource(R.string.activities_title)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            activityItems.forEach {
                ActivityItem(
                    moreItem = it,
                    frame = R.drawable.activity_frame_with_label
                )
            }
        }
    }
}

@Composable
fun ActivityItem(
    modifier: Modifier = Modifier,
    moreItem: ActivityItem,
    frame: Int = R.drawable.activity_frame,
    callback: () -> Unit = {}
) {
    if (moreItem.unlock()) {
        if (moreItem.show()) {
            Box(
                modifier.fillMaxWidth().height(padding120).shadow(
                    elevation = padding16, // 控制阴影的高度和扩散程度
                    shape = RoundedCornerShape(padding4) // 与素材的圆角保持一致
                )
                    .clip(RoundedCornerShape(padding4))
            ) {
                Image(
                    modifier = Modifier.fillMaxSize().padding(padding2).clip(
                        RoundedCornerShape(padding10)
                    ),
                    painter = painterResource(getImageResourceDrawable(moreItem.frame)),
                    colorFilter = ColorFilter.tint(
                        color = B15,
                        BlendMode.SrcAtop
                    ),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )

                Text(
                    text = moreItem.name(),
                    style = MaterialTheme.typography.h1,
                    modifier = Modifier.align(Alignment.TopStart)
                        .padding(top = padding26, start = padding26)
                )

                if (moreItem.subText().isNotEmpty()) {
                    Text(
                        text = moreItem.subText(),
                        style = MaterialTheme.typography.h2,
                        modifier = Modifier.align(Alignment.BottomStart)
                            .padding(bottom = padding26, start = padding26)
                    )
                }

                Box(
                    modifier = Modifier.align(Alignment.BottomEnd)
                        .padding(end = padding16, bottom = padding16),
                ) {
                    GameButton(
                        text = stringResource(R.string.enter),
                    ) {
                        callback()
                        if (moreItem.unlock()) {
                            moreItem.route()
                        } else {
                            moreItem.lockedToast.toast()
                        }
                    }
                    if (moreItem.red()) {
                        Image(
                            modifier = Modifier
                                .size(imageSmall)
                                .align(Alignment.TopEnd),
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                }
            }
        }
    }
}
