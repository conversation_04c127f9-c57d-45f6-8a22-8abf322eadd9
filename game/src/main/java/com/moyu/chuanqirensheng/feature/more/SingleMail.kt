package com.moyu.chuanqirensheng.feature.more

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.sub.language.mapToCurrentLanguage
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding400
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding90
import com.moyu.chuanqirensheng.ui.theme.qualityColor1
import com.moyu.chuanqirensheng.ui.theme.qualityColor2
import com.moyu.chuanqirensheng.ui.theme.qualityColorYellow
import com.moyu.chuanqirensheng.util.date2TimeStamp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun SingleMail(quest: EmailData) {
    LaunchedEffect(Unit) {
        MailManager.setRead(quest.id)
    }
    val backgroundColor =  if (quest.award != null) {
        qualityColorYellow
    } else if (quest.link.isNotEmpty()) {
        qualityColor2
    } else {
        qualityColor1
    }
    val borderColor = backgroundColor.copy(
        red = backgroundColor.red * 0.8f,
        green = backgroundColor.green * 0.8f,
        blue = backgroundColor.blue * 0.8f
    )
    Box(
        modifier = Modifier
            .fillMaxWidth().heightIn(padding110, padding400)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth().padding(horizontal = padding19, vertical = padding10)
        ) {
            Spacer(Modifier.size(padding6))
            Text(
                text = quest.title.mapToCurrentLanguage(),
                style = MaterialTheme.typography.h1
            )
            Spacer(Modifier.size(padding8))
            Text(
                text = quest.content.mapToCurrentLanguage(),
                style = MaterialTheme.typography.h3
            )
            quest.award?.toAward()?.let {
                AwardList(
                    award = it,
                    param = defaultParam.copy(
                        numInFrame = true,
                        showName = false,
                        itemSize = ItemSize.Medium
                    ),
                    paddingVerticalInDp = padding0,
                    paddingHorizontalInDp = padding0
                )
            }
            Spacer(Modifier.size(padding4))
            if (quest.award != null) {
                val gained = MailManager.isGained(quest)
                GameButton(
                    text =
                        if (gained) stringResource(R.string.already_got)
                        else stringResource(R.string.gain_award),
                    enabled = !gained,
                    buttonStyle = ButtonStyle.Blue,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            if (gained) {
                                GameApp.instance.getWrapString(R.string.award_got_toast).toast()
                            } else {
                                AwardManager.doNetAward(quest.awardCode)
                            }
                        }
                    })
            } else if (quest.link.isNotEmpty()) {
                GameButton(
                    text = stringResource(R.string.go),
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        val uri: Uri = quest.link.toUri()
                        val intent = Intent(Intent.ACTION_VIEW, uri.toString().toUri())
                        ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
                    })
            }
            Spacer(Modifier.size(padding8))
            GameLabel(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(end = padding12).size(padding90, padding26)) {
                Text(
                    text = date2TimeStamp(quest.createTime, "yyyy-MM-dd"),
                    style = MaterialTheme.typography.h5
                )
            }
        }
    }
}
