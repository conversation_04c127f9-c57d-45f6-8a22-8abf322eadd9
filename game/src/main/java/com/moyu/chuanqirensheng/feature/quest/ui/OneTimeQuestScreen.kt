package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.screen.common.SearchView
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.Quest

@Composable
fun OneTimeQuestScreen() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val search = remember {
        mutableStateOf("")
    }
    LaunchedEffect(refresh.intValue.toString() + search.value) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        QuestManager.createOneTimeTasks()
        // 完成的任务排前面，已领取的排最后
        QuestManager.oneTimeTasks.filter { !it.isEndingTask() }.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                if (search.value.isNotEmpty()) {
                    tasks.addAll(this.filter { it.name.contains(search.value) })
                } else {
                    tasks.addAll(this)
                }
            }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding4))
        if (BuildConfig.FLAVOR.contains("Lite")) {
            SearchView(search)
        }
        LazyColumn(modifier = Modifier
            .fillMaxSize(), content = {
            items(tasks.size) { index ->
                SingleQuest(tasks[index]) {
                    refresh.intValue += 1
                }
                Spacer(modifier = Modifier.size(padding10))
            }
        })
    }
}