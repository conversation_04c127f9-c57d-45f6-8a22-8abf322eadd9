package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel.uploadLikedData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_MAX_ONE_DAY
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_TYPE
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.RankManager
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.levelToDifficultProgress
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val rankTabs = listOf(
    GameApp.instance.getWrapString(R.string.ending_rank_title),
    GameApp.instance.getWrapString(R.string.difficult_rank_title),
    GameApp.instance.getWrapString(R.string.talent_rank_title),
    GameApp.instance.getWrapString(R.string.famous_rank)
)

val endingsNumRanks = mutableStateOf(emptyList<RankData>())
val difficultNumRanks = mutableStateOf(emptyList<RankData>())
val talentNumRanks = mutableStateOf(emptyList<RankData>())
val famousRanks = mutableStateOf(emptyList<RankData>())

@Composable
fun RankScreen() {
    LaunchedEffect(Unit) {
        endingsNumRanks.value = emptyList()
        difficultNumRanks.value = emptyList()
        talentNumRanks.value = emptyList()
        famousRanks.value = emptyList()
    }
    val pagerState = rememberPagerState {
        rankTabs.size
    }
    val likedData = remember {
        mutableStateOf(LikedData(serverId = ServerManager.getSavedServerId()))
    }
    GameBackground(title = stringResource(R.string.rank_title)) {
        StrokedText(
            modifier = Modifier.graphicsLayer {
                translationY = -padding36.toPx()
                translationX = padding22.toPx()
            },
            text = stringResource(R.string.server) + (ServerManager.getSavedServerId() + 1),
            style = MaterialTheme.typography.h3,
        )
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier.weight(1f).fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> RankPage(1, endingsNumRanks) { rankData, _->
                        // 征服榜，ending数量
                        Text(
                            text = stringResource(R.string.ending_content) + rankData.endingNum,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    1 -> RankPage(4, difficultNumRanks) { rankData, _->
                        // 难度榜
                        Text(
                            text = rankData.level.levelToDifficultProgress(),
                            style = MaterialTheme.typography.h3
                        )
                    }
                    2 -> RankPage(2, talentNumRanks) { rankData, _->
                        // 天赋榜
                        Text(
                            text = stringResource(R.string.talent_level) + rankData.talentLevel,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    else -> RankPage(FAMOUS_TYPE, famousRanks) { rankData, rankIndex ->
                        LaunchedEffect(Unit) {
                            RankManager.init()
                        }
                        DisposableEffect(Unit) {
                            onDispose {
                                if (likedData.value.isNotEmpty()) {
                                    GameApp.globalScope.launch {
                                        uploadLikedData(likedData.value)
                                    }
                                }
                            }
                        }
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            EffectButton(onClick = {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    if (RankManager.famousLiked.value >= FAMOUS_MAX_ONE_DAY) {
                                        GameApp.instance.getWrapString(R.string.top_like_count_one_day).toast()
                                    } else {
                                        RankManager.famousLiked.value += 1
                                        likedData.value = likedData.value.addLiked(rankData.userId)
                                        val award = Award(diamond = repo.gameCore.getFamousDiamond())
                                        Dialogs.awardDialog.value = award
                                        AwardManager.gainAward(award)
                                    }
                                }
                            }) {
                                Image(
                                    modifier = Modifier.size(imageLarge),
                                    painter = painterResource(id = R.drawable.common_menu_vote),
                                    contentDescription = stringResource(R.string.like)
                                )
                            }
                            Text(
                                text = stringResource(R.string.like_num) + (rankData.liked + likedData.value.getExtraNum(rankData.userId)),
                                style = MaterialTheme.typography.h3
                            )
                        }
                    }
                }
            }
            NavigationTab(modifier = Modifier.graphicsLayer {
                translationY = -padding6.toPx()
            }, pageState = pagerState, titles = rankTabs)
        }
    }
}