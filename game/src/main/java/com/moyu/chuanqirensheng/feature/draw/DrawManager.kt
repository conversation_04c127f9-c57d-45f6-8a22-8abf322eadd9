package com.moyu.chuanqirensheng.feature.draw

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.quest.onDrawCard
import com.moyu.chuanqirensheng.feature.sell.SellManager.isDrawing
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager.isThisLevelGained
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import com.moyu.core.util.nextDoubleClosure
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import java.util.Random


fun Ally.canShowEffect(): Boolean {
    return if (DrawManager.canDrawQuality7()) {
        (isHero() && quality >= 3) || (!isHero() && quality >= 7)
    } else {
        (isHero() && quality >= 3) || (!isHero() && quality >= 6)
    }
}

object DrawManager {

    fun canDrawQuality7(): Boolean {
//        return !LanguageManager.isInitChinese()
        // 只要没有领过累抽奖励的，都判定可以抽7阶
        return !isThisLevelGained(repo.gameCore.getDrawAwardPool().first())
    }

    fun getAllyCouponAward(index: Int): Award {
        val drawItems = if (!canDrawQuality7()) {
            repo.gameCore.getDrawPool().filter { it.type == 1 }
        } else {
            repo.gameCore.getDrawPool().filter { it.type == 3 }
        }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomAlly(index).nextDoubleClosure(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue < cumulativeRate) {
                return repo.gameCore.getPoolById(item.pool).toAward(random = randomAlly(index))
            }
        }
        return Award()
    }

    fun getAllyOrangeAward(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.type == 3 }
        return repo.gameCore.getPoolById(drawItems.last().pool).toAward(random = randomAlly())
    }

    fun getHeroCouponAward(index: Int): Award {
        val drawItems = if (!canDrawQuality7()) {
            repo.gameCore.getDrawPool().filter { it.type == 2 }
        } else {
            repo.gameCore.getDrawPool().filter { it.type == 4 }
        }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomHero(index).nextDoubleClosure(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue < cumulativeRate) {
                return repo.gameCore.getPoolById(item.pool).toAward(random = randomHero(index))
            }
        }
        return Award()
    }

    suspend fun buyAllyCoupon(repeat: Int = 1, multiple: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        var totalAward = Award()
        repeat(repeat) {
            var this10DrawHaveOrange = false
            val award = (1..10).map {
                // 确保橙卡
                val singleAward = if (canDrawQuality7() && needOrangeAllyThisTurn() && it == 10 && !this10DrawHaveOrange) {
                    getAllyOrangeAward().multipleHeroAndAlly(multiple)
                } else getAllyCouponAward(it).multipleHeroAndAlly(multiple)
                if (singleAward.outAllies.any { it.isAlly7() }) {
                    this10DrawHaveOrange = true
                }
                totalAward += singleAward
                Dialogs.drawResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(50)
                singleAward
            }.reduce { acc, award -> acc + award }
            if (this10DrawHaveOrange) {
                setAllyOrangeDraw(0)
            } else {
                setAllyOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON) + 10)
            }
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON, multiple)
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON_ALL, multiple)
            GameApp.globalScope.async {
                if (AwardManager.couponAlly.value < 10) {
                    val lack = 10 * multiple - AwardManager.couponAlly.value
                    AwardManager.gainAward(
                        award.copy(
                            couponAlly = -AwardManager.couponAlly.value,
                            key = -lack * repo.gameCore.getAllyCouponRate()
                        )
                    )
                } else {
                    AwardManager.gainAward(award.copy(couponAlly = -10 * multiple))
                }
                AwardManager.couponHistory.value += 10 * multiple
                award.outAllies.map { it.id }.forEach {
                    GiftManager.onDrawUnlockId(it)
                }
            }
        }
        onDrawCard(false, 10 * repeat * multiple)
        isDrawing = false
    }

    suspend fun buyHeroCoupon(repeat: Int = 1, multiple: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        var totalAward = Award()
        repeat(repeat) {
            val award = (1..10).map {
                val singleAward = getHeroCouponAward(it).multipleHeroAndAlly(multiple)
                totalAward += singleAward
                Dialogs.drawResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(50)
                singleAward
            }.reduce { acc, award -> acc + award }
            setHeroOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_HERO_COUPON) + 10 * multiple)
            increaseIntValueByKey(KEY_CONSUME_HERO_COUPON, multiple)
            increaseIntValueByKey(KEY_CONSUME_HERO_COUPON_ALL, multiple)
            GameApp.globalScope.async {
                if (AwardManager.couponHero.value < 10 * multiple) {
                    val lack = 10 * multiple - AwardManager.couponHero.value
                    AwardManager.gainAward(
                        award.copy(
                            couponHero = -AwardManager.couponHero.value,
                            key = -lack * repo.gameCore.getHeroCouponRate()
                        )
                    )
                } else {
                    AwardManager.gainAward(award.copy(couponHero = -10 * multiple))
                }
                AwardManager.couponHistory.value += 10 * multiple
                award.outAllies.map { it.id }.forEach {
                    GiftManager.onDrawUnlockId(it)
                }
            }
        }
        onDrawCard(true, 10 * repeat * multiple)
        isDrawing = false
    }


    fun randomAlly(index: Int = 0): Random {
        return Random(
            (GameApp.instance.getObjectId()?: "null").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_ALLY_COUPON_ALL
            ).toLong() * 2751 + index * 21109 + ServerManager.getSavedServerId())
    }

    fun randomHero(index: Int = 0): Random {
        return Random(
            (GameApp.instance.getObjectId()?: "null").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_HERO_COUPON_ALL
            ).toLong() * 1051 + index * 73109 + ServerManager.getSavedServerId())
    }

    fun getAllyLeftDraw(): Int {
        return 200 - getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON)
    }

    fun needOrangeAllyThisTurn(): Boolean {
        return getAllyLeftDraw() <= 10
    }

    fun setAllyOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON, value)
    }

    fun setHeroOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_HERO_COUPON, value)
    }
}