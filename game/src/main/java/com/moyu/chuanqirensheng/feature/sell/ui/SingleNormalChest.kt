package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.MaskView
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.payGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding170
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.ui.theme.shopItemWidth
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun SingleNormalChest(modifier: Modifier = Modifier, sell: Sell, content: @Composable BoxScope.() -> Unit) {
    Box(
        modifier = modifier, contentAlignment = Alignment.Center
    ) {
        val award = sell.award?: sell.toAward()
        if (sell.isRandom()) {
            Column(
                Modifier.width(shopItemWidth),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(padding14))
                EffectButton {
                    IconView(
                        resZIndex = 99f,
                        res = getImageResourceDrawable(sell.pic),
                        frame = 3.getQualityFrame()
                    ) {
                        sell.name.toast()
                    }
                    MaskView(
                        modifier = Modifier.align(Alignment.BottomCenter), text = stringResource(
                            R.string.sell_storage
                        ) + sell.storage, itemSize = ItemSize.LargePlus
                    )
                }
                Spacer(modifier = Modifier.size(padding7))
                Box(contentAlignment = Alignment.Center) {
                    Text(modifier = Modifier.graphicsLayer {
                        translationY = -padding4.toPx()
                    }, text = sell.name, style = MaterialTheme.typography.h3, color = Color.White)
                }
                Spacer(modifier = Modifier.size(padding10))
                Box {
                    content()
                }
            }
        } else if (sell.isPackageSingle()) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Spacer(modifier = Modifier.size(padding14))
                EffectButton {
                    IconView(
                        resZIndex = 99f,
                        res = getImageResourceDrawable(sell.pic),
                        frame = 3.getQualityFrame()
                    ) {
                        Dialogs.sellPoolDialog.value = award
                    }
                    MaskView(
                        modifier = Modifier.align(Alignment.BottomCenter), text = stringResource(
                            R.string.sell_storage
                        ) + sell.storage, itemSize = ItemSize.LargePlus
                    )
                }
                Spacer(modifier = Modifier.size(padding7))
                Box(contentAlignment = Alignment.Center) {
                    Text(modifier = Modifier.graphicsLayer {
                        translationY = -padding4.toPx()
                    }, text = sell.name, style = MaterialTheme.typography.h3, color = Color.White)
                }
                Spacer(modifier = Modifier.size(padding10))
                Box {
                    content()
                }
            }
        } else if (sell.isPackageExp()) {
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Box(Modifier.width(ItemSize.LargePlus.frameSize)) {
                    ForeverGif(
                        modifier = Modifier
                            .fillMaxSize()
                            .scale(1.2f), resource = payGif.gif, num = payGif.count, needGap = true
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .graphicsLayer {
                                translationY = padding22.toPx()
                            },
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.shop_draw_frame),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding8),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = getImageResourceDrawable(sell.pic)),
                        contentDescription = null
                    )
                    if (sell.storage < 5000) {
                        Text(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .graphicsLayer {
                                    translationY = padding30.toPx()
                                },
                            text = stringResource(R.string.sell_storage_tips, sell.storage),
                            style = MaterialTheme.typography.h3,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                Column(Modifier.graphicsLayer {
                    translationY = padding12.toPx()
                }, horizontalAlignment = Alignment.CenterHorizontally) {
                    TextLabel2(
                        text = stringResource(R.string.package_content),
                        labelSize = LabelSize.Large,
                        frame = R.drawable.common_frame3
                    )
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = award.copy(electric = 0),
                        mainAxisAlignment = Arrangement.spacedBy(padding22),
                    )
                    Box {
                        content()
                    }
                }
            }
        } else if (sell.isMonthCard()) {
            val refresh = remember {
                mutableIntStateOf(0)
            }
            LaunchedEffect(refresh) {
                SellManager.init()
            }
            val leftUpdateTime = remember {
                mutableLongStateOf(0L)
            }
            val leftStorage = remember {
                mutableStateOf(MonthCardManager.getLeftDay(sell.id))
            }
            LaunchedEffect(refresh) {
                refreshNetTime()
                if (isNetTimeValid()) {
                    while (true) {
                        leftUpdateTime.longValue =
                            millisToMidnight(getCurrentTime())
                        leftStorage.value = MonthCardManager.getLeftDay(sell.id)
                        if (leftUpdateTime.longValue <= 1000) {
                            delay(1000)
                            // 修改这个，上面的LauncherEffect会刷新任务
                            refresh.intValue += 1
                        }
                        delay(500)
                    }
                }
            }
            Box(
                modifier = Modifier
                    .size(padding360, padding170)
                    .shadow(
                        elevation = padding16,
                        shape = RoundedCornerShape(padding8)
                    )
                    .clip(RoundedCornerShape(padding8)) // Clip only the background
                    .paint(
                        painterResource(R.drawable.sell_month_card_frame),
                        contentScale = ContentScale.FillBounds
                    )
            ) {
                Row(
                    modifier = Modifier
                        .size(padding360, padding170), // Match size but no clipping
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Column(
                        Modifier.fillMaxHeight(),
                        verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Text(
                            text = sell.name,
                            style = MaterialTheme.typography.h1,
                            modifier = Modifier
                                .padding(start = padding6, top = padding8)
                        )
                        Row(
                            Modifier
                                .size(padding180, padding26)
                                .paint(
                                    painterResource(R.drawable.sell_label),
                                    contentScale = ContentScale.FillBounds
                                )
                                .graphicsLayer {
                                    translationX = -padding12.toPx()
                                },
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                modifier = Modifier.graphicsLayer {
                                    translationX = padding10.toPx()
                                },
                                text = stringResource(R.string.month_card_tips),
                                style = MaterialTheme.typography.h5
                            )
                        }
                        Row(modifier.animateContentSize()) {
                            if (!MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Column {
                                    Text(
                                        text = stringResource(R.string.month_card_immediate),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        Modifier,
                                        award = award.copy(electric = 0),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                                Spacer(Modifier.size(padding4))
                                Spacer(
                                    Modifier
                                        .size(padding2, padding60)
                                        .clip(RoundedCornerShape(50f))
                                        .background(W50)
                                )
                                Spacer(Modifier.size(padding4))
                            }
                            Box(contentAlignment = Alignment.Center) {
                                Column {
                                    Text(
                                        text = if (MonthCardManager.getItemById(sell.id)?.expired() != false || MonthCardManager.canCardGainDailyAward(
                                                        sellId = sell.id
                                            )
                                        ) stringResource(R.string.month_card_day) else stringResource(
                                            R.string.task_refresh_left_time
                                        ) + leftUpdateTime.longValue.millisToHoursMinutesSeconds(),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        award = repo.gameCore.getPoolById(sell.itemId2).toAward(),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                            }
                            if (MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Spacer(Modifier.size(padding4))
                                GameButton(
                                    modifier = Modifier.graphicsLayer {
                                        translationY = padding19.toPx()
                                    },
                                    text = stringResource(R.string.gain_award),
                                    buttonStyle = ButtonStyle.Orange,
                                    buttonSize = ButtonSize.MediumMinus
                                ) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        MonthCardManager.gainDailyAward(sell)
                                    }
                                }
                            }
                        }
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxHeight(), // Allow overflow
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(Modifier.size(padding6))
                        Box(contentAlignment = Alignment.Center) {
                            Image(
                                modifier = Modifier
                                    .size(padding84)
                                    .scale(1.2f), // Ensure it renders above other elements
                                painter = painterResource(getImageResourceDrawable(sell.pic)),
                                contentDescription = null
                            )

                            leftStorage.value?.let {
                                Text(
                                    text = stringResource(R.string.time_left) + it + stringResource(
                                        R.string.time_left_day
                                    ),
                                    style = MaterialTheme.typography.h2,
                                    modifier = Modifier.clip(RoundedCornerShape(padding2))
                                        .background(B50)
                                        .padding(horizontal = padding3, vertical = padding1)
                                )
                            }
                        }
                        Box {
                            content()
                        }
                    }
                }
            }
        } else {
            Column(
                Modifier.width(shopItemWidth),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(contentAlignment = Alignment.Center) {
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = award.copy(electric = 0),
                        param = defaultParam.copy(
                            textColor = Color.White,
                            itemSize = ItemSize.LargePlus,
                            minLine = 2
                        ),
                        mainAxisAlignment = Arrangement.Center,
                    )
                    if (sell.storage in 2..999) {
                        Text(
                            modifier = Modifier
                                .graphicsLayer {
                                    translationY = -padding6.toPx()
                                },
                            text = stringResource(R.string.sell_left_tips, sell.storage),
                            style = MaterialTheme.typography.h4,
                            color = Color.White
                        )
                    }
                }
                Box {
                    content()
                }
            }
        }
        if (sell.desc2 != "0") {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .offset(x = if (sell.isMonthCard()) padding150 else -padding34, y = padding12).scale(
                        if (sell.isPackageExp()) 1.25f else 1.0f
                    )
            ) {
                Image(
                    painter = painterResource(id = R.drawable.shop_discount),
                    contentDescription = null,
                    modifier = Modifier.size(padding40)
                )
                Text(
                    text = sell.desc2,
                    style = MaterialTheme.typography.h6,
                    modifier = Modifier.width(padding34).offset(y = -padding7),
                    maxLines = 2,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}