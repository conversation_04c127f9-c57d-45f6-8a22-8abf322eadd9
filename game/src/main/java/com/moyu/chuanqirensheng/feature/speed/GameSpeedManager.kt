package com.moyu.chuanqirensheng.feature.speed

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


object GameSpeedManager: GameSpeedController {
    private const val baseAnimDuration = 400
    private const val shortDurationFactor = 3
    private var speed by mutableStateOf(gameSpeeds[0])

    override fun getCurrentSpeed() = speed

    override fun setSpeed(speed: Int) {
        GameSpeedManager.speed = gameSpeeds[speed]
    }

    override fun getSpeeds(): List<Speed> {
        return gameSpeeds
    }

    override fun nextSpeed() {
        GameApp.globalScope.launch(Dispatchers.Main) {
            val maxSpeedIndex = if (VipManager.isSpeed10Unlocked()) {
                gameSpeeds.lastIndex
            } else {
                gameSpeeds.lastIndex - 1
            }
            speed = if (getCurrentSpeed().index == maxSpeedIndex) {
                gameSpeeds.first()
            } else {
                val index = gameSpeeds.indexOf(speed) + 1
                gameSpeeds[index]
            }
            setIntValueByKey(KEY_SPEED, getCurrentSpeed().index)
        }
    }

    override fun animDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor).toLong()
    }

    override fun stop(): Boolean {
        return speed.stop
    }

    override fun shortDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor / shortDurationFactor).toLong()
    }

    override fun longDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor * 3).toLong()
    }

    override fun fixedLongDuration(): Long {
        return (speed.animationDurationFactor * 3).toLong()
    }

    override fun isStop(): Boolean {
        return speed.stop || GuideManager.pauseGame()
    }
}