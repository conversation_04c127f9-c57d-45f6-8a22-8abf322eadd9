package com.moyu.chuanqirensheng.feature.vip.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.MovableImage
import com.moyu.chuanqirensheng.screen.effect.vipGif
import com.moyu.chuanqirensheng.screen.resource.CurrentElectricPoint
import com.moyu.chuanqirensheng.screen.resource.VipLevel
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Vip
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun VipScreen() {
    GameBackground(title = stringResource(R.string.vip)) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding19)
        ) {
            CurrentElectricPoint(Modifier.align(Alignment.CenterHorizontally))
            Spacer(modifier = Modifier.size(padding10))
            Box(modifier = Modifier.fillMaxSize()) {
                val pool = repo.gameCore.getVipPool()
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = padding19),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    items(pool.size) { index ->
                        if (index == 0) {
                            Spacer(modifier = Modifier.size(padding10))
                        }
                        Spacer(modifier = Modifier.size(padding10))
                        Column {
                            OneCheatItem(pool[index]) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    val unlocked = VipManager.getVipLevel() >= pool[index].level
                                    if (unlocked) {
                                        VipManager.gain(pool[index])
                                    } else {
                                        Dialogs.vipDetailDialog.value = pool[index]
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}

@Composable
fun VipAllyView(poolId: Int, isPass: Boolean = false) {
    val award = repo.gameCore.getPoolById(poolId).toAward()
    // 通行证第二种就不是ally奖励，所以这种情况直接显示award
    award.outAllies.firstOrNull()?.let { ally ->
        EffectButton(onClick = {
            Dialogs.allyDetailDialog.value = ally.copy(peek = true)
        }) {
            val cardSize = CardSize.Large
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(
                    modifier = Modifier.width(cardSize.width),
                    contentAlignment = Alignment.TopCenter
                ) {
                    Image(
                        modifier = Modifier.fillMaxWidth(),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.common_frame3),
                        contentDescription = null
                    )
                    Text(
                        modifier = Modifier.padding(top = cardSize.height / 20f),
                        text = stringResource(if (isPass) R.string.pass_ally_title else R.string.vip_ally_title),
                        style = MaterialTheme.typography.h1,
                        textAlign = TextAlign.Center,
                    )
                }
                Box(Modifier.size(cardSize.width, cardSize.height)) {
                    MovableImage(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(vertical = padding16)
                            .clip(RoundedCornerShape(cardSize.getRadius())),
                        imageResource = getImageResourceDrawable(ally.getRace().pic),
                    )
                    ForeverGif(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .size(cardSize.width, cardSize.height)
                            .graphicsLayer {
                                scaleX = 0.85f
                            },
                        resource = vipGif.gif,
                        num = vipGif.count,
                        contentScale = ContentScale.FillWidth,
                        needGap = true
                    )
                }
                Box(contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier
                            .width(ItemSize.Huge.frameSize)
                            .scale(2f)
                            .graphicsLayer {
                                translationY = padding1.toPx()
                            },
                        painter = painterResource(id = R.drawable.shop_name_frame),
                        contentScale = ContentScale.FillWidth,
                        contentDescription = null
                    )
                    Text(text = ally.name, style = MaterialTheme.typography.h1, color = Color.White)
                }
            }
        }
    } ?: AwardList(
        award = award,
        param = defaultParam.copy(itemSize = ItemSize.Huge, peek = true, textColor = Color.White)
    )
}

@Composable
fun OneCheatItem(cheat: Vip, callback: () -> Unit) {
    EffectButton(onClick = callback
    ) {
        Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Box(Modifier.size(cheatFrameWidth, cheatFrameHeight)) {
                VipAwards(cheat, callback)
                VipLevel(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .offset(y = -padding10),
                    cheatLevel = cheat.level
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(padding100)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(R.drawable.common_frame_long),
                    contentDescription = null
                )
                Column(Modifier.padding(padding12)) {
                    Text(
                        stringResource(R.string.electric_award, cheat.num),
                        style = MaterialTheme.typography.h2,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.size(padding10))
                    Text(text = cheat.desc, style = MaterialTheme.typography.h3, color = Color.White)
                }
            }
        }
    }
}

@Composable
fun VipAwards(cheat: Vip, callback: () -> Unit) {
    val unlocked = VipManager.getVipLevel() >= cheat.level
    Box(contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding2),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = if (unlocked) R.drawable.equipment_frame else R.drawable.equipment_frame_unlock),
            contentDescription = null
        )
        Column {
            Spacer(modifier = Modifier.size(padding10))
            if (cheat.effectType == 1) {
                val award = cheat.toAward()
                AwardList(
                    Modifier,
                    award = award,
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        textFrameDrawable = R.drawable.shop_name_frame,
                        textFrameDrawableYPadding = padding2,
                        callback = if (unlocked) callback else null
                    ),
                )
            } else {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    IconView(
                        res = getImageResourceDrawable(cheat.pic),
                        resZIndex = 99f, // 不透明框，需要res在上面
                        frame = 3.getQualityFrame(),
                        itemSize = ItemSize.Large,
                        showName = false,
                        callback = { callback.invoke() })
                    Box(modifier = Modifier.graphicsLayer {
//                        translationY = -ItemSize.Large.frameSize.toPx() / 10
                    }, contentAlignment = Alignment.Center) {
                        Image(
                            modifier = Modifier
                                .width(ItemSize.Large.frameSize)
                                .scale(1.8f)
                                .graphicsLayer {
                                    translationY = ItemSize.Large.frameSize.toPx() / 20
                                },
                            painter = painterResource(id = R.drawable.shop_name_frame),
                            contentScale = ContentScale.FillWidth,
                            contentDescription = null
                        )
                        Text(
                            modifier = Modifier.graphicsLayer {
                                translationY = ItemSize.Large.frameSize.toPx() / 15
                            },
                            text = cheat.name,
                            style = ItemSize.Large.getTextStyle(),
                            maxLines = 1,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(padding6),
                contentAlignment = Alignment.Center
            ) {
                if (VipManager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
        } else {
            // null
        }
    }
}