package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager.getDrawLevel
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager.isThisLevelGained
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.resource.CurrentHistoryCouponPoint
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.core.model.DrawAward
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun DrawScreen() {
    val refreshInt = remember {
        mutableIntStateOf(0)
    }
    GameBackground(
        title = stringResource(id = R.string.history_coupon),
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding19))
            CurrentHistoryCouponPoint(Modifier.align(Alignment.CenterHorizontally))
            Spacer(modifier = Modifier.size(padding10))
            val pool = repo.gameCore.getDrawAwardPool()
            val drawLevel = remember(refreshInt.intValue) {
                getDrawLevel()
            }

            // Use remember to store the filtered list
            val show = remember(refreshInt.intValue) {
                pool.filter {
                    !isThisLevelGained(it) && it.level <= drawLevel + 10
                }
            }
            LazyColumn(
                Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(show.size) { index ->
                    Spacer(modifier = Modifier.size(padding10))
                    OneDrawItem(show[index]) {
                        val unlocked = drawLevel >= show[index].level
                        if (unlocked) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                DrawAwardManager.gain(show[index])
                            }
                        } else {
                            GameApp.instance.getWrapString(R.string.draw_level_not_enough).toast()
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding10))
            val enabled = if (drawLevel == 0) false else (0..<drawLevel).any {
                !isThisLevelGained(pool[it])
            }
            GameButton(
                buttonSize = ButtonSize.Big,
                enabled = enabled,
                text = stringResource(R.string.gain_all)
            ) {
                if (enabled) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        DrawAwardManager.gainAll()
                        refreshInt.intValue++
                    }
                } else {
                    GameApp.instance.getWrapString(R.string.no_avaiable_award).toast()
                }
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}

@Composable
fun OneDrawItem(cheat: DrawAward, callback: () -> Unit) {
    Box(
        modifier = Modifier
            .height(padding165)
            .fillMaxWidth()
            .paint(
                painter = painterResource(id = R.drawable.common_frame_long),
                contentScale = ContentScale.FillBounds,
            )
            .padding(horizontal = padding22)
    ) {
        DrawLevel(
            modifier = Modifier
                .align(Alignment.TopStart),
            cheatLevel = "Lv" + cheat.level
        )
        val award = repo.gameCore.getPoolById(cheat.reward).toAward()
        AwardList(
            Modifier
                .align(Alignment.BottomStart)
                .padding(bottom = padding2),
            award = award,
            param = defaultParam.copy(
                peek = true,
                textColor = Color.White,
            ),
        )
        GameButton(
            text = stringResource(id = R.string.gain_award),
            onClick = callback,
            enabled = !isThisLevelGained(cheat) && getDrawLevel() >= cheat.level,
            modifier = Modifier.align(Alignment.CenterEnd)
        )
    }
}

@Composable
fun DrawLevel(
    modifier: Modifier = Modifier,
    cheatLevel: String,
    frame: Painter = painterResource(id = R.drawable.main_frame1)
) {
    Box(modifier = modifier.height(imageLarge), contentAlignment = Alignment.CenterStart) {
        Image(
            modifier = Modifier
                .height(imageLarge)
                .graphicsLayer {
                    rotationY = 180f
                    translationX = -padding14.toPx()
                },
            contentScale = ContentScale.FillHeight,
            painter = frame,
            contentDescription = null
        )
        Text(
            modifier = Modifier.padding(start = padding10),
            text = cheatLevel,
            style = MaterialTheme.typography.h1
        )
    }
}
