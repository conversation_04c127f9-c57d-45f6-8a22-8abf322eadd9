package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getHolidayRanks
import com.moyu.chuanqirensheng.api.getHolidaySolidRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.ui.SingleRecord
import com.moyu.chuanqirensheng.sub.datastore.json
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber

val holidayRanks = mutableStateOf(emptyList<RankData>())
val holidaySolidRanks = mutableStateOf(emptyList<RankData>())


@Composable
fun HolidayRankPage() {
    LaunchedEffect(Unit) {
        try {
            delay(200)
            if (HolidayManager.canShowOnlyRank()) {
                // 活动已经结束，拉取定榜
                getHolidaySolidRanks(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    )
                ).let {
                    holidaySolidRanks.value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                }
            } else {
                getHolidayRanks(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    )
                ).let {
                    holidayRanks.value = json.decodeFromString(
                        ListSerializer(RankData.serializer()), it.message
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    val ranks = if (HolidayManager.canShowOnlyRank()) holidaySolidRanks else holidayRanks
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
            .fillMaxSize(),
        content = {
            items(ranks.value.size) { index->
                SingleRecord(
                    ranks.value[index], index + 1,
                    content = { rankData, _ ->
                        Column {
                            Text(
                                text = stringResource(R.string.holiday_lottery_num) + "：" + (rankData.holidayNum),
                                style = MaterialTheme.typography.h3
                            )
                        }
                    })
            }
        }
    )
}