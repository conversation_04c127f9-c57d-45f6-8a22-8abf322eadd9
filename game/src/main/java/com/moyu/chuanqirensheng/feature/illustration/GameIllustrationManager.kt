package com.moyu.chuanqirensheng.feature.illustration

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_ILLUSTRATE_ALLY
import com.moyu.chuanqirensheng.sub.datastore.KEY_ILLUSTRATE_SKILL1
import com.moyu.chuanqirensheng.sub.datastore.KEY_ILLUSTRATE_SKILL2
import com.moyu.chuanqirensheng.sub.datastore.KEY_ILLUSTRATE_SKILL3
import com.moyu.chuanqirensheng.sub.datastore.KEY_ILLUSTRATE_SKILL4
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.core.model.Ally
import com.moyu.core.model.skill.Skill


object GameIllustrationManager {
    val unlockAllies = mutableStateListOf<Int>()
    val unlockSkill1 = mutableStateListOf<Int>()
    val unlockSkill2 = mutableStateListOf<Int>()
    val unlockSkill3 = mutableStateListOf<Int>()
    val unlockSkill4 = mutableStateListOf<Int>()

    fun init() {
        unlockAllies.clear()
        unlockAllies.addAll(getListObject<Int>(KEY_ILLUSTRATE_ALLY))
        repo.allyManager.data.map { it.mainId }.forEach {
            if (!unlockAllies.contains(it)) {
                unlockAllies.add(it)
            }
        }

        unlockSkill1.clear()
        unlockSkill1.addAll(getListObject<Int>(KEY_ILLUSTRATE_SKILL1))

        unlockSkill2.clear()
        unlockSkill2.addAll(getListObject<Int>(KEY_ILLUSTRATE_SKILL2))

        unlockSkill3.clear()
        unlockSkill3.addAll(getListObject<Int>(KEY_ILLUSTRATE_SKILL3))

        unlockSkill4.clear()
        unlockSkill4.addAll(getListObject<Int>(KEY_ILLUSTRATE_SKILL4))
    }

    fun unlockAlly(ally: Ally) {
//        if (unlockAllies.contains(ally.mainId)) {
//            return
//        } else {
//            unlockAllies.add(ally.mainId)
//            setListObject(KEY_ILLUSTRATE_ALLY, unlockAllies)
//        }
    }

    fun unlockSkill(skill: Skill) {
//        if (skill.isBattleTree()) {
//            if (unlockSkill1.contains(skill.mainId)) {
//                return
//            } else {
//                unlockSkill1.add(skill.mainId)
//                setListObject(KEY_ILLUSTRATE_SKILL1, unlockSkill1)
//            }
//        } else if (skill.isJinNang()) {
//            if (unlockSkill2.contains(skill.mainId)) {
//                return
//            } else {
//                unlockSkill2.add(skill.mainId)
//                setListObject(KEY_ILLUSTRATE_SKILL2, unlockSkill2)
//            }
//        } else if (skill.isTalentSpecial()) {
//            if (unlockSkill3.contains(skill.mainId)) {
//                return
//            } else {
//                unlockSkill3.add(skill.mainId)
//                setListObject(KEY_ILLUSTRATE_SKILL3, unlockSkill3)
//            }
//        } else if (skill.isZhenLing()) {
//            if (unlockSkill4.contains(skill.mainId)) {
//                return
//            } else {
//                unlockSkill4.add(skill.mainId)
//                setListObject(KEY_ILLUSTRATE_SKILL4, unlockSkill4)
//            }
//        }
    }
}