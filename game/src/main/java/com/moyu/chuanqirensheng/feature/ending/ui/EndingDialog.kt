package com.moyu.chuanqirensheng.feature.ending.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.limit.GameLimitManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.share.ui.ShareEndingLayout
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EndingDialog(show: MutableState<Ending?>) {
    show.value?.let { ending ->
        val award = remember {
            ending.toAward().let {
                if (VipManager.isDoubleEndingAward()) {
                    it.copy(
                        key = it.key * 2,
                        diamond = it.diamond * 2,
                        extraKey = it.key,
                        extraDiamond = it.diamond
                    )
                } else it
            }.let { GameLimitManager.getEndingAward(it) }
        }
        LaunchedEffect(Unit) {
            ContinueManager.clearSave()
            repo.gameCore.onBattleEffect(SoundEffect.GameOver)
            MusicManager.stopAll()
        }
        PanelDialog(onDismissRequest = {
            if (show.value != null) {
                show.value = null
                repo.onGameOver(ending)
                GameApp.globalScope.launch(Dispatchers.Main) {
                    AwardManager.gainAward(award)
                }
            }
        }, contentBelow = {
            ShareEndingLayout(ending)
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding19)
                    .padding(top = padding10)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = ending.ending, style = MaterialTheme.typography.h1, color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding16))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = ending.dieReason,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(), text = stringResource(
                        R.string.lasted_stage, ending.age, DifficultManager.getSelectedMap().name + "-" + DifficultManager.getSelected().name
                    ), style = MaterialTheme.typography.h3, color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.last_event) + ending.lastEvent,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.ending_rank, ending.rank.toString()),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.best_title) + ending.title,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.kill_count) + DetailProgressManager.detailProgressData.defeatEnemyRecord.size,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.gain_ally_count) + DetailProgressManager.detailProgressData.getAllyRecord.sumOf { it.number },
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                if (!award.isEmpty()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(R.string.next_turn_award),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(
                        modifier = Modifier.fillMaxWidth(),
                        award = award,
                        mainAxisAlignment = Arrangement.spacedBy(padding8),
                        param = defaultParam.copy(textColor = Color.Black)
                    )
                }
            }
        }
    }
}