package com.moyu.chuanqirensheng.feature.guide.ui

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.draw.ui.AllyCouponItem
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.TALENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.detail.EXAM_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.event.detail.SINGLE_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.logic.event.isNeedGod
import com.moyu.chuanqirensheng.logic.event.isNeedMaster
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.login.MenuButton
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.gapHuge
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.imageHugeLiteFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding280
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun GuideMask() {
    if (GuideManager.showGuide.value) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(if (GuideManager.guideIndex.intValue in listOf(0, 1, 2, 4, 7)) Color.Transparent else B65)
                .clickable {
                    MusicManager.playSound(SoundEffect.Click)
                    when (GuideManager.guideIndex.intValue) {
                        0 -> {
                            repo.clickStart()
                            setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
                        }

                        2 -> {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                EventManager.selectionEvents.firstOrNull {
                                    EventManager.selectEvent(it)
                                }
                                GuideManager.showGuide.value = false
                                GuideManager.guideIndex.intValue = 3
                            }
                            setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
                        }

                        4 -> {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                Dialogs.eventDetailDialog.value = EventManager.selectionEvents.first()
                                GuideManager.showGuide.value = false
                                GuideManager.guideIndex.intValue = 5
                            }
                            setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
                        }

                        6 -> {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                Dialogs.eventDetailDialog.value = EventManager.selectionEvents.first()
                                GuideManager.showGuide.value = false
                                GuideManager.guideIndex.intValue = 5
                            }
                            setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
                        }

                        7 -> {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                if (GuideManager.guideIndex.intValue < BATTLE_GUIDE_START) {
                                    GuideManager.showGuide.value = false
                                    GuideManager.guideIndex.intValue = BATTLE_GUIDE_START
                                }
                            }
                            setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
                        }

                        BATTLE_GUIDE_START -> {
                            val event = EventManager.selectedEvent.value!!
                            Dialogs.selectAllyToBattleDialog.value = SelectAllyData(
                                capacity = 8,
                                filter = { true },
                                needMaster = event.isNeedMaster(),
                                needGod = event.isNeedGod()
                            ) { true }
                            setIntValueByKey(KEY_GUIDE_INDEX, TALENT_GUIDE_START)
                        }

                        TALENT_GUIDE_START -> {
                            gotoSellWithTabIndex(0)
                        }

                        TALENT_GUIDE_START + 1 -> {
                            if ((AwardManager.couponAlly.value + AwardManager.key.value / repo.gameCore.getAllyCouponRate()) < 10) {
                                GameApp.instance.getWrapString(R.string.ally_coupon_not_enough).toast()
                            } else {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    DrawManager.buyAllyCoupon()
                                }
                            }
                            GuideManager.showGuide.value = false
                        }
                    }
                    GuideManager.guideIndex.intValue += 1
                }, horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (GuideManager.guideIndex.intValue) {
                0 -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Spacer(modifier = Modifier.height(padding280))
                        GuidePointAndText(
                            text = stringResource(R.string.guide1),
                            offsetX = (-130).composeDp(),
                            offsetY = -gapMedium,
                            handType = HandType.RIGHT_HAND
                        )
                    }
                }

                1 -> {
                    val show = remember {
                        mutableStateOf(false)
                    }
                    LaunchedEffect(Unit) {
                        delay(1000)
                        show.value = true
                    }
                    if (show.value) {
                        Spacer(
                            Modifier
                                .fillMaxWidth()
                                .height(eventTopLayoutHeight)
                        )
                        GuidePointAndText(
                            text = stringResource(R.string.guide2),
                            offsetX = gapHuge,
                            handType = HandType.LEFT_HAND
                        )
                    }
                }

                2 -> {
                    Spacer(modifier = Modifier.height(eventTopLayoutHeight + padding300))
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = padding16)
                    ) {
                        GuidePointAndText(
                            text = stringResource(R.string.guide3),
                            offsetX = padding300,
                            offsetY = -padding26
                        )
                    }
                }

                4 -> {
                    val show = remember {
                        mutableStateOf(false)
                    }
                    LaunchedEffect(Unit) {
                        delay(1000)
                        show.value = true
                    }
                    if (show.value) {
                        Spacer(modifier = Modifier.height(eventTopLayoutHeight + padding300))
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = padding16)
                        ) {
                            GuidePointAndText(
                                text = stringResource(R.string.guide5),
                                offsetX = padding300,
                                offsetY = -padding26
                            )
                        }
                    }
                }

                7 -> {
                    val show = remember {
                        mutableStateOf(false)
                    }
                    LaunchedEffect(Unit) {
                        delay(1000)
                        show.value = true
                    }
                    if (show.value) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = padding16)
                        ) {
                            Spacer(modifier = Modifier.weight(1f))
                            GuidePointAndText(
                                text = stringResource(R.string.guide8),
                                handType = HandType.RIGHT_HAND,
                                offsetX = padding0,
                            )
                            Spacer(modifier = Modifier.height(padding96))
                        }
                    }
                }

                BATTLE_GUIDE_START -> {
                    if (EventManager.selectedEvent.value?.isBattle() == true) {
                        Spacer(modifier = Modifier.weight(1f))
                        GuidePointAndText(
                            text = GameApp.instance.getWrapString(R.string.guide9),
                            handType = HandType.DOWN_HAND,
                        )
                        Column(
                            Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.SpaceEvenly,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            val event = EventManager.selectedEvent.value!!
                            val capacity = if (event.play in listOf(
                                    SINGLE_BATTLE_PLAY, EXAM_BATTLE_PLAY
                                )
                            ) 1 else 8
                            AllyCardsRow(modifier = Modifier.fillMaxWidth(),
                                allies = BattleManager.getBattleAllies(),
                                capacity = capacity,
                                showName = true,
                                showHp = true,
                                allyClick = {
                                    BattleManager.selectAllyToBattle(it, -1)
                                }) {
                                Dialogs.selectAllyToBattleDialog.value = SelectAllyData(
                                    capacity = capacity,
                                    filter = { true },
                                    needMaster = event.isNeedMaster(),
                                    needGod = event.isNeedGod()
                                ) {
                                    if (BattleManager.getBattleAllies().values.none { !it.isDead() }) {
                                        Dialogs.alertDialog.value =
                                            CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                                onConfirm = {
                                                    EventManager.doEventResult(
                                                        event = event, result = false
                                                    )
                                                })
                                    } else if (event.isNeedMaster() && !BattleManager.getGameMaster()
                                            .isDead() && BattleManager.getBattleAllies().values.none { it.isMaster() }
                                    ) {
                                        GameApp.instance.getWrapString(
                                            R.string.master_need_tips,
                                        ).toast()
                                    } else if (capacity == 1 && BattleManager.allyGameData.none { it.battlePosition == ALLY_ROW1_SECOND }) {
                                        GameApp.instance.getWrapString(R.string.single_ally_need_tips)
                                            .toast()
                                    } else {
                                        val enemies = repo.battleRoles.values.mapNotNull { it }
                                            .filter { !it.isPlayerSide() }
                                        if (enemies.isNotEmpty() && !enemies.all { it.isOver() }) {
                                            val battleAllies =
                                                BattleManager.getBattleRoles(capacity)
                                            repo.setCurrentAllies(battleAllies)
                                            repo.startBattle()
                                        }
                                    }
                                    true
                                }
                            }
                        }
                        Spacer(modifier = Modifier.size(padding120))
                    }
                }

                TALENT_GUIDE_START -> {
                    Spacer(modifier = Modifier.weight(1f))
                    GuidePointAndText(
                        text = GameApp.instance.getWrapString(R.string.guide12),
                        handType = HandType.NO_HAND,
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(modifier = Modifier.size(bottomItemSize))
                        Spacer(modifier = Modifier.size(bottomItemSize))
                        Spacer(modifier = Modifier.size(bottomItemSize))
                        MenuButton(
                            modifier = Modifier.size(bottomItemSize),
                            icon = R.drawable.icon_shop,
                            text = stringResource(R.string.menu4),
                            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_SELL),
                        ) {
                            gotoSellWithTabIndex(0)
                            GuideManager.guideIndex.intValue += 1
                        }
                        Spacer(modifier = Modifier.size(bottomItemSize))
                    }
                    Spacer(modifier = Modifier.size(padding10))
                }

                TALENT_GUIDE_START + 1 -> {
                    Spacer(modifier = Modifier.size(padding260))
                    AllyCouponItem()
                    Spacer(modifier = Modifier.size(padding60))
                    GuidePointAndText(
                        text = if (DrawManager.canDrawQuality7()) GameApp.instance.getWrapString(R.string.guide13_1) else GameApp.instance.getWrapString(R.string.guide13),
                        handType = HandType.UP_HAND,
                    )
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
        }
    }
}

enum class HandType(val value: Int) {
    UP_HAND(1), DOWN_HAND(2), LEFT_HAND(3), RIGHT_HAND(4), NO_HAND(5)
}

@Composable
fun GuidePointAndText(
    text: String, handType: HandType = HandType.UP_HAND, offsetX: Dp? = null, offsetY: Dp? = null
) {
    // 创建一个动画状态，用于处理跳动效果
    val animatedOffsetY = remember { Animatable(0f) }

    // 当组件进入或手指类型改变时，触发跳动动画
    LaunchedEffect(Unit) {
        animatedOffsetY.animateTo(
            targetValue = -20f, // 设置动画的目标值
            animationSpec = repeatable( // 使用repeatable重复动画
                iterations = 999, // 设置动画无限重复
                animation = tween(
                    durationMillis = 500, // 设置动画持续时间
                    easing = FastOutSlowInEasing // 设置动画的缓动效果
                ),
                repeatMode = RepeatMode.Reverse // 设置动画反向重复
            )
        )
    }
    when (handType) {
        HandType.UP_HAND -> {
            Image(
                modifier = Modifier
                    .height(imageLargePlus)
                    .graphicsLayer {
                        translationY = (offsetY?.toPx() ?: -gapMedium.toPx()) + animatedOffsetY.value
                        translationX = offsetX?.toPx() ?: gapLarge.toPx()
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_3),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.size(padding16))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = (offsetY?.toPx()
                        ?: -gapMedium.toPx()) + (-padding19.toPx()) //paddingHugeLite.toPx() 文字背景上移
                }
                .paint(
                    painterResource(R.drawable.dialog_frame), contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding16, horizontal = padding28),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }

        HandType.LEFT_HAND -> {
            Image(
                modifier = Modifier
                    .height(imageLargePlus)
                    .graphicsLayer {
                        translationY = offsetY?.toPx() ?: -gapMedium.toPx()
                        translationX = (offsetX?.toPx() ?: gapLarge.toPx()) + animatedOffsetY.value
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_4),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.size(padding16))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = (offsetY?.toPx()
                        ?: -gapMedium.toPx()) + (-padding19.toPx()) //paddingHugeLite.toPx() 文字背景上移
                }
                .paint(
                    painterResource(R.drawable.dialog_frame), contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding16, horizontal = padding28),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }

        HandType.RIGHT_HAND -> {
            Image(
                modifier = Modifier
                    .height(imageLargePlus)
                    .graphicsLayer {
                        translationY = offsetY?.toPx() ?: -padding150.toPx()
                        translationX = (offsetX?.toPx() ?: gapLarge.toPx()) + animatedOffsetY.value
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_1),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.size(padding16))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = (offsetY?.toPx()
                        ?: -gapMedium.toPx()) + (-padding19.toPx()) //paddingHugeLite.toPx() 文字背景上移
                }
                .paint(
                    painterResource(R.drawable.dialog_frame), contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding16, horizontal = padding28),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }

        HandType.DOWN_HAND -> {
            Text(
                modifier = Modifier
                    .paint(
                        painterResource(R.drawable.dialog_frame),
                        contentScale = ContentScale.FillBounds
                    )
                    .padding(vertical = padding16, horizontal = padding28),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black
            )
            Image(
                modifier = Modifier
                    .height(imageHugeLiteFrame)
                    .graphicsLayer {
                        translationY = animatedOffsetY.value
                        translationX = offsetX?.toPx() ?: padding16.toPx()
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_2),
                contentDescription = ""
            )
        }

        HandType.NO_HAND -> {
            Spacer(modifier = Modifier.size(padding80 + padding16))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = offsetY?.toPx() ?: -gapLarge.toPx()
                }
                .paint(
                    painterResource(R.drawable.dialog_frame), contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding16, horizontal = padding28),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }
    }
}