package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.lottery.MAX_RESET_LOTTERY_NUM
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.button.MEDIA_GAP
import com.moyu.chuanqirensheng.screen.button.RefreshButton
import com.moyu.chuanqirensheng.screen.resource.LotteryPoint
import com.moyu.chuanqirensheng.sub.datastore.KEY_RESET_EXPENSIVE_LOTTERY_NUM
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun ExpensiveLotteryLayout() {
    Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Column(Modifier.align(Alignment.End).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally) {
            RefreshButton(text = stringResource(id = R.string.reset_lottery)) {
                if (LotteryManager.canResetExpensive()) {
                    LotteryManager.resetExpensiveLottery()
                } else {
                    GameApp.instance.getWrapString(R.string.reset_lottery_tips, MAX_RESET_LOTTERY_NUM).toast()
                }
            }
            Text(
                text = "" + getIntFlowByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM) + "/" + MAX_RESET_LOTTERY_NUM,
                style = MaterialTheme.typography.h4
            )
            Text(
                text = stringResource(id = R.string.reset_lottery),
                style = MaterialTheme.typography.h4
            )
        }
        val spinning = remember {
            mutableStateOf(false)
        }
        val singleItemAngle = 360 / 8
        val targetAngle = remember {
            mutableFloatStateOf(0f)
        }
        val currentAngle by animateFloatAsState(
            targetValue = if (spinning.value) targetAngle.floatValue else targetAngle.floatValue % 360,
            animationSpec = TweenSpec(
                durationMillis = if (spinning.value) 2000 else 0,
                easing = FastOutSlowInEasing
            ),
            finishedListener = {
                spinning.value = false
            }, label = ""
        )
        Box(Modifier.align(Alignment.CenterHorizontally), contentAlignment = Alignment.Center) {
            LotteryView(
                Modifier
                    .size(padding380).graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = 360 / 8f
                    }, LotteryManager.getExpensiveAwards(),
                LotteryManager.getExpensiveTurnTables(),
                LotteryManager.expensiveBoughtIndex
            )
            Image(
                modifier = Modifier.size(padding380)
                    .graphicsLayer {
                        // 因为要保证最后一个奖品显示在上面中间，所以要转动显示，保持逻辑不变
                        rotationZ = currentAngle + 360 / 8f
                        scaleY = lotteryScale
                        scaleX = lotteryScale
                    },
                painter = painterResource(id = R.drawable.lottery_top),
                contentDescription = null
            )
        }
        LotteryPoint(cost = if (LotteryManager.isExpensiveFreeLottery()) 0 else LotteryManager.getExpensiveCost())
        Spacer(modifier = Modifier.size(padding6))
        GameButton(clickGap = MEDIA_GAP,
            text = stringResource(R.string.do_lottery),
            enabled = (LotteryManager.isExpensiveFreeLottery() || AwardManager.lotteryMoney.value >= LotteryManager.getExpensiveCost()) && LotteryManager.canDoExpensive(),
            onClick = {
                if (LotteryManager.spinning.value) {
                    // 正在转动
                } else if (!LotteryManager.isExpensiveFreeLottery() && AwardManager.lotteryMoney.value < LotteryManager.getExpensiveCost()) {
                    GameApp.instance.getWrapString(R.string.lottery_money_not_enough).toast()
                } else if (!LotteryManager.canDoExpensive()) {
                    GameApp.instance.getWrapString(R.string.lottery_run_out).toast()
                } else {
                    LotteryManager.spinning.value = true
                    MusicManager.playSound(SoundEffect.LotteryRing)
                    if (!LotteryManager.isExpensiveFreeLottery()) {
                        AwardManager.lotteryMoney.value -= LotteryManager.getExpensiveCost()
                    }
                    val (award, index) = LotteryManager.lotteryExpensiveRandomAward()
                    targetAngle.floatValue = index * singleItemAngle.toFloat() + 360 * 10
                    spinning.value = true
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        delay(2100)
                        LotteryManager.gainExpensiveLotteryAward(index)
                        AwardManager.gainAward(award)
                        Dialogs.awardDialog.value = award
                        LotteryManager.spinning.value = false
                    }
                }
            })
    }
}