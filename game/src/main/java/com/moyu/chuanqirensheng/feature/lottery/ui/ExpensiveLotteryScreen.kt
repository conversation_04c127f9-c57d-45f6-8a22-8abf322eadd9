package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab

@Composable
fun ExpensiveLotteryScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.lottery_tab1),
            GameApp.instance.getWrapString(R.string.lottery_tab2),
        )
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }

    GameBackground(title = stringResource(id = R.string.lottery_title2)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pageState = pagerState,
                titles = listTabItems
            )
            HorizontalPager(
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> ExpensiveLotteryPage()
                    else -> ExpensiveGiftPage()
                }
            }
        }
    }
}