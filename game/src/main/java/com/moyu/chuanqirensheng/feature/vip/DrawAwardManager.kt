package com.moyu.chuanqirensheng.feature.vip

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkDrawAward
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_GAINED
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.DrawAward
import com.moyu.core.model.toAward
import kotlinx.coroutines.delay
import kotlin.math.min


object DrawAwardManager {
    const val perDraw = 100
    val saving = mutableStateOf(false)
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getDrawAwardPool().forEach {
            gainedMap[it.id] = GuardedB(KEY_DRAW_GAINED + it.id)
        }
    }

    // todo 简单处理了，直接50的倍数
    fun getDrawLevel(): Int {
        return min(AwardManager.couponHistory.value / perDraw, repo.gameCore.getDrawAwardPool().size)
    }

    fun isThisLevelGained(cheat: DrawAward): Boolean {
        return gainedMap[cheat.id]!!.value
    }

    suspend fun gain(vip: DrawAward, gain: Boolean = true, check: Boolean = true): Award? {
        val gained = gainedMap[vip.id]!!
        if (gained.value) {
            AppWrapper.getString(R.string.already_got).toast()
            return null
        }
        if (check) {
            checkDrawAward(vip)
        }
        gained.value = true
        val award = repo.gameCore.getPoolById(vip.reward).toAward()
        if (gain) {
            Dialogs.awardDialog.value = award
            AwardManager.gainAward(award)
        }
        return award
    }

    suspend fun gainAll() {
        val upgradeInfo = mutableListOf<String>()
        Dialogs.commonBlockDialog.value = GameApp.instance.getWrapString(R.string.gain_all_tips)
        val pool = repo.gameCore.getDrawAwardPool()
        var award = Award()
        var checked = false
        (0..<getDrawLevel()).forEach {
            if (!isThisLevelGained(pool[it])) {
                if (!checked) checkDrawAward(pool[it])
                checked = true
                gain(pool[it], gain = false, check = false)?.let { currentAward ->
                    award += currentAward
                    award = award.merge()
                    upgradeInfo.add(
                        0, GameApp.instance.getWrapString(R.string.gain_draw_tips, pool[it].level)
                    )
                    Dialogs.commonBlockDialog.value =
                        GameApp.instance.getWrapString(R.string.gain_all_tips) + "\n" + upgradeInfo.joinToString(
                            "\n"
                        )
                    delay(1)
                }
            }
        }
        AwardManager.gainAward(award)
        Dialogs.awardDialog.value = award
        delay(500)
        Dialogs.commonBlockDialog.value = null
    }
}