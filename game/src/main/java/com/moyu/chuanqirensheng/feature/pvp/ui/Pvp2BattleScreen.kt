package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager.filter
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally

@Composable
fun Pvp2BattleScreen() {
    LaunchedEffect(Unit) {
        // 将所有角色加入到局内
        BattleManager.selectAllToGame()
        Pvp2Manager.lastPvpAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                BattleManager.selectAllyToBattle(
                    it, index + ALLY_ROW1_FIRST
                )
            }
        }
    }
    GameBackground(
        title = stringResource(R.string.pvp2), bgMask = B65, background = R.drawable.bg_1
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (repo.inBattle.value) {
                Box(modifier = Modifier.graphicsLayer {
                    // todo 简单处理pvp战斗场景的位置
                    translationY = -padding22.toPx()
                }) {
                    BattleFieldLayout(repo.battleRoles)
                }
            } else {
                val filter = { it: Ally -> Pvp2Manager.getCurrentArena().filter(it) }
                PvpPrepareBattleLayout(allyFilter = filter) {
                    if (BattleManager.getBattleAllies().values.isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    if (repo.gameMode.value.isPvp2Mode()) {
                                        Pvp2Manager.pkFailed(emptyList(),
                                            repo.battleRoles.values.mapNotNull { it })
                                    } else {
                                        Pvp2Manager.pkFailed(emptyList(),
                                            repo.battleRoles.values.mapNotNull { it })
                                    }
                                })
                        true
                    } else if (BattleManager.getBattleAllies().values.none { it.isMaster() }) {
                        GameApp.instance.getWrapString(
                            R.string.master_need_tips,
                        ).toast()
                        false
                    } else if (BattleManager.getBattleAllies().values.count { it.isMaster() } > 1) {
                        GameApp.instance.getWrapString(
                            R.string.only_one_master_tips,
                        ).toast()
                        false
                    } else if (positionListEnemy.none { repo.battleRoles[it]?.isOver() == false }) {
                        GameApp.instance.getWrapString(R.string.pvp_ally_error).toast()
                        false
                    } else if (BattleManager.getBattleAllies().values.any {
                            !it.isHero() && !filter(it)
                        }) {
                        (GameApp.instance.getWrapString(R.string.pvp2_rule_desc) + Pvp2Manager.getCurrentArena().desc).toast()
                        false
                    } else {
                        val battleAllies = BattleManager.getPvpBattleRoles()
                        Pvp2Manager.lastPvpAllyIds.clear()
                        Pvp2Manager.lastPvpAllyIds.addAll(battleAllies.toSortedMap().values.map { it.getAlly().id })
                        setListObject(KEY_PK_ALLY_IDS, Pvp2Manager.lastPvpAllyIds)
                        repo.setCurrentAllies(battleAllies)
                        if (repo.isCurrentEnemyEmptyOrDead()) {
                            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
                        } else {
                            repo.startBattle()
                        }
                        true
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6)
                .graphicsLayer {
                    translationY = padding16.toPx()
                }, settingBattleItems
        )
    }
}