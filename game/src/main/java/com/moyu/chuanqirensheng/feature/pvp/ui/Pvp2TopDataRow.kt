package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.screen.resource.CurrentPvp2Score
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16

@Composable
fun Pvp2TopDataRow(modifier: Modifier) {
    Row(modifier = modifier) {
        CurrentPvp2Score(
            modifier = Modifier
                .padding(start = padding14),
            showPlus = true,
            showFrame = true
        )
        Spacer(modifier = Modifier.size(padding16))
        Column(Modifier.height(moneyHeight), verticalArrangement = Arrangement.SpaceEvenly) {
            Text(
                text = stringResource(
                    R.string.today_pk,
                    Pvp2Manager.pkWinToday.value,
                    Pvp2Manager.pkLoseToday.value
                ),
                style = MaterialTheme.typography.h5
            )
            Text(
                text = stringResource(
                    R.string.all_pk,
                    Pvp2Manager.pkWin.value,
                    Pvp2Manager.pkLose.value
                ),
                style = MaterialTheme.typography.h5
            )
        }
    }
}