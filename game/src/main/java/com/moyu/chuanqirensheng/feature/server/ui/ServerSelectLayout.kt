package com.moyu.chuanqirensheng.feature.server.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.filterHeight
import com.moyu.chuanqirensheng.ui.theme.filterWidth
import com.moyu.chuanqirensheng.ui.theme.padding2


@Composable
fun ServerSelectorLayout(
    modifier: Modifier,
    show: MutableState<Boolean>,
    callback: ((Int) -> Unit)? = null
) {
    if (show.value) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable {
                    show.value = false
                }
                .background(B50))
    }
    AnimatedVisibility(
        modifier = modifier, visible = show.value
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            GameApp.instance.loginData.value.serverList.forEach { server ->
                val selected = server.serverId == ServerManager.getSavedServerId()
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    callback?.invoke(server.serverId)
                }) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = padding2)
                    ) {
                        val res = if (selected) R.drawable.common_button2
                        else R.drawable.common_button_grey
                        Image(
                            modifier = Modifier.size(filterWidth, filterHeight),
                            painter = painterResource(res),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        StrokedText(
                            text = stringResource(R.string.server) + (server.serverId + 1),
                            style = MaterialTheme.typography.h3,
                        )
                    }
                }
            }
        }
    }
}