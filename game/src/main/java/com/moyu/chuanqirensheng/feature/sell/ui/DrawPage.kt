package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.draw.DrawManager.canDrawQuality7
import com.moyu.chuanqirensheng.feature.draw.ui.AllyCouponItem
import com.moyu.chuanqirensheng.feature.draw.ui.HeroCouponItem
import com.moyu.chuanqirensheng.feature.draw.ui.HistoryCouponItem
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.resource.CurrentAllyCouponPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentHeroCouponPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.resource.InfoIcon
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding8


@Composable
fun DrawPage() {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    Column {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier
                .padding(start = padding14)
        ) {
            CurrentKeyPoint(
                showPlus = true,
                showFrame = true
            )
            CurrentDiamondPoint(
                showPlus = false,
                showFrame = true
            )
            CurrentAllyCouponPoint(showFrame = true)
            CurrentHeroCouponPoint(showFrame = true)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            GameLabel {
                Text(
                    text = stringResource(id = R.string.ally_coupon),
                    style = MaterialTheme.typography.h2,
                )
                Box(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = padding22),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    InfoIcon {
                        Dialogs.alertDialog.value = CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.ally_coupon),
                            content = GameApp.instance.getWrapString(if (!canDrawQuality7()) R.string.draw_ally_tips else R.string.draw_ally_tips2),
                            onlyConfirm = true
                        )
                    }
                }
            }
            AllyCouponItem()
            Spacer(modifier = Modifier.size(padding12))
            GameLabel {
                Text(
                    text = stringResource(id = R.string.hero_coupon),
                    style = MaterialTheme.typography.h2,
                )
                Box(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = padding22),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    InfoIcon {
                        Dialogs.alertDialog.value = CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.hero_coupon),
                            content = GameApp.instance.getWrapString(if (!canDrawQuality7()) R.string.draw_hero_tips else R.string.draw_hero_tips2),
                            onlyConfirm = true
                        )
                    }
                }
            }
            HeroCouponItem()
            Spacer(modifier = Modifier.size(padding12))
            if (!canDrawQuality7()) {
                GameLabel {
                    Text(
                        text = stringResource(id = R.string.history_coupon),
                        style = MaterialTheme.typography.h2,
                    )
                }
                HistoryCouponItem()
                Spacer(modifier = Modifier.size(padding12))
            }
        }
    }
}