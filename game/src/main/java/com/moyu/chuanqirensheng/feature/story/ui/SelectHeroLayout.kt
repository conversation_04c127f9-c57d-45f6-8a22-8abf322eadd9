package com.moyu.chuanqirensheng.feature.story.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleHeroCard
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.EmptyIconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.createCountryLabelHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26


@Composable
fun SelectHeroLayout() {
    val allies = repo.allyManager.data.filter { it.isHero() }
    Box {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(createCountryLabelHeight)
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = padding19)
                .horizontalScroll(rememberScrollState()),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.size(padding22))
            allies.firstOrNull { it.selected }?.let {
                Box(contentAlignment = Alignment.Center) {
                    SingleHeroCard(
                        modifier = Modifier.padding(top = padding26)
                            .height(eventCardHeight),
                        ally = it,
                    )
                    EffectButton(
                        modifier = Modifier
                            .align(Alignment.TopEnd).padding(top = padding26)
                            .size(imageSmallPlus),
                        onClick = {
                            repo.allyManager.selectToGame(it)
                        }) {
                        Image(
                            modifier = Modifier
                                .height(backIconHeight)
                                .scale(1.2f),
                            contentScale = ContentScale.FillHeight,
                            painter = painterResource(id = R.drawable.common_exit),
                            contentDescription = stringResource(R.string.quit_page)
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
            } ?:  EmptyIconView(itemSize = ItemSize.Huge, frame = R.drawable.ally_frame) {
                  Dialogs.selectAllyToGameDialog.value = true
            }
        }
        TextLabel2(modifier = Modifier.graphicsLayer {
            translationY = -padding16.toPx()
        }, text = stringResource(R.string.select_story_title), labelSize = LabelSize.Medium)
    }
}