package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager.filter
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.Ally

@Composable
fun WorldBossBattleScreen() {
    val clickedStartBattle = remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        // 将所有角色加入到局内
        BattleManager.selectAllToGame()
    }
    
    GameBackground(
        title = stringResource(R.string.world_boss), bgMask = B65, background = R.drawable.bg_1
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (repo.inBattle.value) {
                Box(modifier = Modifier.graphicsLayer {
                    // 简单处理世界Boss战斗场景的位置
                    translationY = -padding22.toPx()
                }) {
                    BattleFieldLayout(repo.battleRoles)
                }
            } else {
                val todayWorldBoss = WorldBossManager.getTodayWorldBoss()
                val forbiddenAllyIds = WorldBossManager.getForbiddenAllyIds()
                
                val filter = { ally: Ally -> 
                    // 检查是否在禁止上阵清单中
                    if (forbiddenAllyIds.contains(ally.id)) {
                        false
                    } else {
                        // 检查种族限制
                        todayWorldBoss?.filter(ally) ?: true
                    }
                }

                WorldBossPrepareBattleLayout(allyFilter = filter) {
                    if (clickedStartBattle.value) {
                        // 防止快速点击导致的异常
                        false
                    } else if (BattleManager.getBattleAllies().values.isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    // 世界Boss战斗失败不扣除挑战次数
                                })
                        true
                    } else if (BattleManager.getBattleAllies().values.none { it.isMaster() }) {
                        GameApp.instance.getWrapString(
                            R.string.master_need_tips,
                        ).toast()
                        false
                    } else if (positionListEnemy.none { repo.battleRoles[it]?.isOver() == false }) {
                        GameApp.instance.getWrapString(R.string.world_boss_not_found).toast()
                        false
                    } else if (BattleManager.getBattleAllies().values.any {
                            !filter(it)
                        }) {
                        val todayWorldBoss = WorldBossManager.getTodayWorldBoss()
                        val desc = if (todayWorldBoss != null) {
                            GameApp.instance.getWrapString(R.string.world_boss_formation_limit) + 
                            ": " + todayWorldBoss.desc
                        } else {
                            GameApp.instance.getWrapString(R.string.world_boss_formation_limit)
                        }
                        desc.toast()
                        false
                    } else {
                        val battleAllies = BattleManager.getWorldBossBattleRoles()
                        repo.setCurrentAllies(battleAllies)
                        if (repo.isCurrentEnemyEmptyOrDead()) {
                            GameApp.instance.getWrapString(R.string.world_boss_not_found).toast()
                        } else {
                            clickedStartBattle.value = true
                            repo.startBattle()
                        }
                        true
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6)
                .graphicsLayer {
                    translationY = padding16.toPx()
                }, settingBattleItems
        )
    }
}
