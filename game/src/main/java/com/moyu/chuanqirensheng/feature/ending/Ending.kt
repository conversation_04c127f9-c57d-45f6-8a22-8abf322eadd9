package com.moyu.chuanqirensheng.feature.ending

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo
import kotlinx.serialization.Serializable

@Serializable
data class Ending(
    val uuid: String,
    val storyId: Int = 1,
    val pic: String = "",
    val dieReason: String = "",
    val ending: String = "",
    val lastEvent: String = "",
    val shareTitle: String = "",
    val age: Int = 0,
    val kill: Int = 0,
    val title: String = "",
    val endingText: String,
    val rank: Double = 0.0,
    val difficult: Int = 1,
) {
    fun getDisplayEnding(): String {
        return if (ending.isEmpty() || ending == "0") GameApp.instance.getWrapString(R.string.none_ending) else ending
    }

    fun getDifficult(): String {
        return repo.gameCore.getDifficultPool().firstOrNull { it.id == difficult }?.name ?: ""
    }
}
