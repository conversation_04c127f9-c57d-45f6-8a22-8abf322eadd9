package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.pvpRecordFrameHeight
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun SingleQuest(quest: Quest, refresh: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(pvpRecordFrameHeight)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_long),
            contentDescription = null
        )
        Column(modifier = Modifier.padding(horizontal = padding16, vertical = padding10)) {
            val postFix = QuestManager.getTaskProgressFlow(task = quest).let {
                if (it.isNotEmpty()) {
                    "（$it）"
                } else {
                    ""
                }
            }
            Text(
                text = quest.desc + postFix,
                style = if (quest.desc.length >= 12) MaterialTheme.typography.h3 else MaterialTheme.typography.h2
            )
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Spacer(Modifier.size(padding10))
                val award = quest.toAward()
                AwardList(
                    award = award,
                    param = defaultParam.copy(
                        numInFrame = true,
                        showName = false,
                        itemSize = ItemSize.Medium
                    ),
                    paddingVerticalInDp = padding0,
                    paddingHorizontalInDp = padding0
                )
                Spacer(modifier = Modifier.weight(1f))
                val questCompleted =
                    QuestManager.getTaskDoneFlow(quest)
                GameButton(text =
                if (quest.opened) stringResource(R.string.already_got)
                else if (VipManager.isDoubleQuestAward() && quest.isDailyTask()) stringResource(
                    R.string.double_gain
                ) else stringResource(R.string.gain_award),
                    enabled = questCompleted && !quest.opened,
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            if (!questCompleted) {
                                GameApp.instance.getWrapString(R.string.quest_not_done_toast).toast()
                            } else if (quest.opened) {
                                GameApp.instance.getWrapString(R.string.award_got_toast).toast()
                            } else {
                                QuestManager.questReward(quest, award)
                                refresh()
                            }
                        }
                    })
            }
        }
    }
}
