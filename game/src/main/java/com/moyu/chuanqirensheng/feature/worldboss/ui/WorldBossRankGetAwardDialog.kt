package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.SingleQuest
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.LAST_WORLD_BOSS_TYPE
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json
import timber.log.Timber
import com.moyu.core.model.Quest

@Composable
fun WorldBossRankGetAwardDialog(show: MutableState<Boolean>) {
    if (show.value) {
        PanelDialog(
            onDismissRequest = { show.value = false }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding16),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.world_boss_rank_get_awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(padding16))

                val tasks = remember {
                    mutableStateListOf<Quest>()
                }
                val refresh = remember {
                    mutableIntStateOf(0)
                }

                LaunchedEffect(refresh.intValue) {
                    WorldBossManager.init()
                    QuestManager.init()

                    // Ensure cached rank data is available
                    try {
                        if (lastWorldBossRanks.value.isEmpty()) {
                            getRanks(GameApp.instance.resources.getString(R.string.platform_channel), LAST_WORLD_BOSS_TYPE).let {
                                lastWorldBossRanks.value = Json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to load world boss ranks for task checking")
                    }

                    // 完成的任务排前面，已领取的排最后
                    QuestManager.worldBossTasks.map {
                        it.copy(done = QuestManager.getTaskDoneFlow(it))
                    }.sortedBy { it.order }
                        .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
                        .apply {
                            tasks.clear()
                            tasks.addAll(this)
                        }
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    if (tasks.isEmpty()) {
                        Text(
                            text = stringResource(R.string.world_boss_no_tasks),
                            style = MaterialTheme.typography.h3,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(padding16)
                        )
                    } else {
                        tasks.forEach { task ->
                            SingleQuest(task) {
                                refresh.intValue += 1
                            }
                            Spacer(modifier = Modifier.height(padding10))
                        }
                    }
                }
            }
        }
    }
}
