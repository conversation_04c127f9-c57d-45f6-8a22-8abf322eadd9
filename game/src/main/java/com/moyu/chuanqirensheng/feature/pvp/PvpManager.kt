package com.moyu.chuanqirensheng.feature.pvp

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.postPvpRankData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_PVP
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.PvpData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.VIP_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.createPvpRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_TARGET
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_SCORE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN_TODAY
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.model.Award
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import timber.log.Timber

// todo pvp战斗中能生效的天赋列举
val pvpTalentMainIds = listOf(
    8001,
    8002,
    8003,
    8004,
    8005,
    8006,
    8007,
    8008,
    8009,
    8010,
    8011,
    8012,
    8013,
    8014,
    8015,
    8016,
    8017,
    8018,
    8019,
    8020,
    8021,
    8022,
    8023,
    8024,
    8025,
    8026,
    8027,
    8028,
    8029,
    8030,
    8031,
    8032,
    8033,
    8034,
    8035,
    8036,
    8037,
    8038,
    8039,
    8040,
    8041,
    8042,
    8043,
    8044,
    8045,
    8046,
    8047,
    8048,
    8049,
    8050,
    8051,
    8052,
    8053,
    8054,
    8055,
    8056,
    8057,
    8058,
    8059,
    8060,
    8061,
    8062,
    8063,
    8064,
    8065,
    8066,
    8067,
    8068,
    8069,
    8070,
    8071,
    8072,
    8073,
    8074,
    8075,
    8076,
    8077,
    8078,
    8079,
    8080,
    8081,
    8082,
    8083,
    8084,
    8085,
    8086,
    8087,
    8088,
    8089,
    8090,
    8091,
    8092,
    8093,
    8094,
    8095,
    8096,
    8097,
    8098,
    8099,
    8125,
    8126,
    8127,
    8128,
    8129,
    8130,
    8131,
    8132,
    8133,
    8134,
    8135,
    8136,
    8137,
    8138,
    8139,
    8140,
    8141,
    8142,
    8143,
    8144,
    8145,
    8146,
    8147,
    8148,
    8149,
    8150,
    8151,
    8152,
    8153,
    8154,
    8155,
    8156,
    8157,
    8158,
    8159,
    8160,
    8161,
    8162,
    8163,
    8164,
    8165,
    8166,
    8167,
    8168
)
val pvpMasterIds = listOf(8114, 8115, 8116, 8117, 8118, 8119, 8120, 8121, 8122, 8123, 8124)

const val MAX_PVP_NUM = 10
const val MAX_ALL_PVP_NUM = 20
const val INIT_PVP_SCORE = 1000

object PvpManager {
    val pvpScore = Guarded(KEY_PVP_SCORE, mutableStateOf(INIT_PVP_SCORE))
    val lastPvpAllyIds = mutableListOf<Int>()
    val pkNumToday = Guarded(KEY_PK_NUM)
    val pkWin = Guarded(KEY_PVP_WIN)
    val pkLose = Guarded(KEY_PVP_LOSE)
    val pkWinToday = Guarded(KEY_PVP_WIN_TODAY)
    val pkLoseToday = Guarded(KEY_PVP_LOSE_TODAY)
    val pkTargetList = mutableStateListOf<String>()
    val currentTarget = mutableStateOf(RankData(System.currentTimeMillis()))
    val targetsFromServer = mutableStateOf(emptyList<RankData>())
    val currentTargets = mutableStateOf(emptyList<RankData>())

    suspend fun init() {
        initPkNum()
        lastPvpAllyIds.clear()
        lastPvpAllyIds.addAll(getListObject(KEY_PK_ALLY_IDS))
    }

    fun initPkNum() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_PK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            if (pkTargetList.isEmpty()) {
                pkTargetList.addAll(getListObject(KEY_PK_TARGET))
            }
        } else {
            pvpScore.value = INIT_PVP_SCORE
            pkNumToday.value = 0
            pkWinToday.value = 0
            pkLoseToday.value = 0
            pkTargetList.clear()
            setLongValueByKey(KEY_PK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_PK_TARGET, emptyList<String>())
        }
    }

    fun pk(rankData: RankData) {
        if (!DebugManager.unlockAll) {
            if (pkWinToday.value + pkLoseToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
                || pkNumToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
            ) {
                if (MAX_PVP_NUM + VipManager.getExtraPvpNum() < MAX_ALL_PVP_NUM) {
                    Dialogs.alertDialog.value = CommonAlert(
                        content = GameApp.instance.getWrapString(R.string.pvp_num_limit2),
                        onConfirm = {
                            goto(VIP_SCREEN)
                        }
                    )
                } else {
                    GameApp.instance.getWrapString(R.string.pvp_num_limit).toast()
                }

                return
            }
        }
        if (rankData.pvpData.allyIds.isEmpty()) {
            GameApp.instance.getWrapString(R.string.pvp_ally_error).toast()
            return
        }
        val allies = rankData.pvpData.allyIds.take(8)
        allies.forEach { ally ->
            if (repo.gameCore.getAllyPool().firstOrNull { it.id == ally } == null) {
                GameApp.instance.getWrapString(R.string.pvp_upgrade).toast()
                return
            }
        }
        if (currentTarget.value.userId.isNotEmpty()) {
            // 已经选择了对手，正在pk中
            return
        }
        currentTarget.value = rankData
        repo.gameMode.value = GameMode(MODE_PVP)
        increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.PVP_BATTLE.id)
        pkNumToday.value += 1
        val roleHashMap = mutableMapOf<Int, Role?>()
        val heroAdvSkills = allies.firstOrNull { repo.gameCore.getAllyById(it).isHero() }?.let {
            repo.gameCore.getAllyById(it).getRace().skillId
        }
        repeat(allies.size) {
            roleHashMap[ENEMY_ROW1_FIRST + it] =
                createPvpRole(
                    repo.gameCore.getAllyById(allies[it]).getRace(),
                    rankData.pvpData.talentIds,
                    heroAdvSkills ?: emptyList()
                )
        }
        // 竞技场地形
//        val haloSkill = repo.gameCore.getSkillById(PVP_HALO_SKILL_ID)
//        roleHashMap.values.firstNotNullOf { it }.let {
//            it.learnSkill(haloSkill, it)
//        }

        repo.battleRoles.clear()
        repo.battleRoles.putAll(roleHashMap)
        goto(PVP_BATTLE_SCREEN)
    }

    fun pkFailed(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkLose.value += 1
        pkLoseToday.value += 1
        val index = currentTargets.value.sortedByDescending { it.pvpScore }
            .indexOfFirst { it.userId == currentTarget.value.userId }
        val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool(MODE_PVP).size) {
            repo.gameCore.getPvpPool(MODE_PVP).last()
        } else {
            repo.gameCore.getPvpPool(MODE_PVP)[index]
        }
        if (lessThanMax()) {
            GameApp.globalScope.launch {
                Dialogs.awardDialog.value =
                    Award(pvpDiamond = pvpData.loseToken, pvpScore = pvpData.losePoint).apply {
                        AwardManager.gainAward(this)
                    }
            }
            ReportManager.pk(0, pvpScore.value)
            uploadPvpRank()
        }
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
        increaseIntValueByKey(KEY_DIED_IN_PVP)
    }

    private fun lessThanMax(): Boolean {
        return (pkLoseToday.value + pkWinToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()) &&
                pkNumToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun pkWined(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkWin.value += 1
        pkWinToday.value += 1
        if (pkTargetList.contains(currentTarget.value.userId)) {
            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
            repo.inBattle.value = false
        } else {
            pkTargetList.add(currentTarget.value.userId)
            val index = currentTargets.value.sortedByDescending { it.pvpScore }
                .indexOfFirst { it.userId == currentTarget.value.userId }

            val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool(MODE_PVP).size) {
                repo.gameCore.getPvpPool(MODE_PVP).last()
            } else {
                repo.gameCore.getPvpPool(MODE_PVP)[index]
            }
            if (lessThanMax()) {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    setListObject(KEY_PK_TARGET, pkTargetList)
                    Dialogs.awardDialog.value =
                        Award(pvpDiamond = pvpData.winToken, pvpScore = pvpData.winPoint).apply {
                            AwardManager.gainAward(this)
                        }
                    uploadPvpRank()
                    ReportManager.pk(1, pvpScore.value)
                }
            }
            repo.inBattle.value = false
            GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
            Dialogs.gameWinDialog.value = allies + enemies
            restartEffect(dialogEffectState, winBattleEffect)
        }
    }

    fun getMockPvpData(): List<RankData> {
        val allyList =
            repo.gameCore.getAllyPool().filter { it.isHero() && it.star == 1 }
                .shuffled(RANDOM)
                .take(1) +
                    repo.gameCore.getAllyPool()
                        .filter { !it.isHero() && it.star == 1 && it.quality in listOf(4, 5, 6, 7) }
                        .shuffled(RANDOM)
                        .take(7)
        return List(20) {
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value),
                    (pvpScore.value * 1.1f).toInt()
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = allyList.map { it.id })
            )
        } + List(30) {
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value * 0.9f).toInt(),
                    (pvpScore.value),
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = allyList.map { it.id })
            )
        }
    }

    fun getPvpData(): PvpData {
        return PvpData(
            talentIds = TalentManager.getPvpTalents(),
            allyIds = lastPvpAllyIds.ifEmpty {
                repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                    .shuffled(RANDOM).take(6).map { it.id }
            },
            win = pkWinToday.value,
            lose = pkLoseToday.value
        )
    }

    fun refreshTargets() {
        val listAbove = targetsFromServer.value.filter { it.pvpScore > pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        val listBelow = targetsFromServer.value.filter { it.pvpScore <= pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        if (listAbove.size < 2) {
            currentTargets.value =
                (listAbove + listBelow.take(5 - listAbove.size)).sortedByDescending { it.pvpScore }
        } else if (listBelow.size < 3) {
            currentTargets.value =
                (listAbove.take(5 - listBelow.size) + listBelow).sortedByDescending { it.pvpScore }
        } else {
            currentTargets.value =
                (listAbove.take(2) + listBelow.take(3)).sortedByDescending { it.pvpScore }
        }
    }

    fun uploadPvpRank() {
        // todo 放开lite包上传
        if ((DebugManager.uploadRank || !BuildConfig.FLAVOR.contains("Lite"))) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                var retryCount = 0
                val maxRetries = 3

                while (retryCount <= maxRetries) {
                    try {
                        postPvpRankData(
                            RankData(
                                time = getCurrentTime(),
                                versionCode = getVersionCode(),
                                userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                                    ?: GameApp.instance.getWrapString(R.string.not_login),
                                userId = GameApp.instance.getObjectId() ?: "0",
                                userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "skill_7019" else GameApp.instance.getAvatarUrl()
                                    ?: "0",
                                pvpScore = PvpManager.pvpScore.value,
                                pvpLastScore = 0, // 废弃
                                pvpData = getPvpData(),
                                platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                                serverId = ServerManager.getSavedServerId(),
                            )
                        )
                        GameApp.instance.getWrapString(R.string.pvp_upload_ok).toast()
                        break // 成功则退出循环
                    } catch (e: Exception) {
                        retryCount++
                        if (retryCount > maxRetries) {
                            GameApp.instance.getWrapString(R.string.pvp_upload_failed).toast()
                            Timber.e(e)
                        } else {
                            delay(10000L * retryCount) // 递增延迟重试
                        }
                    }
                }
            }
        }
    }

    fun hasRedSell(): Boolean {
        return SellManager.getRedFree(SELL_TYPE_PVP)
    }

    fun hasRedPk(): Boolean {
        return pkNumToday.value < MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun hasRedTask(): Boolean {
        return QuestManager.pvpTasks.any {
            QuestManager.getTaskDoneFlow(it)
                    && !it.opened
        }
    }

    fun hasRedAll(): Boolean {
        return hasRedSell() || hasRedPk() || hasRedTask()
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}