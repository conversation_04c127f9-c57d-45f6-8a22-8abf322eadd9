package com.moyu.chuanqirensheng.feature.server

import com.moyu.chuanqirensheng.sub.datastore.getServerIdFromStore3SyncNoEncrypt
import com.moyu.chuanqirensheng.sub.datastore.setServerIdToStore3SyncNoEncrypt

object ServerManager {

    fun getSavedServerId(): Int {
        return getServerIdFromStore3SyncNoEncrypt()
    }

    suspend fun setSavedServerId(serverId: Int) {
        setServerIdToStore3SyncNoEncrypt(serverId)
    }

}