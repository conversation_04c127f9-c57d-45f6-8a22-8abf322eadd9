package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.sell.preCondition
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.button.RefreshButton
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.resource.CurrentAllyCouponPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentHeroCouponPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentPvpPoint
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.TYPE_KEY


@Composable
fun SellPage(itemTypes: List<Int>, showPvpDiamond: Boolean = false) {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    val shopChests =
        SellManager.items.filter { it.type in itemTypes && it.preCondition() }.sortedBy { it.priority }.filter {
            // storageType是永久限量，storage是剩余数量
            it.storage > 0 || it.storageType != 2
        }.filter {
            // 钥匙商店，如果是谷歌商店，不显示没有谷歌商品id的商品
            if (it.isAifadian()) {
                if (it.googleItemId == "0" && GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    false
                } else if (GameApp.instance.isTaptap() && it.desc == "0"){
                    // 付费商品+taptap版本+没有跳转链接  来做到不显示
                    false
                } else true
            } else true
        }.filter {
            // 头条包不要广告
            if (GameApp.instance.isToutiao()) !it.isAd() else true
        }
    Column {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier
                .padding(start = padding14)
        ) {
            if (showPvpDiamond) {
                CurrentPvpPoint(
                    showPlus = true,
                    showFrame = true
                )
            } else {
                CurrentKeyPoint(
                    showPlus = true,
                    showFrame = true
                )
                CurrentDiamondPoint(
                    showPlus = false,
                    showFrame = true
                )
                CurrentAllyCouponPoint(showFrame = true)
                CurrentHeroCouponPoint(showFrame = true)
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = padding19)
                    .verticalScroll(
                        rememberScrollState()
                    ),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                shopChests.groupBy { it.type }.entries.sortedBy { it.key }.forEach { typeGroups ->
                    typeGroups.value.groupBy { it.title }.keys.forEach { title ->
                        val items = shopChests.filter { it.type == typeGroups.key }
                            .filter { it.title == title }
                        GameLabel {
                            if (items.any { it.canRefresh }) {
                                val cost = VipManager.getShopRefreshCost()
                                RefreshButton(
                                    Modifier
                                        .align(Alignment.CenterEnd)
                                        .padding(end = padding19),
                                    text = stringResource(id = R.string.refresh)
                                ) {
                                    val refreshedNum = when (typeGroups.key) {
                                        1 -> SellManager.refresh1Count.intValue
                                        else -> SellManager.refresh2Count.intValue
                                    }
                                    val limit = VipManager.getRealShopRefreshLimit()
                                    if (refreshedNum >= limit) {
                                        (GameApp.instance.getWrapString(R.string.reach_max_refresh) + limit + GameApp.instance.getWrapString(
                                            R.string.reach_max_refresh2
                                        )).toast()
                                    } else {
                                        Dialogs.alertDialog.value = CommonAlert(
                                            title = GameApp.instance.getWrapString(R.string.refresh_shop),
                                            content = GameApp.instance.getWrapString(R.string.refresh_cost) + cost + GameApp.instance.getWrapString(
                                                R.string.refresh_cost2
                                            ),
                                            confirmText = GameApp.instance.getWrapString(R.string.refresh),
                                            onConfirm = {
                                                if (AwardManager.key.value >= cost) {
                                                    AwardManager.gainKey(-cost)
                                                    SellManager.refreshSellItemByType(typeGroups.key)
                                                } else {
                                                    GiftManager.onDiamondNotEnough()
                                                    GameApp.instance.getWrapString(R.string.key_not_enough)
                                                        .toast()
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                            Text(
                                text = title,
                                style = MaterialTheme.typography.h2,
                                textAlign = TextAlign.Center
                            )
                        }
                        FlowRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            overflow = FlowRowOverflow.Visible,
                            maxItemsInEachRow = 3
                        ) {
                            items
                                .sortedBy { if (typeGroups.key == TYPE_KEY) it.price else it.priceType }
                                .forEach {
                                    OneSellItem(it)
                                }
                        }
                    }
                    Spacer(modifier = Modifier.size(padding48))
                }
            }
        }
    }
}