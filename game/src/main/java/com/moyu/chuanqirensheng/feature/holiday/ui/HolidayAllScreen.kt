package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.B50


@Composable
fun HolidayAllScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.holiday_tab1),
            GameApp.instance.getWrapString(R.string.holiday_tab2),
            GameApp.instance.getWrapString(R.string.holiday_tab3),
            GameApp.instance.getWrapString(R.string.holiday_tab4),
            GameApp.instance.getWrapString(R.string.holiday_tab5),
        )
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    LaunchedEffect(Unit) {
        SellManager.init()
        HolidayManager.init(true)
    }
    GameBackground(
        title = stringResource(id = R.string.holiday_title),
        bgMask = B50,
        background = R.drawable.holiday_bg
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pageState = pagerState,
                titles = listTabItems,
                redIcons = HolidayManager.getRedIcons()
            )
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> HolidaySignPage()
                    1 -> HolidayQuestPage()
                    2 -> HolidayLotteryPage()
                    3 -> HolidaySellPage()
                    else -> HolidayRankPage()// 加密，不然可能有风险
                }
            }
        }
    }
}