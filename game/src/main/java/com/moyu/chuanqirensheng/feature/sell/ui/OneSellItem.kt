package com.moyu.chuanqirensheng.feature.sell.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.ButtonType
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.ad.KEY_BUY_AD_ITEM
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.core.aiFaDian
import com.moyu.core.model.Sell
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun OneSellItem(sell: Sell, scale: Float = 1f) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        SingleNormalChest(Modifier.scale(scale), sell) {
            SellButton(sell = sell)
        }
        Spacer(modifier = Modifier.size(padding16))
    }
}

@Composable
fun SellButton(sell: Sell, showRealMoney: Boolean = false) {
    GameButton(
        text = if (sell.storage <= 0) stringResource(R.string.sold_out) else {
            if (sell.isAifadian()) {
                if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        sell.priceDollar.toString()
                    } else {
                        sell.price.toString()
                    }
                } else if (showRealMoney) {
                    sell.price.toString()
                } else {
                    GameApp.instance.getWrapString(R.string.go_and_get)
                }
            } else sell.price.toString()
        },
        buttonSize = ButtonSize.MediumMinus,
        buttonStyle = ButtonStyle.Orange,
        buttonType = if (sell.storage <= 0) ButtonType.Normal
        else if (sell.isAifadian()) if (showRealMoney) ButtonType.RealMoney else ButtonType.AiFaDian
        else if (sell.isAd()) ButtonType.Ad
        else if (sell.isKeyMoney()) ButtonType.Key
        else if (sell.isPvpMoney()) ButtonType.Pvp
        else ButtonType.Diamond,
        enabled = sell.storage > 0,
        onClick = {
            if (sell.storage <= 0) {
                GameApp.instance.getWrapString(R.string.sold_out).toast()
            } else if (sell.isHoliday() && !HolidayLotteryManager.canDoCheap()) {
                GameApp.instance.getWrapString(R.string.lottery_run_out).toast()
            } else if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
                GiftManager.onDiamondNotEnough()
                GameApp.instance.getWrapString(R.string.diamond_not_enough).toast()
            } else if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
                GiftManager.onPvpDiamondNotEnough()
                GameApp.instance.getWrapString(R.string.pvp_diamond_not_enough).toast()
            } else if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
                GiftManager.onKeyNotEnough()
                GameApp.instance.getWrapString(R.string.key_not_enough).toast()
            } else if (sell.isAifadianTower() && !GameApp.instance.resources.getBoolean(R.bool.has_billing) && AwardManager.realMoney.value < sell.price) {
                if (GameApp.instance.canShowAifadian()) {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.real_money_not_enough),
                            content = GameApp.instance.getWrapString(R.string.real_money_not_enough_content),
                            onConfirm = {
                                val uri: Uri = Uri.parse(aiFaDian)
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                ContextCompat.startActivity(
                                    GameApp.instance.activity,
                                    intent,
                                    Bundle()
                                )
                            })
                } else {
                    GameApp.instance.getWrapString(R.string.real_money_not_enough).toast()
                }
            } else {
                if (sell.isAd()) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AdHolder.playAd(KEY_BUY_AD_ITEM) {
                            if (sell.isNewTaskPackage()) {
                                SevenDayManager.openPackage(sell)
                            } else if (sell.isHoliday()) {
                                HolidayManager.openPackage(sell)
                            } else if (sell.isTower()) {
                                TowerManager.openPackage(sell)
                            } else if (sell.isMonthCard()) {
                                MonthCardManager.openPackage(sell)
                            } else if (sell.isLotteryGift()) {
                                LotteryManager.openPackage(sell)
                            } else {
                                SellManager.openSellChest(sell)
                            }
                        }
                    }
                } else if (sell.isAifadian()) {
                    if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                        // 谷歌内购
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            BillingManager.prepay(sell) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    if (sell.isNewTaskPackage()) {
                                        SevenDayManager.openPackage(sell)
                                    } else if (sell.isHoliday()) {
                                        HolidayManager.openPackage(sell)
                                    } else if (sell.isTower()) {
                                        TowerManager.openPackage(sell)
                                    } else if (sell.isLotteryGift()) {
                                        LotteryManager.openPackage(sell)
                                    } else if (sell.isMonthCard()) {
                                        MonthCardManager.openPackage(sell)
                                    } else {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            }
                        }
                    } else {
                        if (sell.isAifadianTower()) {
                            Dialogs.alertDialog.value =
                                CommonAlert(
                                    title = GameApp.instance.getWrapString(R.string.confirm_buy),
                                    content = GameApp.instance.getWrapString(R.string.confirm_buy2) + sell.name + "?",
                                    onConfirm = {
                                        // todo 爬塔的爱发电sell
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            if (sell.isNewTaskPackage()) {
                                                SevenDayManager.openPackage(sell)
                                            } else if (sell.isHoliday()) {
                                                HolidayManager.openPackage(sell)
                                            } else if (sell.isTower()) {
                                                TowerManager.openPackage(sell)
                                            } else if (sell.isMonthCard()) {
                                                MonthCardManager.openPackage(sell)
                                            } else if (sell.isLotteryGift()) {
                                                LotteryManager.openPackage(sell)
                                            } else {
                                                SellManager.openSellChest(sell)
                                            }
                                        }
                                    })
                        } else {
                            if (sell.desc == "0") {
                                GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                            } else {
                                if (GameApp.instance.canShowAifadian()) {
                                    val uri: Uri = Uri.parse(sell.desc)
                                    val intent = Intent(Intent.ACTION_VIEW, uri)
                                    ContextCompat.startActivity(
                                        GameApp.instance.activity,
                                        intent,
                                        Bundle()
                                    )
                                } else {
                                    GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                                }
                            }
                        }
                    }
                } else {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.confirm_buy),
                            content = GameApp.instance.getWrapString(R.string.confirm_buy2) + sell.name + "?",
                            onConfirm = {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    if (sell.isNewTaskPackage()) {
                                        SevenDayManager.openPackage(sell)
                                    } else if (sell.isHoliday()) {
                                        HolidayManager.openPackage(sell)
                                    } else if (sell.isTower()) {
                                        TowerManager.openPackage(sell)
                                    } else if (sell.isMonthCard()) {
                                        MonthCardManager.openPackage(sell)
                                    } else if (sell.isLotteryGift()) {
                                        LotteryManager.openPackage(sell)
                                    } else {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            })
                }
            }
        })
}