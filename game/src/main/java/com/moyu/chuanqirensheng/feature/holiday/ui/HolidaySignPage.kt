package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.holiday.HolidaySignManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getDaySinceDec24
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun HolidaySignPage() {
    val signs = HolidaySignManager.getShowSigns()
    Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Center) {
        LazyVerticalGrid(
            modifier = Modifier
                .fillMaxWidth()
                .padding(padding10).padding(bottom = padding10),
            columns = GridCells.Fixed(4),
            content = {
                items(signs.size) { index ->
                    val sign = signs[index]
                    Box(contentAlignment = Alignment.Center) {
                        Text(
                            modifier = Modifier.align(Alignment.TopCenter).graphicsLayer {
                                translationY = -padding2.toPx()
                            },
                            text = stringResource(R.string.day_of, sign.day),
                            color = if (getDaySinceDec24() >= sign.day) Color.White else Color.Gray,
                            style = MaterialTheme.typography.h3
                        )
                        val param = if (getDaySinceDec24() >= sign.day) {
                            defaultParam.copy(
                                itemSize = ItemSize.Large,
                                showName = false,
                                showEffect = false
                            ) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    HolidaySignManager.gain(sign)
                                }
                            }
                        } else {
                            defaultParam.copy(
                                itemSize = ItemSize.Large,
                                showName = false,
                                showEffect = false
                            )
                        }
                        AwardList(
                            award = sign.toAward(),
                            param = param
                        )
                        if (HolidaySignManager.isSignGained(sign)) {
                            Image(
                                painter = painterResource(id = R.drawable.common_choose),
                                contentDescription = null,
                                modifier = Modifier.size(imageLarge)
                            )
                        }
                        if (getDaySinceDec24() >= sign.day && !HolidaySignManager.isSignGained(sign)) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .size(imageSmall),
                                painter = painterResource(R.drawable.red_icon),
                                contentDescription = null
                            )
                        }
                    }
                }
            })
    }
}