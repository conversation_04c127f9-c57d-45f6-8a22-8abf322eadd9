package com.moyu.chuanqirensheng.feature.talent

import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.pvp.pvpMasterIds
import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.feature.quest.onTaskTalentUp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkTalent
import com.moyu.chuanqirensheng.sub.datastore.KEY_TALENT_LEVEL
import com.moyu.chuanqirensheng.sub.datastore.getMapObject
import com.moyu.chuanqirensheng.sub.datastore.setMapObject
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.core.GameCore
import com.moyu.core.model.Talent
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.builtins.serializer
import kotlin.math.max

val reportLevels = listOf(5, 10)

object TalentManager {
    val talents = mutableStateMapOf<Int, Int>()

    fun init() {
        talents.clear()
        talents.putAll(getMapObject(KEY_TALENT_LEVEL, Int.serializer(), Int.serializer()))
    }

    suspend fun upgradeTalent(talent: Talent): Talent? {
        checkTalent(talent = talent)
        val talentLevel = talents[talent.mainId] ?: 0
        GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent)
        talents[talent.mainId] = talentLevel + 1
        save()
        GameApp.globalScope.launch(Dispatchers.Main) {
            onTaskTalentUp(talentLevel + 1)
            val allLevel = talents.values.sum()
            if (allLevel in reportLevels || (allLevel > 10 && allLevel % 10 == 0)) {
                ReportManager.onTalentUpgrade(level = allLevel)
            }
        }
        return repo.gameCore.getTalentPool()
            .firstOrNull { it.mainId == talent.mainId && it.level == talentLevel + 1 }
    }

    fun save() {
        setMapObject(KEY_TALENT_LEVEL, talents, Int.serializer(), Int.serializer())
    }

    fun getLockInfoByTalent(scroll: Talent): Pair<Boolean, String> {
        return when (scroll.conditionType) {
            0 -> Pair(false, "")
            100 -> {
                val target = scroll.conditionNum
                val targetTalent = repo.gameCore.getTalentPool().first {
                    it.type == scroll.type && it.position.first() == scroll.position.first() - 1 && it.position[1] == scroll.position[1]
                }
                Pair(
                    (talents[targetTalent.mainId] ?: 0) < target,
                    GameApp.instance.getWrapString(R.string.talent_condition1, target)
                )
            }
            200 -> {
                // 前一页技能总等级达到指定要求
                val prevType = scroll.type - 1
                val sum = getTotalLevelByType(prevType)
                val target = scroll.conditionNum
                Pair(
                    sum < target,
                    GameApp.instance.getWrapString(R.string.talent_condition2, target)
                )
            }

            else -> {
                val target = scroll.conditionNum
                Pair(
                    false,
                    GameApp.instance.getWrapString(R.string.talent_condition3, target)
                )
            }
        }
    }

    fun getTotalLevelByType(type: Int): Int {
        return repo.gameCore.getTalentPool().filter { it.type == type && it.level == 1 }.sumOf { talents[it.mainId] ?: 0 }
    }

    fun getUnlockedPageSize(): Int {
        repeat(repo.gameCore.getTalentPool().maxOf { it.type }) {
            if (it != 0) {
                // 第一页不用算，默认显示两页
                if (getTotalLevelByType(it + 1) < repo.gameCore.getUnlockTalentPageLevel()) {
                    // todo 写死，至少要解锁两页
                    return max(2, it + 1)
                }
            }
        }
        // todo 写死，至少要解锁两页
        return max(2, repo.gameCore.getTalentPool().maxOf { it.type })
    }

    fun getPvpTalents(): Map<Int, Int> {
        return talents.filter { it.value > 0 }.filter { it.key in pvpTalentMainIds || it.key in pvpMasterIds }
    }
}