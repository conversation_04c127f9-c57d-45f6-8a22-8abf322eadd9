package com.moyu.chuanqirensheng.feature.sign.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.router.SIGN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SIGN
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun SignIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_SIGN)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Box(modifier = modifier.graphicsLayer {
            translationX = LabelSize.Large.width.toPx() / 2.4f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                text = stringResource(R.string.sign_title),
                icon = R.drawable.icon_sign
            ) {
                goto(SIGN_SCREEN)
            }
            if (SignManager.getShowSigns().any {
                    getLoginDays() >= it.day && !SignManager.isSignGained(it)
                }) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopCenter).padding(end = padding6, top = padding2)
                        .size(imageTinyPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}
