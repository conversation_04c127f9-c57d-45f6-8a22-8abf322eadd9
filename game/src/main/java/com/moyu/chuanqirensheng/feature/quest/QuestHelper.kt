package com.moyu.chuanqirensheng.feature.quest

import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueIfBiggerByKey
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Sell
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isProfession

fun getLoginDays(): Int {
    return getIntFlowByKey(KEY_GAME_LOGIN_DAY)
}

//  1=次数，2=天数
fun onTaskStartGameTime() {
    setIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.START_GAME.id + "_1", 1)
}

fun onTaskStartGameDay() {
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.START_GAME.id + "_2")
    increaseIntValueByKey(KEY_GAME_LOGIN_DAY)
}

// 1=x年，100=维持100年x次
// 200=维持200年x次
fun onTaskAge(age: Int) {
    val difficult = DifficultManager.getSelected().id
    (1..difficult).forEach {
        setIntValueIfBiggerByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_${it}", age)
        setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_${it}", age)
    }
    setIntValueIfBiggerByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id, age)
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id, age)
}

fun onTaskEnterEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENTER_EVENT.id + "_${event.play}")
}

fun onTaskKillEnemy(enemies: List<Role>) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id, enemies.size)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id,
        enemies.size
    )
    enemies.forEach {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
        it.getRace().getRaceCountryIndex()?.let {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_${it + 11}", 1
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.KILL.id + "_${it + 11}", 1
            )
        }
    }
}

fun onTaskDoneEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.play}")
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.id}")
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${event.id}")
}

fun onTaskGetItem(award: Award) {
    if (award.equips.isNotEmpty()) {
        award.equips.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_40",
                award.equips.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_40",
                award.equips.size
            )
        }
    }
    if (award.allies.isNotEmpty()) {
        award.allies.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_1" + "${it.star}",
                award.allies.sumOf { it.num }
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_1" + "${it.star}",
                award.allies.sumOf { it.num }
            )
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_10",
                award.allies.sumOf { it.num }
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_10",
                award.allies.sumOf { it.num }
            )
        }
    }
    if (award.skills.isNotEmpty()) {
        award.skills.forEach {
            if (it.isMagic()) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_20",
                    award.skills.size
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_20",
                    award.skills.size
                )
            } else if (it.isProfession()) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_30",
                    award.skills.size
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_30",
                    award.skills.size
                )
            }
        }
    }
    if (award.resources.any { it > 0 }) {
        award.resources.forEachIndexed { index, i ->
            if (i > 0) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_5" + "${index + 1}",
                    i
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAIN_ITEM.id + "_5" + "${index + 1}",
                    i
                )
            }
        }
    }
}

val recordingEventIds = repo.gameCore.getGameTaskPool().filter { it.type == QuestEvent.ENDING.id && it.subType.first() > 10000 }.map { it.subType.first() }

fun onTaskEnding(event: Event, win: Boolean) {
    if ((event.isEnd || event.id in recordingEventIds) && win) {
        setBooleanValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENDING.id + "_${event.id}" + "_${DifficultManager.getSelected().id}", true)
        setBooleanValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENDING.id + "_${event.id}" + "_${0}", true)
    }
}

fun onTaskSpecialDecision1(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27000 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27000 + type}",
        1
    )
}

fun onTaskSpecialDecision2(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27003 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27003 + type}",
        1
    )
}

fun onTaskSpecialDecision3(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27006 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27006 + type}",
        1
    )
}

fun onTaskSpecialDecision4(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27009 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27009 + type}",
        1
    )
}

fun onTaskSpecialDecision5(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27012 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27012 + type}",
        1
    )
}

fun onTaskSpecialDecision6(type: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27015 + type}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.FATAL_PLAY.id + "_${27015 + type}",
        1
    )
}

fun onTaskBuy(sell: Sell) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.BUY.id + "_${sell.type}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.BUY.id + "_${sell.type}")
}

fun onTaskStarUp(ally: Ally, level: Int) {
    // 1=任意升到x级，2=总等级升到x级
    if (ally.isHero()) {
        setIntValueIfBiggerByKey(
            KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_1",
            level
        )
        setIntValueIfBiggerByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_1",
            level
        )
    } else {
        setIntValueIfBiggerByKey(
            KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_2",
            level
        )
        setIntValueIfBiggerByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.STAR_UP.id + "_2",
            level
        )
    }
}

fun onDrawCard(isHero: Boolean, count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    if (isHero) {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_1",
            count
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_1",
            count
        )
    } else {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_2",
            count
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id + "_2",
            count
        )
    }
}


fun onTaskTalentUp(level: Int) {
    // 1=任意升到x级，2=总等级升到x级
    setIntValueIfBiggerByKey(
        KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_1",
        level
    )
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_1",
        level
    )
    val totalLevel = TalentManager.talents.values.sum()
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_2", totalLevel)
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.TALENT_UP.id + "_2",
        totalLevel
    )
}