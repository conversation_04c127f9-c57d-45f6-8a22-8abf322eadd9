package com.moyu.chuanqirensheng.feature.gift

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp.Companion.instance
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.sell.SellManager.items
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_HERO_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_DISPLAY_GIFT
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_ALLY_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_HERO_FAILED
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELL_ITEMS
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_DRAW_ID_PREFIX
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Gift
import com.moyu.core.model.toAward

val googleGiftIds = repo.gameCore.getSellPool().filter { it.isGoogle() }.map { it.id }
val taptapGiftIds = repo.gameCore.getSellPool().filter { it.isTaptap() }.map { it.id }


fun Gift.match(): Boolean {
    return if (instance.resources.getBoolean(R.bool.has_billing)) {
        googleGiftIds.contains(this.id)
    } else {
        taptapGiftIds.contains(this.id)
    }
}


object GiftManager {
    var lastShowTime = 0L
    private val displayGifts = mutableListOf<Gift>()
    fun init() {
        // todo 兼容之前的首冲礼包记录，注意，确保首冲是gift的第一个
        if (getBooleanFlowByKey(KEY_GIFT_AWARDED + 9001)) {
            setBooleanValueByKey(
                KEY_GIFT_AWARDED + repo.gameCore.getGiftPool().first().id,
                true
            )
        }
        val savedGifts = getListObject(KEY_DISPLAY_GIFT, Gift.serializer())
        repo.gameCore.getGiftPool().filter { it.match() }.forEach { gift ->
            val savedGift = savedGifts.firstOrNull { it.id == gift.id }
            if (savedGift == null) {
                displayGifts.add(gift)
            } else {
                displayGifts.add(
                    gift.copy(
                        displayTime = savedGift.displayTime,
                        dialogShowed = savedGift.dialogShowed,
                    )
                )
            }
        }
    }


    fun onRefreshNumTheNextDay() {
        setIntValueByKey(KEY_CONSUME_ALLY_COUPON, 0)
        setIntValueByKey(KEY_CONSUME_HERO_COUPON, 0)
        setIntValueByKey(KEY_DIED_IN_GAME, 0)
        setIntValueByKey(KEY_KEY_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_DIAMOND_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_PVP_DIAMOND_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_DIED_IN_PVP, 0)
        setIntValueByKey(KEY_DRAW_ALLY_FAILED, 0)
        setIntValueByKey(KEY_DRAW_HERO_FAILED, 0)
        UnlockManager.drawUnlockIds.forEach { id ->
            if (getBooleanFlowByKey(
                    KEY_UNLOCK_DRAW_ID_PREFIX + id
                )
            ) {
                setBooleanValueByKey(KEY_UNLOCK_DRAW_ID_PREFIX + id, false)
            }
        }
    }

    fun setShowed(gift: Gift) {
        lastShowTime = getCurrentTime()
        val index = displayGifts.indexOfFirst { it.id == gift.id }
        displayGifts[index] = displayGifts[index].copy(dialogShowed = true)
        setListObject(KEY_DISPLAY_GIFT, displayGifts, Gift.serializer())
    }

    // dictShowDialog: 是否直接弹窗，直接弹窗则要判断triggerType
    fun getDisplayGifts(): List<Gift> {
        if (!instance.canShowAifadian()) {
            return emptyList()
        }
        // 没有正在显示的，就重新筛选
        val pool = repo.gameCore.getGiftPool().filter { it.match() }
        return pool.filter {
            // 未购买的，或者限购为0的，显示
            !getBooleanFlowByKey(KEY_GIFT_AWARDED + it.id) || it.limitBuy == 0
        }.filter {
            it.filterGiftUnlock()
        }.filter {
            val unlocks = repo.gameCore.getPoolById(it.poolId).toAward().unlockList
            if (unlocks.isNotEmpty()) {
                unlocks.all { id ->
                    // 如果是解锁类礼包，已经解锁了的话，就不要展示
                    !UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(id))
                }
            } else true
        }.filter {
            // 如果有显示时间，表示已经显示过了，要判定时间是否超过，而且要根据triggerType处理
            // 上次显示的时间戳
            val displayTime = displayGifts.first { gift -> gift.id == it.id }.displayTime
            if (displayTime == 0L) {
                true
            } else {
                // 可以显示的时间(limitTime == 0表示不限时，则不会过期)
                val displayTimeMillis =
                    if (it.limitTime == 0) 99999999999L else it.limitTime * 60 * 1000L
                // 当前时间，减去上次显示的时间，大于显示时间，则表示已经过期
                val displayTimePassed = getCurrentTime() - displayTime > displayTimeMillis
                when (it.triggerType) {
                    1 -> {
                        // 终身触发一次，需要保证显示未超时
                        !displayTimePassed
                    }

                    2 -> {
                        // 每日触发一次，需要保证今天没显示过
                        !displayTimePassed || (!isSameDay(getCurrentTime(), displayTime))
                    }

                    else -> false
                }
            }
        }.sortedBy { it.priority }.let {
            it.forEach { gift ->
                val displayTime = displayGifts.first { it.id == gift.id }.displayTime
                // 可以显示的时间(limitTime == 0表示不限时，则不会过期)
                val displayTimeMillis =
                    if (gift.limitTime == 0) 99999999999L else gift.limitTime * 60 * 1000L
                // 当前时间，减去上次显示的时间，大于显示时间，则表示已经过期
                val displayTimePassed = getCurrentTime() - displayTime > displayTimeMillis
                if (displayTime == 0L) {
                    // 首次展示，需要记录时间
                    val index = displayGifts.indexOfFirst { it.id == gift.id }
                    displayGifts[index] =
                        displayGifts[index].copy(displayTime = getCurrentTime())
                }
                if (!isSameDay(getCurrentTime(), displayTime)
                    && (gift.isTriggerEveryDay() && (displayTimePassed || gift.limitTime == 0))
                ) {
                    // 如果triggerType==2的礼包，第二天重置时间前，要判定，如果正在显示，还没结束，不重置
                    val index = displayGifts.indexOfFirst { it.id == gift.id }
                    displayGifts[index] = displayGifts[index].copy(
                        dialogShowed = false,
                        displayTime = getCurrentTime()
                    )
                }
            }
            setListObject(KEY_DISPLAY_GIFT, displayGifts, Gift.serializer())
            it.map {
                val copy = displayGifts.first { gift -> gift.id == it.id }
                it.copy(displayTime = copy.displayTime, dialogShowed = copy.dialogShowed)
            }.filter {
                // 再判定一次unlock，有可能更新后，dialogShowed变了
                it.filterGiftUnlock()
            }
        }
    }

    fun Gift.filterGiftUnlock(): Boolean {
        // 出现条件判定
        val unlock = repo.gameCore.getUnlockById(this.unlockId)
        // todo 特殊逻辑，首页icon（!isBattle），只要已经展示了，就不要再判定条件
        return if (displayGifts.firstOrNull { gift -> gift.id == this.id }?.dialogShowed == true) true
        else {
            UnlockManager.getUnlockedFlow(unlock)
        }
    }

    fun onKeyNotEnough() {
        increaseIntValueByKey(KEY_KEY_NOT_ENOUGH)
    }

    fun onPvpDiamondNotEnough() {
        increaseIntValueByKey(KEY_PVP_DIAMOND_NOT_ENOUGH)
    }

    fun onDiamondNotEnough() {
        increaseIntValueByKey(KEY_DIAMOND_NOT_ENOUGH)
    }

    fun onDrawAllyFailed() {
        increaseIntValueByKey(KEY_DRAW_ALLY_FAILED)
    }

    fun onDrawHeroFailed() {
        increaseIntValueByKey(KEY_DRAW_HERO_FAILED)
    }

    fun onDrawUnlockId(id: Int) {
        if (UnlockManager.drawUnlockIds.contains(id) && !getBooleanFlowByKey(
                KEY_UNLOCK_DRAW_ID_PREFIX + id
            )
        ) {
            setBooleanValueByKey(KEY_UNLOCK_DRAW_ID_PREFIX + id, true)
        }
    }

    fun onGiftBought(sellId: Int) {
        setBooleanValueByKey(KEY_GIFT_AWARDED + sellId, true)
        setBooleanValueByKey(SELL_FOREVER + sellId, true)
        items.indexOfFirst { it.id == sellId }.takeIf { it >= 0 }?.let {
            if (!items[it].isMonthCard()) {
                items[it] = items[it].copy(storage = items[it].storage - 1)
                setListObject(KEY_SELL_ITEMS, items)
            }
        }
    }
}