package com.moyu.chuanqirensheng.feature.difficult.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager.maxDoneDifficultLevel
import com.moyu.chuanqirensheng.feature.limit.GameLimitManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toLootAward
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.selectDifficultPanelHeight
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.GameMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun SelectMapLayout() {
    Box {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(selectDifficultPanelHeight)
                .paint(
                    painterResource(id = R.drawable.common_frame_long),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = padding10)
                .horizontalScroll(rememberScrollState()),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DifficultManager.getShowMaps().forEach {
                SingleMapCard(modifier = Modifier.graphicsLayer {
                    translationY = padding6.toPx()
                }, map = it)
            }
        }
        TextLabel2(modifier = Modifier.graphicsLayer {
            translationY = -padding16.toPx()
        }, text = stringResource(R.string.map), labelSize = LabelSize.Medium)
    }
}

@Composable
fun SingleMapCard(modifier: Modifier = Modifier, map: GameMap) {
    EffectButton(modifier = modifier.size(bottomItemSize), onClick = {
        if (DifficultManager.isMapLocked(map.id)) {
            GameApp.instance.getWrapString(R.string.unlock_map_tips).toast()
        } else {
            DifficultManager.selectMap(map)
        }
    }) {
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.shop_draw_frame),
            contentDescription = null
        )
        Text(
            modifier = Modifier.align(Alignment.TopCenter),
            textAlign = TextAlign.Center,
            text = map.name,
            style = MaterialTheme.typography.h5,
            color = Color.White
        )
        Image(
            painter = painterResource(id = getImageResourceDrawable("map${map.id}")),
            modifier = Modifier
                .size(imageLarge)
                .align(Alignment.TopCenter)
                .graphicsLayer {
                    translationY = padding19.toPx()
                },
            contentDescription = null
        )
        if (DifficultManager.isMapCanLoot(map.id)) {
            Box(Modifier
                .align(alignment = Alignment.BottomCenter)
                .graphicsLayer { translationY = padding10.toPx() },
                contentAlignment = Alignment.CenterStart) {
                GameButton(
                    text = stringResource(R.string.loot),
                    buttonSize = ButtonSize.Small,
                    buttonStyle = ButtonStyle.Orange
                ) {
                    GameLimitManager.init()
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        val award = map.toLootAward().let {
                            if (VipManager.isDoubleEndingAward()) {
                                it.copy(
                                    key = it.key * 2,
                                    diamond = it.diamond * 2,
                                    extraKey = it.key,
                                    extraDiamond = it.diamond
                                )
                            } else it
                        }
                        val realAward = GameLimitManager.getEndingAward(
                            award,
                            ""
                        )
                        if (realAward.couponAlly != 0 || realAward.couponHero != 0 || realAward.diamond != 0) {
                            Dialogs.awardDialog.value = realAward
                        } else {
                            GameApp.instance.getWrapString(R.string.one_day_loot_diamond_limitation).toast()
                        }
                        AwardManager.gainAward(realAward)
                    }
                }
                Image(
                    modifier = Modifier.size(padding16),
                    contentScale = ContentScale.FillHeight,
                    painter = painterResource(id = DifficultManager.getIcon(maxDoneDifficultLevel.value)),
                    contentDescription = null
                )
            }
        }
        if (DifficultManager.getSelectedMap() == map) {
            Image(
                painter = painterResource(id = R.drawable.common_choose),
                modifier = Modifier
                    .size(imageMedium)
                    .align(Alignment.BottomEnd),
                contentDescription = null
            )
        }
        if (DifficultManager.isMapLocked(map.id)) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(imageMedium),
                painter = painterResource(R.drawable.common_lock),
                contentDescription = null
            )
        }
    }
}
