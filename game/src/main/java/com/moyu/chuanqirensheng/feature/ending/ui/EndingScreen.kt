package com.moyu.chuanqirensheng.feature.ending.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26

@Composable
fun EndingScreen() {
    GameBackground(title = stringResource(R.string.life_record)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .verticalScroll(rememberScrollState()).padding(
                    horizontal = padding19, vertical = padding16
                )
        ) {
            val endings = EndingManager.endings.reversed()
            if (endings.isEmpty()) {
                Spacer(modifier = Modifier.size(gapMedium))
                Text(
                    text = stringResource(R.string.no_records_yet),
                    style = MaterialTheme.typography.h2
                )
            } else {
                Spacer(modifier = Modifier.size(padding10))
                LazyVerticalGrid(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    columns = GridCells.Fixed(3),
                    horizontalArrangement = Arrangement.spacedBy(padding26),
                    verticalArrangement = Arrangement.spacedBy(padding16)
                ) {
                    items(endings.size) { index ->
                        val ending = endings[index]
                        SingleEndingCard(
                            ending = ending
                        )
                    }
                }
            }
        }
    }
}




