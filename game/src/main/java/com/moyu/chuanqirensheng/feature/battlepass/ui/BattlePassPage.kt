package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.resource.CurrentPassPoint
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun BattlePassPage() {
    val currentSeason = BattlePassManager.getPassSeason()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ).padding(top = padding19, bottom = padding10)
    ) {
        CurrentPassPoint(Modifier.align(Alignment.CenterHorizontally))
        Box(modifier = Modifier.fillMaxSize()) {
            CurrentPassItem(modifier = Modifier
                .align(Alignment.CenterEnd)
                .graphicsLayer {
                    translationY = -gapMedium.toPx()
                })

            val pool = repo.gameCore.getBattlePassPool().filter { it.season == currentSeason }
            LazyColumn(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(bottom = padding8).padding(start = padding19),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(padding10))
                    }
                    Column {
                        Spacer(modifier = Modifier.size(padding10))
                        OnePassItem(pool[index]) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val unlocked = (BattlePassManager.getCurrentWarPass()?.id
                                    ?: 0) >= pool[index].id
                                if (unlocked) {
                                    if (pool[index].unlockType == 2 && !AwardManager.battlePassBought[BattlePassManager.getPassSeason()]!!.value) {
                                        (GameApp.instance.getWrapString(R.string.battlepass_unlock1) + BattlePassManager.getPassSeason() + GameApp.instance.getWrapString(
                                            R.string.battlepass_unlock2
                                        )).toast()
                                    } else {
                                        BattlePassManager.gain(pool[index])
                                    }
                                } else {
                                    Dialogs.warPassDetailDialog.value = pool[index]
                                }
                            }
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}