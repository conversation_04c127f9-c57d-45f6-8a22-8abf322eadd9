package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding6


val towerTabs = mutableStateListOf(
        GameApp.instance.getWrapString(R.string.tower_tab1),
        GameApp.instance.getWrapString(R.string.tower_tab2),
        GameApp.instance.getWrapString(R.string.tower_tab3),
    )

@Composable
fun TowerAllScreen() {
    val listTabItems = remember {
        towerTabs
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    GameBackground(title = stringResource(id = R.string.tower_mode)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> TowerPage()
                    1 -> TowerSellPage()
                    else -> TowerRankPage()
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding6.toPx()
                },
                pageState = pagerState,
                titles = listTabItems,
                redIcons = TowerManager.getRedIcons()
            )
        }
    }
}