package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.holiday.HolidaySignManager
import com.moyu.chuanqirensheng.feature.router.HOLIDAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.MovableImage
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getNYStartTimeMillis
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import kotlinx.coroutines.delay

@Composable
fun HolidayIcon(modifier: Modifier = Modifier) {
    if (HolidayManager.canShowHolidayIcon()) {
        val nyStartTimeMillis = getNYStartTimeMillis()
        val currentTime = remember {
            mutableLongStateOf(0L)
        }
        LaunchedEffect(Unit) {
            if (isNetTimeValid()) {
                while (true) {
                    currentTime.longValue = getCurrentTime()
                    delay(500)
                }
            }
        }

        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = padding14.toPx()
            }, onClick = {
                if (HolidayManager.canShowEntrance()) {
                    goto(HOLIDAY_SCREEN)
                } else {
                    GameApp.instance.getWrapString(R.string.not_open).toast()
                }
            }) {
                MovableImage(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    imageResource = R.drawable.chest_1101,
                )
                if (HolidaySignManager.showRed() || HolidayLotteryManager.isCheapFreeLottery() || HolidayManager.hasRed()) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(ItemSize.LargePlus.itemSize / 20)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding4))
            Text(
                text = stringResource(R.string.holiday_title),
                maxLines = 2,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h5
            )
            if (currentTime.value <= nyStartTimeMillis) {
                Text(
                    text = (nyStartTimeMillis - currentTime.longValue).toDayHourMinuteSecond(),
                    style = MaterialTheme.typography.h6,
                )
            }
        }
    }
}
