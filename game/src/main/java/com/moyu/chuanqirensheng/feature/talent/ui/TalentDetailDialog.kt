package com.moyu.chuanqirensheng.feature.talent.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.screen.skill.SkillLevelButton
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.core.model.Award
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.max

@Composable
fun TalentDetailDialog(show: MutableState<Int?>) {
    show.value?.let { talentMainId ->
        val talentLevel = TalentManager.talents[talentMainId] ?: 0
        val showTalentLevel = max(1, talentLevel)
        val showTalent = repo.gameCore.getTalentPool()
            .first { it.level == showTalentLevel && it.mainId == talentMainId }
        val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
        val showSize = if (talentLevel == 0 || talentLevel == showTalent.levelLimit) {
            1
        } else {
            2
        }
        PanelDialog(onDismissRequest = {
            show.value = null
        }, contentBelow = {
            Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterEnd) {
                    SkillLevelButton(Modifier.padding(end = padding36), skill)
                }
                TalentStarUpView(skill = skill)
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                if (talentLevel < showTalent.levelLimit) {
                    Text(
                        text = "${showTalent.name}Lv${talentLevel}-Lv${talentLevel + 1}",
                        style = MaterialTheme.typography.h1,
                        color = Color.Black
                    )
                } else {
                    Text(
                        text = "${showTalent.name}Lv${talentLevel}",
                        style = MaterialTheme.typography.h1,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(padding4))
                val skills =
                    repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
                Spacer(modifier = Modifier.size(padding19))
                Column(
                    Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(horizontal = padding19)
                        .verticalScroll(
                            rememberScrollState()
                        )
                ) {
                    skills.filter { it.level >= showTalentLevel }.take(showSize).forEach {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            if (it.level == talentLevel + 1) {
                                Text(
                                    text = stringResource(id = R.string.next_level),
                                    style = MaterialTheme.typography.h3,
                                    color = Color.Blue
                                )
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(id = R.drawable.common_arrow_down),
                                    contentDescription = null
                                )
                            } else if (it.level == talentLevel) {
                                Text(
                                    text = stringResource(id = R.string.current_level),
                                    style = MaterialTheme.typography.h3,
                                    color = Color.Green
                                )
                                Image(
                                    modifier = Modifier.size(imageSmall),
                                    painter = painterResource(id = R.drawable.common_arrow_down),
                                    contentDescription = null
                                )
                            }
                        }
                        Text(
                            text = "Lv.${it.level}:" + it.getRealDescColorful(
                                MaterialTheme.typography.h2.toSpanStyle()
                            ),
                            style = MaterialTheme.typography.h2,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(padding10))
                    }
                }
            }
        }
    }
}

@Composable
fun TalentStarUpView(modifier: Modifier = Modifier, skill: Skill) {
    val talentLevel = TalentManager.talents[skill.mainId] ?: 0
    val nextLevel = talentLevel + 1
    val scroll = repo.gameCore.getTalentPool().first { it.talentSkill == skill.id }
    val nextScroll = repo.gameCore.getTalentPool()
        .firstOrNull { it.mainId == scroll.mainId && it.level == nextLevel }
    val award = nextScroll?.let {
        (if (nextScroll.costPool == 0) Award() else repo.gameCore.getPoolById(nextScroll.costPool)
            .toAward()) + Award(diamond = nextScroll.cost)
    } ?: Award()

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(padding4))
        val haveNextLevel = scroll.level < scroll.levelLimit
        Row(verticalAlignment = Alignment.CenterVertically) {
            nextScroll?.let {
                AwardList(
                    modifier = modifier,
                    award = award,
                    param = defaultParam.copy(
                        showName = false,
                        itemSize = ItemSize.Small,
                        checkAffordable = true,
                        showColumn = false,
                        noFrameForItem = true,
                        numInFrame = false,
                        showReputationLevel = true,
                        textColor = Color.White,
                    ),
                )
            }
        }
        Spacer(modifier = Modifier.size(padding4))
        val (locked, toast) = TalentManager.getLockInfoByTalent(scroll)
        GameButton(text = if (talentLevel == 0) stringResource(id = R.string.building_star_up) else if (haveNextLevel) stringResource(
            id = R.string.building_star_up
        ) else stringResource(id = R.string.star_max),
            buttonStyle = ButtonStyle.Orange,
            enabled = scroll.level != scroll.levelLimit && AwardManager.isAffordable(award),
            buttonSize = ButtonSize.Big,
            locked = locked,
            onClick = {
                if (locked) {
                    toast.toast()
                } else if (!haveNextLevel) {
                    GameApp.instance.getWrapString(R.string.star_max).toast()
                } else if (!AwardManager.isAffordable(award)) {
                    GiftManager.onDiamondNotEnough()
                    GameApp.instance.getWrapString(R.string.talent_resource_not_enough).toast()
                } else {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AwardManager.isAffordable(award, true)
                        restartEffect(dialogEffectState, starUpEffect)
                        Dialogs.detailTalentDialog.value =
                            TalentManager.upgradeTalent(scroll)?.mainId
                    }
                }
            })
    }
}
