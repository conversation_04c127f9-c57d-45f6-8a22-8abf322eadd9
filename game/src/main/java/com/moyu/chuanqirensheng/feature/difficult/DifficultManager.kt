package com.moyu.chuanqirensheng.feature.difficult

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAX_DONE_DIFFICULT
import com.moyu.chuanqirensheng.sub.datastore.KEY_MAX_DONE_MAP
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELECTED_DIFFICULT
import com.moyu.chuanqirensheng.sub.datastore.KEY_SELECTED_MAP
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.core.model.Difficult
import com.moyu.core.model.GameMap



object DifficultManager {
    val difficulties = repo.gameCore.getDifficultPool()
    val maps = repo.gameCore.getMapPool()
    val selected = mutableStateOf(difficulties.first())
    val selectedMap = mutableStateOf(maps.first())
    val maxDoneDifficultLevel = Guarded(KEY_MAX_DONE_DIFFICULT)
    val maxDoneMapLevel = Guarded(KEY_MAX_DONE_MAP)
    fun init() {
        selected.value = difficulties.first { it.id == getIntFlowByKey(KEY_SELECTED_DIFFICULT, 1) }
        selectedMap.value = maps.first { it.id == getIntFlowByKey(KEY_SELECTED_MAP, 1) }
    }

    fun getSelected(): Difficult {
        return selected.value
    }

    fun getSelectedMap(): GameMap {
        return selectedMap.value
    }

    fun select(difficult: Difficult) {
        selected.value = difficult
        setIntValueByKey(KEY_SELECTED_DIFFICULT, difficult.id)
        val showMaps = getShowMaps()
        if (selectedMap.value !in showMaps) {
            selectedMap.value = showMaps.last()
            setIntValueByKey(KEY_SELECTED_MAP, selectedMap.value.id)
        }
    }

    fun selectMap(map: GameMap) {
        selectedMap.value = map
        setIntValueByKey(KEY_SELECTED_MAP, map.id)
    }

    fun pass() {
        if (selected.value.id > maxDoneDifficultLevel.value) {
            maxDoneDifficultLevel.value = selected.value.id
        }
        if (selectedMap.value.id > maxDoneMapLevel.value) {
            maxDoneMapLevel.value = selectedMap.value.id
        }
    }

    fun isDifficultLocked(level: Int): Boolean {
        return maxDoneDifficultLevel.value + 1 < level
    }

    fun getShowDifficulties(): List<Difficult> {
        return difficulties.filter { it.id <= maxDoneDifficultLevel.value + 2 }
    }

    fun isMapLocked(level: Int): Boolean {
        return maxDoneMapLevel.value + 1 < level
    }

    fun isMapCanLoot(level: Int): Boolean {
        return maxDoneMapLevel.value >= level
    }

    fun getShowMaps(): List<GameMap> {
        return maps.filter { it.id <= maxDoneMapLevel.value + 2 }
    }

    fun getIcon(difficult: Int): Int {
        return when (difficult) {
            1 -> R.drawable.difficult1
            2 -> R.drawable.difficult2
            3 -> R.drawable.difficult3
            4 -> R.drawable.difficult4
            5 -> R.drawable.difficult5
            else -> R.drawable.difficult1
        }
    }
}