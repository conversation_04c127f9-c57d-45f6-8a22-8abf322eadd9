package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding6

val worldBossTabs = mutableStateListOf<String>()

@Composable
fun WorldBossScreen() {
    LaunchedEffect(Unit) {
        WorldBossManager.init()
        
        // 初始化tab列表
        worldBossTabs.clear()
        worldBossTabs.add(GameApp.instance.getWrapString(R.string.world_boss_challenge))
        worldBossTabs.add(GameApp.instance.getWrapString(R.string.world_boss_rank_realtime))
        worldBossTabs.add(GameApp.instance.getWrapString(R.string.world_boss_rank_final))
        
        // 如果开服第8天且有多个服务器，添加跨服排行榜
        if (WorldBossManager.shouldShowCrossServerRank()) {
            worldBossTabs.add(GameApp.instance.getWrapString(R.string.world_boss_cross_rank_realtime))
            worldBossTabs.add(GameApp.instance.getWrapString(R.string.world_boss_cross_rank_final))
        }
    }
    
    val pagerState = rememberPagerState {
        worldBossTabs.size
    }
    
    GameBackground(
        title = stringResource(R.string.world_boss),
        bgMask = B65, 
        background = R.drawable.bg_force4
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> WorldBossChallengePage()
                    1 -> WorldBossRankPage(isRealtime = true, isCrossServer = false)
                    2 -> WorldBossRankPage(isRealtime = false, isCrossServer = false)
                    3 -> if (WorldBossManager.shouldShowCrossServerRank()) {
                        WorldBossRankPage(isRealtime = true, isCrossServer = true)
                    }
                    4 -> if (WorldBossManager.shouldShowCrossServerRank()) {
                        WorldBossRankPage(isRealtime = false, isCrossServer = true)
                    }
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding6.toPx()
                }, 
                pageState = pagerState, 
                titles = worldBossTabs
            )
        }
    }
}
