package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.screen.resource.CurrentPassPoint
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.Quest
import kotlinx.coroutines.delay

@Composable
fun WarPassQuestScreen() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val leftUpdateTime = remember {
        mutableStateOf(0L)
    }
    LaunchedEffect(refresh.intValue.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        QuestManager.init()
        // 完成的任务排前面，已领取的排最后
        QuestManager.warPassTasks.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                tasks.addAll(this)
            }
    }
    LaunchedEffect(refresh) {
        refreshNetTime()
        if (isNetTimeValid()) {
            while (true) {
                leftUpdateTime.value = millisToMidnight(getCurrentTime())
                if (leftUpdateTime.value <= 1000) {
                    delay(1000)
                    // 修改这个，上面的LauncherEffect会刷新任务
                    refresh.intValue += 1
                }
                delay(500)
            }
        }
    }
    Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding10))
        CurrentPassPoint(Modifier.align(Alignment.CenterHorizontally))
        Text(
            modifier = Modifier.align(Alignment.End)
                .padding(end = padding12),
            text = stringResource(R.string.task_refresh_left_time) + leftUpdateTime.value.millisToHoursMinutesSeconds(),
            style = MaterialTheme.typography.h3
        )
        LazyColumn(modifier = Modifier
            .fillMaxSize(), content = {
            items(tasks.size) { index ->
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding10)) {
                    SingleQuest(tasks[index]) {
                        refresh.intValue += 1
                    }
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
        })
    }
}