package com.moyu.chuanqirensheng.util

import android.os.SystemClock
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.TimeZone

/**
 * 日期格式字符串转换成时间戳
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
fun date2TimeStamp(timestampMill: Long, format: String = "yyyy-MM-dd HH:mm:ss"): String {
    try {
        val sdf = SimpleDateFormat(format)
        return sdf.format(Date(timestampMill))
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""
}

fun Long.millisToHoursMinutesSeconds(): String {
    val hours = this / 1000 / 60 / 60
    val minutes = this / 1000 / 60 % 60
    val seconds = this / 1000 % 60
    return "%02d:%02d:%02d".format(hours, minutes, seconds)
}

fun isSameDay(millis1: Long, millis2: Long): Boolean {
    val interval = millis1 - millis2
    return interval < 86400000 && interval > -86400000 && millis2Days(
        millis1
    ) == millis2Days(millis2)
}

fun millisToMidnight(now: Long): Long {
    val todayStartMillis = (now + 28800000L) % 86400000L  // 从当天00:00:00算起已经过的毫秒数
    return 86400000L - todayStartMillis  // 今天剩下的毫秒数到午夜
}

fun isYesterday(millis1: Long, millis2: Long): Boolean {
    val interval = millis2 - millis1
    return interval < 2 * 86400000 && interval > 0 && millis2Days(
        millis1
    ) == millis2Days(millis2) - 1
}

fun isAfterEightAM(now: Long): Boolean {
    val millisSinceMidnight = (now + 28800000L) % 86400000L  // 从今天的00:00算起已经过的毫秒数（东八区）
    val eightAMMillis = 8 * 60 * 60 * 1000  // 从00:00到08:00的毫秒数（8小时）
    return millisSinceMidnight > eightAMMillis  // 判断当前时间是否在8点之后
}


fun inSomeHours(millis1: Long, millis2: Long, hours: Int): Boolean {
    val interval = millis1 - millis2
    return (interval < 3600 * 1000 * hours) && (interval > -3600 * 1000 * hours)
}

fun millis2Days(millis: Long): Long {
    return (28800000L + millis) / 86400000
}

suspend fun refreshNetTime() {
    if (GameApp.instance.lastNetWorkTime.longValue == 0L) {
        RetrofitModel.getLoginData()
    }
}

fun isNetTimeValid(): Boolean {
    return if (BuildConfig.FLAVOR.contains("Lite")) true else GameApp.instance.lastNetWorkTime.longValue != 0L
}

fun getCurrentTime(): Long {
    return if (BuildConfig.FLAVOR.contains("Lite")) {
        System.currentTimeMillis()
    } else {
        SystemClock.elapsedRealtime() - GameApp.instance.elapsedDiffTime
    }
}

fun getCurrentDay(initTime: Long): Int {
    if (isNetTimeValid()) {
        return ((getCurrentTime() - initTime) / (1000 * 60 * 60 * 24)).toInt() + 1
    } else error("Net time is not valid")
}

fun getCurrentDay(): Int {
    return millis2Days(getCurrentTime()).toInt()
}


private fun millis2Weeks(millis: Long): Long {
    val daysFrom1970Thursday = (28800000L + millis) / 86400000
    return (daysFrom1970Thursday + 3) / 7
}

fun isSameWeek(millis1: Long, millis2: Long): Boolean {
    val interval = millis1 - millis2
    return interval < 86400000 * 7 && interval > -86400000 * 7 && millis2Weeks(
        millis1
    ) == millis2Weeks(millis2)
}

fun isBetween23_45And00_15(millis1: Long): Boolean {
    return !isSameDay(millis1, millis1 - 60 * 1000 * 15) || !isSameDay(
        millis1,
        millis1 + 60 * 1000 * 15
    )
}

fun timeLeft(now: Long, initTime: Long, keepDays: Int): Long {
    val targetTime = initTime + keepDays * 86400000L
    return targetTime - now
}

fun gapWeek(now: Long, initTime: Long): Int {
    val gap = now - initTime
    return (gap / 1000 / 60 / 60 / 24 / 7).toInt()
}

fun Long.toDayHourMinuteSecond(): String {
    val days = this / 1000 / 60 / 60 / 24
    return if (days > 0) {
        "$days" + GameApp.instance.getWrapString(R.string.time_left_day)
    } else {
        millisToHoursMinutesSeconds()
    }
}


fun getDaySinceDec24(): Int {
    val currentMillis = getCurrentTime()

    val calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"))
    calendar.set(Calendar.YEAR, 2025) // 设置为 2025 年
    calendar.set(Calendar.MONTH, Calendar.JANUARY) // 1 月（注意：Calendar.JANUARY = 0）
    calendar.set(Calendar.DAY_OF_MONTH, 28) // 2025 年除夕为1月28日
    calendar.set(Calendar.HOUR_OF_DAY, 0) // 0 时
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    val dec24StartMillis = calendar.timeInMillis

    // 计算当前时间距离 2024 年 12 月 24 日的天数
    val dayDifference = (currentMillis - dec24StartMillis) / (24 * 60 * 60 * 1000L)
    return dayDifference.toInt() + 1 // 返回第几天，需从 1 开始
}


// 获取纽约时间 2024 年 12 月 24 日 0 时的时间戳（毫秒）
fun getNYStartTimeMillis(): Long {
    val calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"))
    calendar.set(Calendar.YEAR, 2025) // 设置为 2025 年
    calendar.set(Calendar.MONTH, Calendar.JANUARY) // 1 月（注意：Calendar.JANUARY = 0）
    calendar.set(Calendar.DAY_OF_MONTH, 28) // 2025 年除夕为1月28日
    calendar.set(Calendar.HOUR_OF_DAY, 0) // 0 时
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)

    return calendar.timeInMillis
}
