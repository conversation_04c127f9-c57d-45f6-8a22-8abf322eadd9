package com.moyu.chuanqirensheng.util


import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import android.util.Base64
import byte2HexWithBlank
import com.moyu.chuanqirensheng.application.GameApp
import hex2Bytes
import java.io.UnsupportedEncodingException
import java.security.KeyFactory
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec


object AESUtil {
    /*   算法/模式/填充 */
    private const val CipherMode = "AES/ECB/PKCS5Padding"

    /*  创建密钥  */
    private fun createKey(password: String): SecretKeySpec {
        var data: ByteArray? = null
        val sb = StringBuffer(32)
        sb.append(password)
        while (sb.length < 32) {
            sb.append("0")
        }
        if (sb.length > 32) {
            sb.setLength(32)
        }
        try {
            data = sb.toString().toByteArray(charset("UTF-8"))
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        return SecretKeySpec(data, "AES")
    }

    /* 加密字节数据  */
    fun encrypt(content: ByteArray?, password: String): ByteArray? {
        try {
            val key = createKey(password)
            val cipher = Cipher.getInstance(CipherMode)
            cipher.init(Cipher.ENCRYPT_MODE, key)
            return cipher.doFinal(content)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /*加密(结果为16进制字符串)  */
    fun encrypt(content: String, password: String): String? {
        var data: ByteArray? = null
        try {
            data = content.toByteArray(charset("UTF-8"))
        } catch (e: Exception) {
            e.printStackTrace()
        }
        data = encrypt(data, password)
        return byte2HexWithBlank(data)
    }

    /*解密字节数组*/
    fun decrypt(content: ByteArray?, password: String): ByteArray? {
        try {
            val key = createKey(password)
            val cipher = Cipher.getInstance(CipherMode)
            cipher.init(Cipher.DECRYPT_MODE, key)
            return cipher.doFinal(content)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /*解密16进制的字符串为字符串  */
    fun decrypt(content: String, password: String): String? {
        var data: ByteArray? = null
        try {
            data = hex2Bytes(content)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        data = decrypt(data, password)
        if (data == null) return null
        var result: String? = null
        try {
            result = String(data, Charsets.UTF_8)
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        return result
    }

    val charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

    fun simpleEncryption(input: String, key: String): String {
        val realInput = "sos$input"
        return realInput.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val sum = charset.indexOf(char) + charset.indexOf(keyChar)
            charset[sum % charset.length]
        }.joinToString("")
    }

    fun simpleDecryption(encrypted: String, key: String): String {
        return encrypted.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val diff = charset.indexOf(char) - charset.indexOf(keyChar) + charset.length
            charset[diff % charset.length]
        }.joinToString("").substring(3)
    }

    fun simpleDecryptionInner(encrypted: String, key: String): String {
        return encrypted.mapIndexed { index, char ->
            val keyChar = key[index % key.length]
            val diff = charset.indexOf(char) - charset.indexOf(keyChar) + charset.length
            charset[diff % charset.length]
        }.joinToString("")
    }

    fun isShareCode(text: String, key: String): Boolean {
        return simpleDecryptionInner(text, key).startsWith("sos")
    }

    fun signData(data: String, privateKey: PrivateKey): ByteArray {
        val signature = Signature.getInstance("SHA256withRSA")
        signature.initSign(privateKey)
        signature.update(data.toByteArray())
        return signature.sign()
    }

    fun encodeToBase64(byteArray: ByteArray): String {
        return Base64.encodeToString(byteArray, Base64.NO_WRAP)
    }

    fun sha256AndBase64(str: String, privateKey: PrivateKey): String {
        val signedData = signData(str, privateKey)
        return encodeToBase64(signedData)
    }

    fun stringToPrivateKey(privateKeyString: String): PrivateKey {
        val keyBytes: ByteArray = Base64.decode(privateKeyString, Base64.NO_WRAP)
        val spec = PKCS8EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance("RSA")
        return keyFactory.generatePrivate(spec)
    }

    fun getPrivateKeyFromAssets(context: Context, fileName: String): PrivateKey {
        context.assets.open(fileName).use { inputStream ->
            val keyBytes = inputStream.readBytes()
            // 移除 PEM 文件的头部和尾部标记
            val pem = keyBytes.decodeToString()
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("\\s".toRegex(), "")

            val decoded = Base64.decode(pem, Base64.NO_WRAP)
            val spec = PKCS8EncodedKeySpec(decoded)
            val keyFactory = KeyFactory.getInstance("RSA")
            return keyFactory.generatePrivate(spec)
        }
    }

    fun sha256(base: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hash = digest.digest(base.toByteArray(charset("UTF-8")))
            val hexString = StringBuilder()
            for (b in hash) {
                val hex = Integer.toHexString(0xff and b.toInt())
                if (hex.length == 1) hexString.append('0')
                hexString.append(hex)
            }
            hexString.toString()
        } catch (e: java.lang.Exception) {
            throw RuntimeException(e)
        }
    }

    fun getSignature(): String {
        try {
            // 获取PackageManager的实例
            val pm: PackageManager = GameApp.instance.packageManager
            // 通过调用getPackageInfo()方法获得当前应用的PackageInfo对象
            // PackageManager.GET_SIGNATURES已在API 28中弃用，推荐使用GET_SIGNING_CERTIFICATES
            val packageInfo: PackageInfo =
                pm.getPackageInfo(GameApp.instance.packageName, PackageManager.GET_SIGNING_CERTIFICATES)
            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners?: emptyArray()
            } else {
                emptyArray()
            }
            // 循环遍历所有签名
            for (signature in signatures) {
                // 获取签名的哈希值
                val md = MessageDigest.getInstance("SHA")
                md.update(signature.toByteArray())
                val signatureHash = bytesToHex(md.digest())
                // 打印或发送这个哈希值到服务器进行校验
//                ("SignatureHash $signatureHash").toast()
                return signatureHash.toString()
            }
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return ""
    }

    // 辅助方法，用于将字节数组转换为十六进制字符串
    private fun bytesToHex(bytes: ByteArray): String? {
        val builder = java.lang.StringBuilder()
        for (b in bytes) {
            builder.append(String.format("%02x", b))
        }
        return builder.toString()
    }
}
