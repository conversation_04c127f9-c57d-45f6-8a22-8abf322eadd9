package com.moyu.chuanqirensheng.util

import android.content.pm.PackageManager
import androidx.core.content.pm.PackageInfoCompat
import com.moyu.chuanqirensheng.application.GameApp
import kotlin.math.pow
import kotlin.math.roundToLong

fun getVersion(): String {
    return try {
        GameApp.instance.packageManager.getPackageInfo(GameApp.instance.packageName, 0).versionName?: ""
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        ""
    }
}

fun getVersionCode(): Int {
    return try {
        val packageInfo = GameApp.instance.packageManager.getPackageInfo(GameApp.instance.packageName, 0)
        PackageInfoCompat.getLongVersionCode(packageInfo).toInt()
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        0
    }
}

fun getVersions(): String {
    return ((Math.PI + Math.E) * (10.0.pow(16))).roundToLong().toString()
}

fun getSimpleVersions(): String {
    return ((Math.PI * Math.E) * (2.0.pow(12))).roundToLong().toString()
}