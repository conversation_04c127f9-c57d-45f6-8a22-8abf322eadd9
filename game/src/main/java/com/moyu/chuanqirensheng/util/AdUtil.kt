package com.moyu.chuanqirensheng.util

object AdUtil {
    /**
     * 从网上返回的数据，用本地固定密钥解密，得到解密后数据
     */
    fun decodeText(text: String): String? {
        return AESUtil.decrypt(text, getVersions())
    }

    /**
     * 用本地密钥加密后，发到网上
     */
    fun encodeText(text: String): String? {
        return AESUtil.encrypt(text, getVersions())
    }

    fun simpleEncodeText(objectId: String): String {
        return AESUtil.simpleEncryption(objectId, getSimpleVersions())
    }

    fun simpleDecryptionText(objectId: String): String {
        return AESUtil.simpleDecryption(objectId, getSimpleVersions())
    }

    fun isShareCode(text: String): Boolean {
        return AESUtil.isShareCode(text, getSimpleVersions())
    }
}