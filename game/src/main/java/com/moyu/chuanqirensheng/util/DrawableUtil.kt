package com.moyu.chuanqirensheng.util

import com.moyu.chuanqirensheng.R
import timber.log.Timber
import kotlin.math.absoluteValue

fun getImageResourceDrawable(imgName: String): Int {
    return try {
        val field = R.drawable::class.java.getField(imgName)
        field.getInt(field.name)
    } catch (e: Exception) {
        Timber.e("getImageResourceDrawable" + e.message)
        R.drawable.element_1
    }
}

fun getRankNumber(index: Int): Triple<Int, Int?, Int?> {
    return when (index) {
        in 1..9 -> {
            Triple(getSingleRankNumber(index), null, null)
        }

        in 10..99 -> {
            Triple(getSingleRankNumber(index / 10), getSingleRankNumber(index % 10), null)
        }

        100 -> {
            Triple(getSingleRankNumber(1), getSingleRankNumber(0), getSingleRankNumber(0))
        }

        else -> {
            Triple(getSingleRankNumber(0), null, null)
        }
    }
}

fun getSingleRankNumber(index: Int): Int {
    return when (index) {
        1 -> R.drawable.number_1
        2 -> R.drawable.number_2
        3 -> R.drawable.number_3
        4 -> R.drawable.number_4
        5 -> R.drawable.number_5
        6 -> R.drawable.number_6
        7 -> R.drawable.number_7
        8 -> R.drawable.number_8
        9 -> R.drawable.number_9
        else -> R.drawable.number_0
    }
}


fun getDamageNumber(damage: Int): List<Int> {
    val damageNumberList = mutableListOf<Int>()

    // 添加负号
    if (damage >= 0) {
        damageNumberList.add(R.drawable.damage_num_minus)
    } else {
        damageNumberList.add(R.drawable.damage_num_plus)
    }

    // 将 damage 转换为字符串，然后逐个字符转换为对应的图片资源
    val damageString = damage.absoluteValue.toString()
    for (char in damageString) {
        val index = char.toString().toInt()
        damageNumberList.add(getSingleDamageNumber(index))
    }

    return damageNumberList
}

fun getHealNumber(heal: Int): List<Int> {
    val healNumberList = mutableListOf<Int>()

    // 添加正号
    if (heal >= 0) {
        healNumberList.add(R.drawable.damage_num_plus)
    } else {
        healNumberList.add(R.drawable.damage_num_minus)
    }

    // 将 heal 转换为字符串，然后逐个字符转换为对应的图片资源
    val healString = heal.absoluteValue.toString()
    for (char in healString) {
        val index = char.toString().toInt()
        healNumberList.add(getSingleDamageNumber(index))
    }

    return healNumberList
}


fun getSingleDamageNumber(index: Int): Int {
    return when (index) {
        1 -> R.drawable.damage_num_1
        2 -> R.drawable.damage_num_2
        3 -> R.drawable.damage_num_3
        4 -> R.drawable.damage_num_4
        5 -> R.drawable.damage_num_5
        6 -> R.drawable.damage_num_6
        7 -> R.drawable.damage_num_7
        8 -> R.drawable.damage_num_8
        9 -> R.drawable.damage_num_9
        else -> R.drawable.damage_num_0
    }
}