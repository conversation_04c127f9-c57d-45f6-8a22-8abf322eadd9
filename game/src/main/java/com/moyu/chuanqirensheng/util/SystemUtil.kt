package com.moyu.chuanqirensheng.util

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.view.View
import android.view.inputmethod.InputMethodManager
import com.moyu.chuanqirensheng.application.GameApp

fun hideKeyboard(activity: Activity) {
    val imm: InputMethodManager =
        activity.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    var view = activity.currentFocus
    if (view == null) {
        view = View(activity)
    }
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

fun triggerRebirth() {
    val packageManager: PackageManager = GameApp.instance.packageManager
    val intent: Intent? = packageManager.getLaunchIntentForPackage(GameApp.instance.packageName)
    val componentName: ComponentName? = intent?.component
    val mainIntent: Intent = Intent.makeRestartActivityTask(componentName)
    GameApp.instance.startActivity(mainIntent)
    Runtime.getRuntime().exit(0)
}


fun killSelf() {
    Runtime.getRuntime().exit(0)
}