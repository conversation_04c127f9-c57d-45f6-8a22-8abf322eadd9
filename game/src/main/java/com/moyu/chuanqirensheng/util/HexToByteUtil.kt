private val hexArray = "0123456789ABCDEF".toCharArray()

/**
 * byte数组转换成16进制字符串
 */
fun byte2HexWithBlank(bytes: ByteArray?): String? {
    if (bytes == null || bytes.isEmpty()) {
        return null
    }
    val hexChars = CharArray(bytes.size * 2)
    for (j in bytes.indices) {
        val v = (bytes[j].toInt() and 0xFF)

        hexChars[j * 2] = hexArray[v ushr 4]
        hexChars[j * 2 + 1] = hexArray[v and 0x0F]
    }
    return String(hexChars)
}

/**
 * 16进制字符串转换成byte数组
 */
fun hex2Bytes(hexString: String): ByteArray {
    val arrB = hexString.toByteArray()
    val iLen = arrB.size
    val arrOut = ByteArray(iLen / 2)
    var strTmp: String?
    var i = 0
    while (i < iLen) {
        strTmp = String(arrB, i, 2)
        arrOut[i / 2] = strTmp.toInt(16).toByte()
        i += 2
    }
    return arrOut
}