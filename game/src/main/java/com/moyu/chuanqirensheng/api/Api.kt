package com.moyu.chuanqirensheng.api

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitManager.httpClient
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.rank.RankData
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType

val serverUrl = GameApp.instance.resources.getString(R.string.serverUrl)

suspend fun uploadLikedData(data: String): CommonResult {
    return httpClient.post(serverUrl + "upload_liked_v2") {
        contentType(ContentType.Application.Json)
        setBody(data)
    }.body()
}

suspend fun getLoginData(data: String): CommonResult {
    return httpClient.get(serverUrl + "login_user_v2") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun getShareData(data: String): CommonResult {
    return httpClient.get(serverUrl + "share_info_v2") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun getPvpByScore(platform: String, pvpScore: Int): CommonResult {
    return httpClient.get(serverUrl + "rank_data_pvp_v2") {
        url {
            parameters.append("platform", platform)
            parameters.append("pvpScore", pvpScore.toString())
        }
    }.body()
}

suspend fun getRanks(platform: String, type: Int): CommonResult {
    return httpClient.get(serverUrl + "rank_data_v2") {
        url {
            parameters.append("platform", platform)
            parameters.append("type", type.toString())
        }
    }.body()
}

suspend fun postRankData(rankData: RankData, isPvp: Boolean = false) {
    return httpClient.post(serverUrl + if (isPvp) "rank_pvp_data_v2" else "rank_data_v2") {
        contentType(ContentType.Application.Json)
        setBody(rankData)
    }.body()
}

suspend fun getGameSave(data: String): CommonResult {
    return httpClient.get(serverUrl + "save_v2") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun postSave(data: String): CommonResult {
    return httpClient.post(serverUrl + "save_v2") {
        contentType(ContentType.Application.Json)
        setBody(data)
    }.body()
}

suspend fun tryUseGameSave(data: String): CommonResult {
    return httpClient.get(serverUrl + "try_use_save_v2") {
        url {
            parameters.append("data", data)
        }
    }.body()
}

suspend fun useShareCode(codes: String, data: String, versionCode: Int): String {
    return httpClient.get(serverUrl + "use_share_code_v2") {
        url {
            parameters.append("codes", codes)
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun getAwards(codes: String, data: String, versionCode: Int): String {
    return httpClient.get(serverUrl + "awards_v2") {
        url {
            parameters.append("codes", codes)
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun postTowerRankData(rankData: RankData) {
    return httpClient.post(serverUrl + "rank_tower_data_v3") {
        contentType(ContentType.Application.Json)
        setBody(rankData)
    }.body()
}