package com.moyu.chuanqirensheng.api

import android.os.SystemClock
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.award.AwardFromServer
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager
import com.moyu.chuanqirensheng.sub.bill.PayData
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.sub.loginsdk.LoginData
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.core.model.Award
import kotlinx.coroutines.delay
import timber.log.Timber


object RetrofitModel {
    suspend fun uploadLikedData(likedData: LikedData) {
        doEncodedApiNoRandomCheck(
            data = likedData,
            inputSerializer = LikedData.serializer(),
        ) {
            uploadLikedData(it)
        }
    }

    suspend fun useShareCode(
        userId: String, codes: String, encryptedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = useShareCode(codes, encryptedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward()
            } ?: Award(valid = false)
        } catch (e: Exception) {
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Award(valid = false)
        }
    }

    suspend fun getCodeAwards(
        userId: String, codes: String, encodedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = getAwards(codes, encodedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward().apply {
                    // 保存解锁信息
                    unlockList.map { id ->
                        UnlockManager.unlockCode(id)
                    }
                }
            } ?: Award(valid = false)
        } catch (e: Exception) {
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Award(valid = false)
        }
    }

    suspend fun getLoginData() {
        val result = doEncodedApi(
            data = GameApp.instance.getLoginUser(),
            inputSerializer = LoginUser.serializer(),
            outputSerializer = LoginData.serializer()
        ) {
            getLoginData(it)
        } ?: LoginData(0, verified = false, showDialog = true)

        GameApp.instance.lastNetWorkTime.longValue = result.time
        GameApp.instance.elapsedDiffTime = SystemClock.elapsedRealtime() - GameApp.instance.lastNetWorkTime.longValue
        ServerManager.setSavedServerId(result.serverData.serverId)

        if (result.needPostAntiCheat && AntiCheatManager.isPostCheating()) {
            GameApp.instance.loginData.value = result.copy(
                verified = false,
                showDialog = true,
                dialogText = GameApp.instance.getWrapString(R.string.login_error_tips)
            )
        } else {
            GameApp.instance.loginData.value = result
        }
        ReportManager.onLogin()
    }

    suspend fun getBill(
        userId: String, giftId: String, uuid: String
    ): CommonResult {
        var repeat = 5
        while (repeat-- > 0) {
            delay(1500)
            innerGetBill(userId, giftId, uuid).takeIf { it.succeeded }?.let { return it }
            delay(2500)
        }
        return innerGetBill(userId, giftId, uuid)
    }

    private suspend fun innerGetBill(userId: String, giftId: String, uuid: String): CommonResult {
        return try {
            AdUtil.encodeText(userId)?.let {
                val result = getBillInfo(it, giftId, uuid)
                if (result.succeeded) {
                    val codeAwardJson = AESUtil.decrypt(result.message, userId)
                    codeAwardJson?.let {
                        json.decodeFromString(PayData.serializer(), it).let { payData ->
                            if (payData.giftId.toString() == giftId && payData.userId == userId && payData.giftUUID == uuid) {
                                CommonResult(succeeded = true, message = "")
                            } else CommonResult(succeeded = false, message = "数据不匹配，请反馈")
                        }
                    } ?: CommonResult(succeeded = false, message = "服务端数据解析错误1，请反馈")
                } else {
                    result
                }
            } ?: CommonResult(succeeded = false, message = "数据加密异常，请反馈")
        } catch (e: Exception) {
            Timber.e(e)
            CommonResult(succeeded = false, message = "网络异常")
        }
    }
}