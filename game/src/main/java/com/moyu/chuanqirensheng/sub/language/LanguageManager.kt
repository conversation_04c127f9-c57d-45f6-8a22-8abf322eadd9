package com.moyu.chuanqirensheng.sub.language

import android.app.LocaleManager
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.LocaleList
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.core.os.LocaleListCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.KEY_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.dataStore
import com.moyu.chuanqirensheng.sub.datastore.dataStore2
import com.moyu.chuanqirensheng.sub.datastore.dataStore3
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.mapData2
import com.moyu.chuanqirensheng.sub.datastore.mapData3
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding34
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.AD_UNIT_ID_T1
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale

fun Map<String, String>.mapToCurrentLanguage(): String {
    return if (LanguageManager.selectedLanguage.value == "zh") {
        this["zh"] ?: this["zh"] ?: ""
    } else if (LanguageManager.selectedLanguage.value.startsWith("zh")) {
        this["zh-rTW"] ?: this["zh"] ?: ""
    } else {
        this[LanguageManager.selectedLanguage.value] ?: this["zh"] ?: ""
    }
}

object LanguageManager {
    val languages = arrayOf("English", "中文", "日本語", "한국어", "Deutsch", "Español", "Indonesia", "Português")
    val languageCodes = arrayOf("en", "zh-rTW", "ja", "ko", "de", "es", "id", "pt" )
    val selectedLanguage = mutableStateOf("")

    suspend fun init() {
        mapData.putAll(
            GameApp.instance.dataStore.data.first().asMap()
                .map { it.key.name to it.value.toString() })
        mapData2.putAll(GameApp.instance.dataStore2.data.first().asMap().map { it.key.name to it.value.toString() })
        mapData3.putAll(GameApp.instance.dataStore3.data.first().asMap().map { it.key.name to it.value.toString() })

        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
            if (getStringFlowByKey(KEY_LANGUAGE).isEmpty() || getStringFlowByKey(KEY_LANGUAGE) !in languageCodes) {
                val language = Locale.getDefault().language
                languageCodes.forEachIndexed { index, code ->
                    if (language.startsWith(code)) {
                        selectedLanguage.value = languageCodes[index]
                    } else if (language.startsWith("zh")) {
                        // 多种中文都用繁体
                        selectedLanguage.value = languageCodes[1]
                    }
                }
                if (selectedLanguage.value.isEmpty()) {
                    selectedLanguage.value = languageCodes[0]
                }
                setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
            } else {
                selectedLanguage.value = getStringFlowByKey(KEY_LANGUAGE)
            }
            setLocale(GameApp.instance, selectedLanguage.value)
        } else {
            selectedLanguage.value = languageCodes[1]
            setLocale(GameApp.instance, selectedLanguage.value)
        }
        if (getStringFlowByKey(KEY_INIT_LANGUAGE).isEmpty()) {
            // 初始语言记录，用于区分中文区还是英文区用户，对于抽卡有做两个模式
            setStringValueByKey(KEY_INIT_LANGUAGE, selectedLanguage.value)
        }
    }


    @Composable
    fun LanguageSelectorView() {
        val showLanguage = remember {
            mutableStateOf(false)
        }
        if (showLanguage.value) {
            languages.forEachIndexed { index, language ->
                val selected = languageCodes[index] == selectedLanguage.value
                GameButton(
                    text = language,
                    buttonSize = ButtonSize.MediumMinus,
                    buttonStyle = if (selected) ButtonStyle.Orange else ButtonStyle.Blue
                ) {
                    if (selectedLanguage.value != languageCodes[index]) {
                        selectedLanguage.value = languageCodes[index]
                        setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
                        updateLocale(GameApp.instance, languageCodes[index])
                    }
                    showLanguage.value = false
                }
            }
        } else {
            EffectButton(
                Modifier.padding(start = padding19, top = padding34, end = padding8),
                onClick = {
                    showLanguage.value = !showLanguage.value
                }) {
                Text(
                    text = stringResource(R.string.language_title),
                    style = MaterialTheme.typography.h1
                )
            }
        }
    }

    fun setLocale(context: Context, languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - 使用新的LocaleManager API
            try {
                val localeManager = context.getSystemService(LocaleManager::class.java)
                localeManager?.applicationLocales = LocaleList(locale)
            } catch (e: Exception) {
                // 如果LocaleManager不可用，回退到AppCompatDelegate
                setLocaleUsingAppCompat(languageCode)
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7+ (API 24+) - 使用AppCompatDelegate
            setLocaleUsingAppCompat(languageCode)
        } else {
            // 旧版本Android - 使用传统方法
            setLocaleUsingLegacyMethod(context, locale)
        }
    }

    private fun setLocaleUsingAppCompat(languageCode: String) {
        val localeList = LocaleListCompat.forLanguageTags(languageCode)
        AppCompatDelegate.setApplicationLocales(localeList)
    }

    @Suppress("DEPRECATION")
    private fun setLocaleUsingLegacyMethod(context: Context, locale: Locale) {
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
    }

    fun updateLocale(context: Context, languageCode: String) {
        GameApp.globalScope.launch {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ - 使用LocaleManager，系统会自动重启Activity
                try {
                    val localeManager = context.getSystemService(LocaleManager::class.java)
                    val locale = Locale(languageCode)
                    localeManager?.applicationLocales = LocaleList(locale)
                    // 不需要手动重启，系统会自动处理
                } catch (e: Exception) {
                    // 回退到AppCompatDelegate
                    updateLocaleUsingAppCompat(languageCode)
                }
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7+ - 使用AppCompatDelegate
                updateLocaleUsingAppCompat(languageCode)
            } else {
                // 旧版本 - 使用传统方法并手动重启
                setLocale(context, languageCode)
            }
            delay(1000)
            triggerRebirth()
        }
    }

    private fun updateLocaleUsingAppCompat(languageCode: String) {
        val localeList = LocaleListCompat.forLanguageTags(languageCode)
        AppCompatDelegate.setApplicationLocales(localeList)
        // AppCompatDelegate会自动重启Activity，不需要手动triggerRebirth
    }

    fun getLocalizedAssetFileName(fileName: String): String {
        val language = selectedLanguage.value
        languageCodes.forEach {
            if (language == it) {
                if (language == "zh-rTW") {
                    // 默认
                    return fileName
                }
                return it + "_" + fileName
            }
        }
        return fileName
    }

    fun getCountryAdId(): String {
        return AD_UNIT_ID_T1
    }

    fun isSelectedChinese() = selectedLanguage.value == languageCodes[1]

    fun getLine(): Int {
        return if (isSelectedChinese()) 1 else 2
    }

    fun canSoftWrap(): Boolean {
        return !isSelectedChinese()
    }
}