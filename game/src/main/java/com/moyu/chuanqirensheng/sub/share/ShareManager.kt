package com.moyu.chuanqirensheng.sub.share

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemory
import com.moyu.chuanqirensheng.sub.datastore.KEY_OTHER_USE_YOUR_CODE_GAINED_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_SHARE_AWARD_COUNT
import com.moyu.chuanqirensheng.sub.datastore.KEY_SHARE_AWARD_GAINED1
import com.moyu.chuanqirensheng.sub.datastore.KEY_SHARE_AWARD_GAINED2
import com.moyu.chuanqirensheng.sub.datastore.KEY_SHARE_TIME_STAMP
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Award
import kotlinx.coroutines.delay

object ShareManager {
    val shareCount = Guarded(KEY_SHARE_AWARD_COUNT)
    val shareGained1 = GuardedB(KEY_SHARE_AWARD_GAINED1) // 分享平台
    val shareGained2 = GuardedB(KEY_SHARE_AWARD_GAINED2) // 分享taptap
    val beingShareCount = GuardedMemory()
    val otherUseYourCodeAwardedCount = Guarded(KEY_OTHER_USE_YOUR_CODE_GAINED_COUNT)

    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_SHARE_TIME_STAMP), getCurrentTime())) {
                setLongValueByKey(KEY_SHARE_TIME_STAMP, getCurrentTime())
                shareGained1.value = false
                shareGained2.value = false
            }
        }
    }

    fun share1Gained(): Boolean {
        return shareGained1.value
    }

    fun share2Gained(): Boolean {
        return shareGained2.value
    }

    suspend fun gainShare1Award(key: Int) {
        if (!share1Gained()) {
            shareGained1.value = true
            delay(2000)
            Dialogs.awardDialog.value = Award(
                key = key,
            ).let {
                AwardManager.gainAward(it)
                it
            }
        }
    }

    suspend fun gainShare2Award(key: Int) {
        if (!share2Gained()) {
            shareGained2.value = true
            delay(2000)
            Dialogs.awardDialog.value = Award(
                key = key,
            ).let {
                AwardManager.gainAward(it)
                it
            }
        }
    }
}