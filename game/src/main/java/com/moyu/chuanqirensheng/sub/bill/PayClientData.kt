package com.moyu.chuanqirensheng.sub.bill

import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class PayClientData(
    val userId: String,
    val orderName: String,
    val totalMoneyInCent: Int,
    val prepayId: String = "",
    val tradeNo: String = "",
    val random: Int = 0,
    @Transient // 如果异常，不支持重新尝试，只能找客服解决，所以不需要序列化
    val award: () -> Unit = {}
)
