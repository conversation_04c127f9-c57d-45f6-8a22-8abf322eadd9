package com.moyu.chuanqirensheng.sub.saver

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

object CloudSaverManager {
    suspend fun init() {
        if (!GameApp.newUser) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                async {
                    delay(25 * 60 * 1000)
                    GameApp.instance.uploadCurrentSave().apply {
                        GameApp.instance.getWrapString(R.string.auto_sync_tips).toast()
                    }
                    delay(5 * 60 * 1000)
                }
            }
        }
    }

    fun checkIfNewUserHaveCloudSave() {
        GameApp.globalScope.launch(Dispatchers.IO) {
            delay(3000)
            GameApp.instance.getCloudSave()?.let {
                Dialogs.useSaveDialog.value = it
            }
        }
    }
}