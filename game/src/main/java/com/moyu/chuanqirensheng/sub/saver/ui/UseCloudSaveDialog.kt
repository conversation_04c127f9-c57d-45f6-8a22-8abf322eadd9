package com.moyu.chuanqirensheng.sub.saver.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.saver.GameSaver
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.util.date2TimeStamp
import kotlinx.coroutines.delay

@Composable
fun UseCloudSaverDialog(cloudSaverDialog: MutableState<GameSaver?>) {
    cloudSaverDialog.value?.let { cloudSave ->
        LaunchedEffect(Unit) {
            if (DebugManager.debug) {
                delay(5000)
            } else {
                delay(1000)
            }
            GameApp.instance.useThisCloudSave(cloudSave)
            cloudSaverDialog.value = null
        }

        PanelDialog(
            onDismissRequest = {
                if (DebugManager.debug) {
                    cloudSaverDialog.value = null
                }
            },
            contentBelow = {
                if (DebugManager.debug) {
                    GameButton(
                        text = stringResource(id = R.string.cancel),
                        buttonStyle = ButtonStyle.Orange,
                        onClick = {
                            cloudSaverDialog.value = null
                        })
                }
                GameButton(
                    text = stringResource(R.string.use),
                    onClick = { })
            }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(horizontal = padding26)
                    .padding(bottom = padding48)
            ) {
                Text(
                    text = stringResource(R.string.user_cloud_save_title),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding26))
                Column(
                    modifier = Modifier
                        .clip(RoundedCornerShape(padding4))
                        .background(B50)
                        .padding(padding10),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = stringResource(R.string.save_upload_time),
                        style = MaterialTheme.typography.h3
                    )
                    Text(
                        text = date2TimeStamp(cloudSave.timestamp),
                        style = MaterialTheme.typography.h3
                    )
                }
                Spacer(modifier = Modifier.size(padding10))
            }
        }
    }
}