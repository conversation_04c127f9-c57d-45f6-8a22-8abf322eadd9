package com.moyu.chuanqirensheng.sub.bill.pay

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding22
import kotlinx.coroutines.delay

@Composable
fun PayBlockDialog(
    switch: MutableState<String?>
) {
    if (switch.value != null) {
        val canCancel = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            delay(10000)
            canCancel.value = true
        }
        PanelDialog(
            onDismissRequest = { },
            showClose = false,
            contentBelow = {
                if (canCancel.value) {
                    GameButton(text = stringResource(id = R.string.cancel)) {
                        switch.value = null
                    }
                }
            }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize()
                    .padding(horizontal = padding22)
            ) {
                Spacer(modifier = Modifier.size(padding14))
                Text(
                    text = stringResource(R.string.order_confirming),
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding22))
                Text(
                    text = switch.value ?: "",
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
            }
        }
    }
}
