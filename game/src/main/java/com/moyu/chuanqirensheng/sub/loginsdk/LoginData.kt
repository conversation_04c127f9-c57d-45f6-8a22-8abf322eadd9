package com.moyu.chuanqirensheng.sub.loginsdk

import com.moyu.chuanqirensheng.api.CommonApiData
import kotlinx.serialization.Serializable

@Serializable
data class ServerData(
    val serverId: Int,
    val createTimeStamp: Long,
)


@Serializable
data class LoginData(
    val time: Long,
    val verified: Boolean,
    val showDialog: Boolean = false,
    val dialogText: String = "",
    val buttonText: String = "",
    val buttonLink: String = "",
    val messageId: Int = 1,
    override val randomInt: Int = 1,
    val top50Record: List<Int> = emptyList(),
    val needPostAntiCheat: Boolean = false,
    val otherUseMyShareCount: Int = 0,
    val customServiceTips: String = "QQ3159671927、微信T15760456854",
    val canShowAifadian: Boolean = true,
    val serverData: ServerData = ServerData(0, 0L), // 服务器ID，新增
    val serverList: List<ServerData> = emptyList(), // 可用服务器id列表，如果只有1个或者为空，则不提供切换服务器功能
): CommonApiData