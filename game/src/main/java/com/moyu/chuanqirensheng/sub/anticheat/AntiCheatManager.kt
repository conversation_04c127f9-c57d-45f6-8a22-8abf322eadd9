package com.moyu.chuanqirensheng.sub.anticheat

import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHEATING
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.config.ALLY_FILE_NAME
import com.moyu.core.config.DAY_REWARD_FILE_NAME
import com.moyu.core.config.GIFT_FILE_NAME
import com.moyu.core.config.SELL_FILE_NAME
import com.moyu.core.config.TALENT_FILE_NAME
import com.moyu.core.config.VIP_FILE_NAME
import com.moyu.core.config.WAR_PASS2_FILE_NAME
import com.moyu.core.config.WAR_PASS_FILE_NAME
import com.moyu.core.model.Ally
import com.moyu.core.model.BattlePass
import com.moyu.core.model.DrawAward
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.Talent
import com.moyu.core.model.Vip
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

object AntiCheatManager {
    fun init() {
        GameApp.globalScope.launch(Dispatchers.IO) {
            async {
                while (true) {
                    delay(30 * 1000)
                    checkPoolCheck()
                }
            }
        }
    }

    suspend fun isCheating(): Boolean {
        if (VipManager.isCheat()) {
            if (BuildConfig.FLAVOR.contains("Lite")) {
                "电力作弊".toast()
                delay(2000)
                return false
            } else {
                setBooleanValueByKey(KEY_CHEATING, true)
                return true
            }
        }
        return false
    }

    fun isPostCheating(): Boolean {
        return false
    }

    val antiConfigs = listOf(
        ALLY_FILE_NAME,
        VIP_FILE_NAME,
        TALENT_FILE_NAME,
        SELL_FILE_NAME,
        GIFT_FILE_NAME,
        DAY_REWARD_FILE_NAME,
        WAR_PASS_FILE_NAME,
        WAR_PASS2_FILE_NAME,
    )

    suspend fun checkPoolCheck() {
//        ConfigManager.configLoaders.filter { it.getKey() in antiConfigs }.forEach {
//            val list2 = it.reLoadConfig()
//            val list1 = repo.gameCore.getPoolByKeyAny(it.getKey())
//            list1.forEachIndexed { index, configData ->
//                if (configData != list2[index]) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkSell(sell: Sell) {
//        ConfigManager.configLoaders.first { it.getKey() == SELL_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Sell).id == sell.id }.let {
//                if (it != sell.copy(storage = (it as Sell).storage, award = null)) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkTalent(talent: Talent) {
//        ConfigManager.configLoaders.first { it.getKey() == TALENT_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Talent).id == talent.id }.let {
//                if (it != talent) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkQuest(task: Quest) {
//        ConfigManager.configLoaders.first { it.getKey() == TASK_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Quest).id == task.id }.let {
//                if (it != task.copy(opened = false, done = false, needRemoveCount = 0)) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkAlly(ally: Ally) {
//        ConfigManager.configLoaders.first { it.getKey() == ALLY_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Ally).id == ally.id }.let {
//                val configAlly = it as Ally
//                if (configAlly.star != ally.star || configAlly.starUpNum != ally.starUpNum) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkVip(vip: Vip) {
//        ConfigManager.configLoaders.first { it.getKey() == VIP_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Vip).id == vip.id }.let {
//                val configAlly = it as Vip
//                if (configAlly.level != vip.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkDrawAward(vip: DrawAward) {
//        ConfigManager.configLoaders.first { it.getKey() == DRAW_AWARD_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as DrawAward).id == vip.id }.let {
//                val configAlly = it as DrawAward
//                if (configAlly.level != vip.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkWarPass(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkWarPass2(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS2_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊,${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }
}