package com.moyu.chuanqirensheng.sub.ad

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

val isWatching = mutableStateOf(false)

const val KEY_BUY_AD_ITEM = "KEY_BUY_AD_ITEM"

object AdHolder : AdInterface {
    var playCount = 0
    var adInit = false

    override fun playAd(adId: String, callback: suspend () -> Unit) {
        if (isWatching.value) {
            GameApp.instance.getWrapString(R.string.greater_than_10_seconds).toast()
            return
        }
        isWatching.value = true
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (VipManager.isSkipAd()) {
                AwardManager.adNum.value += 1
                callback()
                isWatching.value = false
            } else {
                realShowAd(adId, callback)
                delay(10000)
                isWatching.value = false
            }
        }
    }

    private fun realShowAd(reportKey: String, callback: suspend () -> Unit) {
        TTAdPlayer.playAd(reportKey, callback)
        playCount += 1
    }
}