package com.moyu.chuanqirensheng.sub.bill.pay

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8

@Composable
fun ErrorOrderDialog(show: MutableState<Boolean>) {
    show.value.takeIf { it }?.let {
        PanelDialog(onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(padding8)
                    .verticalScroll(rememberScrollState())
            ) {
                BillingManager.payClientDataList.forEach {
                    Column(
                        Modifier
                            .padding(horizontal = padding10, vertical = padding6)
                            .clip(
                                RoundedCornerShape(padding4)
                            )
                            .background(B50)
                            .padding(horizontal = padding10, vertical = padding6)
                    ) {
                        val money = stringResource(
                            R.string.order_money,
                            it.totalMoneyInCent / 100
                        )
                        Row {
                            Text(text = it.orderName, style = MaterialTheme.typography.h5)
                            Spacer(modifier = Modifier.size(padding4))
                            Text(
                                text = money, style = MaterialTheme.typography.h5
                            )
                        }
                        val orderId = stringResource(R.string.order_id, it.tradeNo + it.userId.reversed())
                        Text(
                            text = orderId,
                            style = MaterialTheme.typography.h5
                        )
                        Text(
                            text = stringResource(R.string.error_order_tips, GameApp.instance.loginData.value.customServiceTips),
                            style = MaterialTheme.typography.h5
                        )
                        Spacer(modifier = Modifier.size(padding4))
                        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                            GameButton(
                                buttonSize = ButtonSize.MediumMinus,
                                text = stringResource(id = R.string.copy),
                                onClick = {
                                    val myClipboard =
                                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                    val myClip = ClipData.newPlainText("text", it.orderName + "," + money + "," + orderId)
                                    myClipboard.setPrimaryClip(myClip)
                                    GameApp.instance.getWrapString(R.string.copied).toast()
                                })
                            GameButton(
                                buttonSize = ButtonSize.MediumMinus,
                                text = stringResource(id = R.string.delete),
                                onClick = {
                                    BillingManager.removePayClientData(it)
                                    if (BillingManager.payClientDataList.isEmpty()) {
                                        show.value = false
                                    }
                                })
                        }
                    }
                    Spacer(modifier = Modifier.size(padding16))
                }
                Spacer(modifier = Modifier.size(padding16))
            }
        }
    }
}