package com.moyu.chuanqirensheng.sub.loginsdk.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4


@Composable
fun PermissionAlertDialog(
    switch: MutableState<Boolean>,
    confirm: () -> Unit,
) {
    if (switch.value) {
        LaunchedEffect(Unit) {
            // todo 不需要权限弹窗，则直接回调confirm
            if (!GameApp.instance.resources.getBoolean(
                    R.bool.need_privacy_check
                )
            ) {
                switch.value = false
                confirm()
            }
        }
        val clickedOk = remember {
            mutableStateOf(false)
        }
        PanelDialog(
            onDismissRequest = {
                if (clickedOk.value) {
                    confirm()
                    switch.value = false
                } else {
                    GameApp.instance.getWrapString(R.string.click_ok_confirm).toast()
                }
            },
            showClose = false,
            contentBelow = {
                GameButton(
                    text = stringResource(id = R.string.confirm),
                    buttonStyle = ButtonStyle.Orange
                ) {
                    clickedOk.value = true
                    switch.value = false
                    confirm()
                }
            }
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = stringResource(R.string.permission_content),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Column(
                    horizontalAlignment = Alignment.Start,
                ) {
                    Spacer(modifier = Modifier.size(padding36))
                    Text(
                        text = stringResource(R.string.permission_tips),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding10))
                    if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        Text(
                            text = stringResource(R.string.associated_boot),
                            style = MaterialTheme.typography.h4,
                            color = RaceNameColor
                        )
                        Spacer(modifier = Modifier.size(padding4))
                    }
                    Text(
                        text = stringResource(R.string.read_write_permission),
                        style = MaterialTheme.typography.h4,
                        color = RaceNameColor
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    Text(
                        text = stringResource(R.string.location_permission),
                        style = MaterialTheme.typography.h4,
                        color = RaceNameColor
                    )
                    Spacer(modifier = Modifier.size(padding4))
                    if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        Text(
                            text = stringResource(R.string.app_list_permission),
                            style = MaterialTheme.typography.h4,
                            color = RaceNameColor
                        )
                    }
                }
            }
        }
    }
}