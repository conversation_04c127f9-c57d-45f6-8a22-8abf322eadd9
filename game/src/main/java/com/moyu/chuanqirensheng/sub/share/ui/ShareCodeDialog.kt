package com.moyu.chuanqirensheng.sub.share.ui

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.doEncodedApi
import com.moyu.chuanqirensheng.api.getShareData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.loginsdk.LoginData
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.sub.share.ShareManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.core.model.Award
import com.moyu.core.tapShareCodeUrl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun ShareCodeDialog(show: MutableState<Boolean>) {
    if (show.value) {
        val maxUseOther = if (BuildConfig.FLAVOR.contains("Lite")) 4 else repo.gameCore.getMaxOtherUseYourCount()
        LaunchedEffect(Unit) {
            delay(200)
            doEncodedApi(GameApp.instance.getLoginUser(), LoginUser.serializer(), LoginData.serializer()) {
                getShareData(it)
            }?.let {
                ShareManager.beingShareCount.value = it.otherUseMyShareCount
            }
        }
        val keyNum = repo.gameCore.getShareCodeAwardKeyNum()
        val award = Award(key = keyNum)
        PanelDialog(onDismissRequest = {
            show.value = false
        },
            contentBelow = {
                GameButton(text = stringResource(id = R.string.go_share), onClick = {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", GameApp.instance.getShareCode())
                    myClipboard.setPrimaryClip(myClip)
                    GameApp.instance.getWrapString(R.string.code_copy_tips).toast()
                    if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                        val uri: Uri = Uri.parse(tapShareCodeUrl)
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        ContextCompat.startActivity(
                            GameApp.instance.activity,
                            intent,
                            Bundle()
                        )
                    }
                })
                val canAwardCount = ShareManager.beingShareCount.value - minOf(
                    maxUseOther,
                    ShareManager.otherUseYourCodeAwardedCount.value
                )
                if (canAwardCount > 0 && ShareManager.otherUseYourCodeAwardedCount.value < maxUseOther) {
                    GameButton(
                        text = stringResource(R.string.share_tips, canAwardCount),
                        onClick = {
                            ShareManager.otherUseYourCodeAwardedCount.value += 1
                            Dialogs.awardDialog.value = award
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                AwardManager.gainAward(award)
                            }
                        })
                }
            }) {
            Column(
                modifier = Modifier.fillMaxSize().padding(horizontal = padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.share_code),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    text = stringResource(id = R.string.share_award_tips),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                AwardList(
                    award = award,
                    mainAxisAlignment = Arrangement.spacedBy(padding16),
                    param = defaultParam.copy(itemSize = ItemSize.Large, textColor = Color.Black)
                )
                Row {
                    Text(
                        text = stringResource(id = R.string.share_code) + GameApp.instance.getShareCode(),
                        style = MaterialTheme.typography.h4,
                        color = Color.Black
                    )
                    Text(
                        text = stringResource(id = R.string.click_to_copy),
                        style = MaterialTheme.typography.h4.copy(textDecoration = TextDecoration.Underline),
                        modifier = Modifier.clickable {
                            val myClipboard =
                                GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val myClip =
                                ClipData.newPlainText("text", GameApp.instance.getShareCode())
                            myClipboard.setPrimaryClip(myClip)
                            (GameApp.instance.getShareCode() + GameApp.instance.getWrapString(R.string.copied)).toast()
                        },
                        color = Color.Blue
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                Text(
                    text = stringResource(
                        R.string.shared_count,
                        ShareManager.shareCount.value,
                        repo.gameCore.getMaxUseOtherCount()
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding16))
                Text(
                    text = stringResource(
                        R.string.accepted_count,
                        ShareManager.beingShareCount.value,
                        maxUseOther
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
            }
        }
    }
}