package com.moyu.chuanqirensheng.sub.saver

import byte2HexWithBlank
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.api.doEncodedApi
import com.moyu.chuanqirensheng.api.doEncodedApiNoRandomCheck
import com.moyu.chuanqirensheng.api.getGameSave
import com.moyu.chuanqirensheng.api.postSave
import com.moyu.chuanqirensheng.api.tryUseGameSave
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.sub.config.compressZip4j
import com.moyu.chuanqirensheng.sub.config.openText
import com.moyu.chuanqirensheng.sub.config.saveText
import com.moyu.chuanqirensheng.sub.config.uncompressZip4j
import com.moyu.chuanqirensheng.sub.datastore.clearDataStore1
import com.moyu.chuanqirensheng.sub.datastore.clearDataStore2
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.getVersions
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.DS_NAME
import com.moyu.core.util.RANDOM
import hex2Bytes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import timber.log.Timber
import java.io.File


val savePath =
    GameApp.instance.filesDir.absolutePath + File.separator + "datastore" + File.separator + DS_NAME + ".preferences_pb"

class CloudSaverDefault : CloudSaver {
    var switching = false

    override suspend fun switchServerId(serverId: Int) {
        if (switching) return
        val backUpServerId = ServerManager.getSavedServerId()
        switching = true
        if (uploadCurrentSave().succeeded) {
            ServerManager.setSavedServerId(serverId)
            getCloudSave(mute = true)?.let {
                // 这里是切到的目标服务器有存档，使用这个存档
                clearDataStore2()
                GameApp.instance.getWrapString(R.string.switch_server_ok_tips).toast()
                useThisCloudSave(it, mute = true)
            }?: run {
                // 这里是切到的目标服务器没有存档，新号重启
                clearDataStore1()
                clearDataStore2()
                GameApp.instance.getWrapString(R.string.switch_server_ok_tips).toast()
                delay(1000)
                triggerRebirth()
            }
        } else {
            GameApp.instance.getWrapString(R.string.error_sync3).toast()
        }
        // 走到这里就是切换失败了，回滚服务器id
        ServerManager.setSavedServerId(backUpServerId)
        switching = false
    }

    override suspend fun uploadCurrentSave(): CommonResult {
        GameApp.instance.getObjectId()?.let { objectId ->
            getCurrentSave()?.let { save ->
                val gameSaveData = GameSaver(
                    userId = objectId,
                    timestamp = getCurrentTime(),
                    userName = GameApp.instance.getUserName() ?: "none",
                    footPrint = getFootPrint(),
                    version = getVersionCode(),
                    save = save,
                    randomInt = RANDOM.nextInt(),
                    serverId = ServerManager.getSavedServerId()
                )
                return doEncodedApiNoRandomCheck(gameSaveData, GameSaver.serializer()) {
                    postSave(it)
                } ?: CommonResult(false, GameApp.instance.getWrapString(R.string.sync_error))
            } ?: GameApp.instance.getWrapString(R.string.pack_error).toast()
        }
        return CommonResult(false, GameApp.instance.getWrapString(R.string.error_sync2))
    }

    override suspend fun getCloudSave(mute: Boolean): GameSaver? {
        return try {
            doEncodedApi(
                GameApp.instance.getLoginUser(),
                LoginUser.serializer(),
                GameSaver.serializer(),
                mute = mute
            ) {
                getGameSave(it)
            }?.let { result ->
                if (result.valid) {
                    result
                } else {
                    result.message.toast()
                    null
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            if (!mute) {
                GameApp.instance.getWrapString(R.string.no_save).toast()
            }
            null
        }
    }

    override suspend fun useThisCloudSave(save: GameSaver, mute: Boolean) {
        withContext(Dispatchers.IO) {
            try {
                doEncodedApiNoRandomCheck(GameApp.instance.getLoginUser(), LoginUser.serializer()) {
                    tryUseGameSave(it)
                }?.let {
                    val targetFile =
                        File(GameApp.instance.cacheDir.absolutePath + File.separator + "tmp.save")
                    saveText(targetFile.absolutePath, hex2Bytes(save.save))
                    val uncompressTargetFile =
                        GameApp.instance.cacheDir.absolutePath + File.separator + "uncompressed.save"
                    GameApp.instance.getObjectId()?.let {
                        uncompressZip4j(
                            targetFile.absolutePath,
                            uncompressTargetFile,
                            it + getVersions()
                        )
                        File(uncompressTargetFile + File.separator + DS_NAME + ".preferences_pb").renameTo(
                            File(savePath)
                        )
                    } ?: GameApp.instance.getWrapString(R.string.id_error).toast()
                    if (!mute) {
                        GameApp.instance.getWrapString(R.string.sync_tips).toast()
                    }
                    // todo 隔离了未完成的游戏，所以任意时候读档，需要删除未完成的游戏
                    ContinueManager.clearSave()

                    delay(2000)
                    triggerRebirth()
                }?:  GameApp.instance.getWrapString(R.string.too_many_use_save_tips).toast()
            } catch (e: Exception) {
                Timber.e(e)
                (GameApp.instance.getWrapString(R.string.error_operate) + e.message).toast()
            }
        }
    }
}

suspend fun getCurrentSave(): String? {
    return withContext(Dispatchers.IO) {
        try {
            val zipFilePath =
                GameApp.instance.cacheDir.absolutePath + File.separator + "compressed.zip"
            GameApp.instance.getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: GameApp.instance.getWrapString(R.string.upload_exception).toast()
            byte2HexWithBlank(openText(zipFilePath))
        } catch (e: Exception) {
            val zipFilePath =
                GameApp.instance.cacheDir.absolutePath + File.separator + UUID.generateUUID()
                    .toString().take(16) + ".zip"
            GameApp.instance.getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: GameApp.instance.getWrapString(R.string.upload_exception).toast()
            byte2HexWithBlank(openText(zipFilePath))
        }
    }
}