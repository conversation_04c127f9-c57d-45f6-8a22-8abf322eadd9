package com.moyu.chuanqirensheng.sub.review.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.ClickableStars
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4

@Composable
fun GameReviewDialog(info: MutableState<Boolean>) {
    if (info.value) {
        PanelDialog(
            onDismissRequest = { info.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding4),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.rank_star_title),
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding19))
                Text(
                    text = stringResource(R.string.review_text),
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                ClickableStars {
                    GameReviewManager.doRankStar(it)
                }
            }
        }
    }
}