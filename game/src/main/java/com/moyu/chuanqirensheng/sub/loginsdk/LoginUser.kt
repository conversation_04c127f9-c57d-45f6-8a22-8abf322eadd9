package com.moyu.chuanqirensheng.sub.loginsdk

import com.moyu.chuanqirensheng.api.CommonApiData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LoginUser(
    @SerialName("v")
    val versionCode: Int,
    @SerialName("ui")
    val userId: String,
    @SerialName("un")
    val userName: String,
    @SerialName("f")
    val footPrint: String = "",
    @SerialName("c")
    val isCheating: Boolean,
    @SerialName("p")
    val platformChannel: String,
    @SerialName("b")
    val buildFlavor: String = "",
    @SerialName("s")
    val signature: String = "",
    @SerialName("r")
    override val randomInt: Int,
    val serverId: Int = 0, // 服务器ID，新增
) : CommonApiData
