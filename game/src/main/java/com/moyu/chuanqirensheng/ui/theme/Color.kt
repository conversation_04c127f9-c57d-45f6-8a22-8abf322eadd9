package com.moyu.chuanqirensheng.ui.theme

import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Ally
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill

val B85 = Color(0xb9000000)
val B75 = Color(0xa9000000)
val B65 = Color(0xa4000000)
val B50 = Color(0x80000000)
val B35 = Color(0x50000000)
val B04 = Color(0x0a000000)
val B15 = Color(0x2A000000)
val W10 = Color(0x25FFFFFF)
val W30 = Color(0x55FFFFFF)
val W50 = Color(0x80FFFFFF)
val Red50 = Color(0x80EE0000)
val DARK_RED = Color(0xffEE0000)
val HealTextColor = Color(0xFF009900)
val DarkGreen = Color(0xFF009900)
val TcgNameTextColor = Color(0xFFFBF6CD)
val RaceNameColor = Color(0xFF812EFF)
val SkillLevel1Color = Color.White
val SkillLevel2Color = Color(0xFF009900)
val SkillLevel3Color = Color(0xFF00ffff)
val SkillLevel4Color = Color(0xFFA18EFF)
val SkillLevel5Color = Color(0xFFCC9900)
val SkillLevel6Color = Color(0xffFB8759)
val PageTitleColor = Color(0xFFFBF6CD)
val BackgroundTop = Color(0XFF1a0f11)
val BackgroundBottom = Color(0XFF27110b)
val SkillStoryColor = Color(0xffFB8759)

val SliderColor = Color(0xFFEAE3FF)
val SliderTrackColor = Color(0xFF000000)

val LogoMaskEnd = Color(0X70514458)
val NormalDamageShieldColor = Color(0xFF009900)
val AllDamageShieldColor = Color(0xffdaa520)
val Cyan60 = Color(0x7700ffff)
val Yellow60 = Color(0x88dfdf22)
val White80 = Color(0xaaffffff)
val EquipBlue = Color(0XFF03A9F4)

val qualityColor1 = Color(0xff35a645) // 暗绿，奖励感，低饱和
val qualityColor2 = Color(0xff4a8bbe) // 暗蓝，选择感，低饱和
val qualityColor3 = Color(0xffc46528) // 暗橙，史诗感，低饱和
val qualityColor4 = Color(0xffae2e2e) // 暗红，紧张感，低饱和
val qualityColorPurple = Color(0xff573a9c) // 暗紫，战略感，低饱和
val qualityColorYellow = Color(0xfffcb90b) // 暗黄，随机感，低饱和

fun Int.toQualityColor(): Color {
    return when (this) {
        1, 2, 3 -> NormalDamageShieldColor
        4, 5, 6 -> EquipBlue
        else -> SkillStoryColor
    }
}

fun Ally.getColor(): Color {
    return quality.toQualityColor()
}

fun Skill.getColor() : Color {
    return repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.quality?.toQualityColor() ?: Color.White
}

fun DamageType.getTextColor(): Color {
    return when (this) {
        DamageType.DamageType1 -> Color.Red
        DamageType.DamageType2 -> SkillLevel2Color
        DamageType.DamageType3 -> Color.Yellow
        DamageType.DamageType4 -> SkillLevel6Color
        DamageType.DamageType5 -> Color.White
        else -> Color.White
    }
}