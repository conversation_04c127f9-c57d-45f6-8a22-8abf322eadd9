package com.moyu.chuanqirensheng.ui.theme

import com.moyu.chuanqirensheng.util.composeDp

val logoHeight = 220.composeDp()
val dialogHeight = 486.composeDp()
val dialogWidth = 400.composeDp()

val imageTinyPlus = 16.composeDp()
val imageSmall = 20.composeDp()
val imageSmallPlus = 28.composeDp()
val imageSmallPlusFrame = 30.composeDp()
val imageMediumMinus = 34.composeDp()
val imageMedium = 36.composeDp()
val imageLarge = 44.composeDp()
val imageLargeFrame = 52.composeDp()
val imageLargePlus = 58.composeDp()
val imageHugeLite = 68.composeDp()
val imageHugeLiteFrame = 72.composeDp()
val imageHugeFrame = 94.composeDp()

val moneyWidth = 101.composeDp()
val moneyHeight = 32.composeDp()

val moneyShortWidth = 90.composeDp()
val moneyShortHeight = 30.composeDp()
// 战斗相关
val singleRoleWidth = 100.composeDp()
val singleRoleHeight = 132.composeDp()
val oneRoleWidth = 100.composeDp()
val buffSize = 16.composeDp()

val gapSmall = 36.composeDp()
val gapSmallPlus = 46.composeDp()
val gapMedium = 60.composeDp()
val gapLarge = 80.composeDp()
val gapHugeMinus = 98.composeDp()
val gapHuge = 110.composeDp()
val gapHugePlus = 130.composeDp()
val gapHugePlusPlus = 230.composeDp()

val padding0 = 0.composeDp()
val padding1 = 1.composeDp()
val padding2 = 2.composeDp()
val padding3 = 3.composeDp()
val padding4 = 4.composeDp()
val padding5 = 5.composeDp()
val padding6 = 6.composeDp()
val padding7 = 7.composeDp()
val padding8 = 8.composeDp()
val padding10 = 10.composeDp()
val padding12 = 12.composeDp()
val padding14 = 14.composeDp()
val padding16 = 16.composeDp()
val padding19 = 19.composeDp()
val padding22 = 22.composeDp()
val padding26 = 26.composeDp()
val padding28 = 28.composeDp()
val padding30 = 30.composeDp()
val padding34 = 34.composeDp()
val padding36 = 36.composeDp()
val padding40 = 40.composeDp()
val padding42 = 42.composeDp()
val padding44 = 44.composeDp()
val padding45 = 45.composeDp()
val padding48 = 48.composeDp()
val padding51 = 51.composeDp()
val padding54 = 54.composeDp()
val padding57 = 57.composeDp()
val padding60 = 60.composeDp()
val padding64 = 64.composeDp()
val padding66 = 66.composeDp()
val padding69 = 69.composeDp()
val padding72 = 72.composeDp()
val padding80 = 80.composeDp()
val padding84 = 84.composeDp()
val padding90 = 90.composeDp()
val padding96 = 96.composeDp()
val padding100 = 100.composeDp()
val padding110 = 110.composeDp()
val padding120 = 120.composeDp()
val padding130 = 130.composeDp()
val padding138 = 138.composeDp()
val padding150 = 150.composeDp()
val padding165 = 165.composeDp()
val padding170 = 170.composeDp()
val padding180 = 180.composeDp()
val padding200 = 200.composeDp()
val padding212 = 212.composeDp()
val padding220 = 220.composeDp()
val padding226 = 226.composeDp()
val padding260 = 260.composeDp()
val padding280 = 280.composeDp()
val padding300 = 300.composeDp()
val padding360 = 360.composeDp()
val padding380 = 380.composeDp()
val padding400 = 400.composeDp()
val padding420 = 420.composeDp()
val padding500 = 500.composeDp()
val padding520 = 520.composeDp()
val padding620 = 620.composeDp()
val padding780 = 780.composeDp()

val hugeButtonWidth = 182.composeDp()
val hugeButtonHeight = 70.composeDp()
val bigButtonWidth = 158.composeDp()
val bigButtonHeight = 62.composeDp()
val bottomItemSize = 84.composeDp()
val buttonMinusWidth = 92.composeDp()
val buttonMinusHeight = 42.composeDp()
val buttonWidth = 118.composeDp()
val buttonHeight = 50.composeDp()
val smallButtonWidth = 58.composeDp()
val smallButtonHeight = 32.composeDp()
val tutorTitleWidth = 88.composeDp()
val tutorTitleHeight = 36.composeDp()

val eventAwardWidth = 122.composeDp()
val eventAwardHeight = 172.composeDp()

val tabButtonHeight = 41.composeDp()
val tabButtonWidth = 80.composeDp()

val animateSmall = 12.composeDp()
val animateLarge = 24.composeDp()

val detailBigHeight = 580.composeDp()
val expBarHeight = 18.composeDp()
val roleInfoHeight = 88.composeDp()

val cardWidth = 90.composeDp()
val cardHeight = 106.composeDp()
val shakeDp = 6.composeDp()

val noticeHeight = 100.composeDp()

val backIconHeight = 42.composeDp()
val titleHeight = 56.composeDp()

val roleEffectWidth = 220.composeDp()
val roleEffectHeight = 300.composeDp()
val upgradeEffectWidth = 400.composeDp()


val codeInputWidth = 210.composeDp()
val codeInputHeight = 40.composeDp()

val chartWidth = 220.composeDp()
val chartHeight = 140.composeDp()

val textFieldHeight = 40.composeDp()

val cardStarSize = 13.composeDp()
val cardStarBigSize = 20.composeDp()

val hpWidth = 68.composeDp()
val hpHeight = 12.composeDp()

val filterWidth = 92.composeDp()
val filterHeight = 32.composeDp()

val propertyBigWidth = 96.composeDp()
val propertyBigHeight = 22.composeDp()


val advPropertyBigWidth = 42.composeDp()
val advPropertyBigHeight = 100.composeDp()

val userHeadWidth = 198.composeDp()
val userHeadHeight = 86.composeDp()

val turnEffectWidth = 120.composeDp()

val propertyBigImageSize = 20.composeDp()

val eventCardWidth = 176.composeDp()
val eventCardHeight = 164.composeDp()
val eventCardBigWidth = 226.composeDp()
val eventCardBigHeight = 280.composeDp()

val shopItemWidth = 118.composeDp()

val slideWidth = 180.composeDp()
val slideHeight = 36.composeDp()

val rankIndexOneDigitWidth = 12.composeDp()
val rankIndexOneDigitHeight = 20.composeDp()
val pvpRecordFrameHeight = 120.composeDp()

val dialogFrameHeight = 140.composeDp()

val answerWidth = 400.composeDp()
val answerHeight = 80.composeDp()

val eventTopLayoutHeight = 160.composeDp()

val labelWidth = 300.composeDp()
val labelHeight = 106.composeDp()

val settingSize = 52.composeDp()

val cheatBarWidth = 188.composeDp()
val cheatBarHeight = 22.composeDp()

val cheatFrameHeight = 158.composeDp()
val cheatFrameWidth = 136.composeDp()
val cheatDecHeight = 322.composeDp()

val reputationItemHeight = 118.composeDp()

val createCountryLabelHeight = 168.composeDp()
val createCountryPanelHeight = 310.composeDp()
val selectDifficultPanelHeight = 130.composeDp()
