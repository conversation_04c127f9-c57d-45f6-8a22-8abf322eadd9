package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.buglyId
import com.tencent.bugly.crashreport.CrashReport

/**
 * Bugly初始化，用来上报crash
 */
class BuglyTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            CrashReport.initCrashReport(context.applicationContext, buglyId, DebugManager.debug)
        }
    }
}