
package com.moyu.chuanqirensheng.sub.bill


import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.core.model.Sell


const val SHARE_WECHAT_FRIEND = 1
const val SHARE_WECHAT_CIRCLE = 2

object BillingManager {
    val payClientDataList = mutableStateListOf<PayClientData>()
    //initiate purchase on consume button click
    fun consume(productId: String) {

    }

    fun queryAsync() {

    }

    suspend fun prepay(sell: Sell, award: () -> Unit) {

    }

    private fun checkIfCanAward(payClientData: PayClientData) {
        if (getBooleanFlowByKey(payClientData.tradeNo.reversed().replace("-", ""))) {
            return
        } else {
            setBooleanValueByKey(payClientData.tradeNo.reversed().replace("-", ""), true)
            payClientData.award()
        }
    }

    fun removePayClientData(payData: PayClientData) {
    }

}