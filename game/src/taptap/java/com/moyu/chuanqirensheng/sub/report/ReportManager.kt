package com.moyu.chuanqirensheng.sub.report

import com.moyu.chuanqirensheng.application.GameApp
import com.xingma.sdk.XmSdk

object ReportManager {

    fun init() {
    }

    fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {}

    fun onLogin() {
    }

    fun pk(i: Int, value: Int) {

    }

    fun battle(i: Int, i1: Int, age: Int) {

    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {

    }

    fun onNewGame(i: Int) {

    }

    fun onContinueGame(i: Int) {

    }

    fun onPage(route: String) {
    }

    fun onTalentUpgrade(level: Int) {
        report("${level}级")
    }

    fun onDungeonProgress(day: Int) {
        report("${day}天")
    }

    private fun report(action: String) {
        
    }
}