package com.moyu.chuanqirensheng.sub.share.ui

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.feature.ending.Ending

@Composable
fun ShareEndingLayout(ending: Ending) {
//    EffectButton(
//        onClick = {
//            BillingManager.shareToWechat(
//                GameApp.instance.getWrapString(R.string.app_name),
//                ending.shareTitle + "\n" + ending.endingText,
//                SHARE_WECHAT_FRIEND
//            )
//            GameApp.globalScope.launch(Dispatchers.Main) {
//                gainShare1Award(repo.gameCore.getImageShareAwardNum())
//            }
//        }) {
//        Column(
//            modifier = Modifier
//                .clip(RoundedCornerShape(padding4))
//                .background(W10).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally
//        ) {
//            Row(verticalAlignment = Alignment.CenterVertically) {
//                Image(
//                    modifier = Modifier
//                        .size(imageMedium)
//                        .scale(1.2f),
//                    painter = painterResource(id = R.drawable.icon_share_wechat),
//                    contentDescription = null
//                )
//                Spacer(modifier = Modifier.size(padding6))
//                Text(
//                    text = stringResource(R.string.share_target2),
//                    style = MaterialTheme.typography.h4
//                )
//            }
//            if (!share1Gained()) {
//                Spacer(modifier = Modifier.size(padding4))
//                AwardList(
//                    award = Award(key = repo.gameCore.getImageShareAwardNum()),
//                    param = defaultParam.copy(
//                        showName = false,
//                        noFrameForItem = true,
//                        numInFrame = false
//                    ),
//                    paddingHorizontalInDp = padding0,
//                    paddingVerticalInDp = padding0
//                )
//                Spacer(modifier = Modifier.size(padding4))
//                Text(
//                    text = stringResource(R.string.share_platform_tips),
//                    style = MaterialTheme.typography.h6
//                )
//            }
//        }
//    }
//
//    EffectButton(onClick = {
//        BillingManager.shareToWechat(
//            GameApp.instance.getWrapString(R.string.app_name),
//            ending.shareTitle + "\n" + ending.endingText,
//            SHARE_WECHAT_CIRCLE
//        )
//        GameApp.globalScope.launch(Dispatchers.Main) {
//            gainShare2Award(repo.gameCore.getImageShareAwardNum())
//        }
//    }) {
//        Column(
//            modifier = Modifier
//                .clip(RoundedCornerShape(padding4))
//                .background(W10).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally
//        ) {
//
//            Row(verticalAlignment = Alignment.CenterVertically) {
//                Image(
//                    modifier = Modifier
//                        .size(imageMedium)
//                        .scale(1.2f),
//                    painter = painterResource(id = R.drawable.icon_share_circle),
//                    contentDescription = null
//                )
//                Spacer(modifier = Modifier.size(padding6))
//                Text(
//                    text = stringResource(R.string.share_target3),
//                    style = MaterialTheme.typography.h4
//                )
//            }
//            if (!ShareManager.share2Gained()) {
//                Spacer(modifier = Modifier.size(padding4))
//                AwardList(
//                    award = Award(key = repo.gameCore.getImageShareAwardNum()),
//                    param = defaultParam.copy(
//                        showName = false,
//                        noFrameForItem = true,
//                        numInFrame = false
//                    ),
//                    paddingHorizontalInDp = padding0,
//                    paddingVerticalInDp = padding0
//                )
//                Spacer(modifier = Modifier.size(padding4))
//                Text(
//                    text = stringResource(R.string.share_platform_tips),
//                    style = MaterialTheme.typography.h6
//                )
//            }
//        }
//    }
}
