package com.moyu.chuanqirensheng.sub.bill

import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.api.RetrofitManager
import com.moyu.chuanqirensheng.api.serverUrl
import io.ktor.client.call.body
import io.ktor.client.request.get


suspend fun getPrepay(
    data: String, versionCode: Int
): CommonResult {
    return RetrofitManager.httpClient.get(serverUrl + "try_use_rank") {
        url {
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun checkPayOk(orderEncrypted: String, versionCode: Int): CommonResult {
    return RetrofitManager.httpClient.get(serverUrl + "try_check_p") {
        url {
            parameters.append("data", orderEncrypted)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}