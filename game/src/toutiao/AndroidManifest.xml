<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 配置APP KEY -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <queries>
        <package android:name="com.tencent.mm" />
    </queries>
    <application
        android:networkSecurityConfig="@xml/network_security_config"
        android:extractNativeLibs="true"
        android:usesCleartextTraffic="true"/>
</manifest>