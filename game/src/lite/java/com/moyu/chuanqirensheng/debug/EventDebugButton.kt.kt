package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.sell.Award
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isHeroSkill
import com.moyu.core.model.skill.isTalentSkill
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventIdTag(modifier: Modifier, id: Int, cardSize: CardSize) {
    Column(modifier = modifier) {
        StrokedText(
            text = id.toString(), style = cardSize.getTextStyle()
        )
    }
}

@Composable
fun EventDebugButton() {
    val show = remember {
        mutableStateOf(false)
    }
    val showBinFu = remember {
        mutableStateOf(false)
    }
    val showZhanJiShu = remember {
        mutableStateOf(false)
    }
    val showEquip = remember {
        mutableStateOf(false)
    }
    FlowRow(
        modifier = Modifier
            .background(W50)
            .verticalScroll(rememberScrollState()),
        overflow = FlowRowOverflow.Visible,
    ) {
        val text = remember {
            mutableStateOf("")
        }
        GameButton(buttonSize = ButtonSize.Small, text = "局内调试") {
            show.value = !show.value
        }
        if (show.value) {
            GameButton(buttonSize = ButtonSize.Small, text = "资源10w") {
                GameApp.globalScope.launch {
                    AwardManager.gainAward(Award(elements = listOf(100000, 100000, 100000, 100000)))
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "产量1w") {
                GameApp.globalScope.launch {
                    repeat(4) {
                        BattleManager.gainExtraElement(it, 10000)
                    }
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "所有盟友掉血10%") {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BattleManager.getGameAllies().forEach {
                        BattleManager.hurtAllyInGame(it, 10)
                    }
                }
            }
            GameButton(
                text = "年龄+1",
                buttonSize = ButtonSize.Small,
            ) {
                EventManager.gotoNextEvent(EventManager.selectedEvent.value, true)
            }
            GameButton(
                text = "年龄+5",
                buttonSize = ButtonSize.Small,
            ) {
                EventManager.gotoNextEvent(EventManager.selectedEvent.value, true, 5)
            }
            GameButton(
                text = "年龄+50",
                buttonSize = ButtonSize.Small,
            ) {
                EventManager.gotoNextEvent(EventManager.selectedEvent.value, true, 50)
            }
            GameButton(buttonSize = ButtonSize.Small, text = "建设度10") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            science = 10,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "探索度10") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            politics = 10,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "繁荣度10") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            military = 10,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "气温10") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            population = 10,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "气温-10") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            population = -10,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "国家属性+100") {
                GameApp.globalScope.launch {
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            politics = 100,
                            military = 100,
                            science = 100,
                        )
                    )
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "冒险技能") {
                Dialogs.debugAdvSkillDialog.value = {
                    GameApp.globalScope.launch {
                        AwardManager.gainAward(Award(skills = listOf(it)))
                    }
                }
            }
            (1..3).forEach { star ->
                GameButton(buttonSize = ButtonSize.Small, text = "${star}阶盟友") {
                    Award(
                        allies = repo.gameCore.getAllyPool()
                            .filter { it.star == 1 && it.quality == star }).let {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainAward(award = it)
                        }
                    }
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "猎手*1000") {
                Award(allies = listOf(repo.gameCore.getAllyPool().first().copy(num = 1000))).let {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award = it)
                    }
                }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "显示冒险") {
                showBinFu.value = !showBinFu.value
            }
            if (showBinFu.value) {
                DecorateTextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(textFieldHeight),
                    text.value
                ) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isAdventure() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Red,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "显示天赋") {
                showZhanJiShu.value = !showZhanJiShu.value
            }
            if (showZhanJiShu.value) {
                DecorateTextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(textFieldHeight),
                    text.value
                ) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isTalentSkill() && it.level == 1 }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Red,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(buttonSize = ButtonSize.Small, text = "显示神器") {
                showEquip.value = !showEquip.value
            }
            if (showEquip.value) {
                DecorateTextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(textFieldHeight),
                    text.value
                ) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isHeroSkill() && it.level == 1 }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleHeroView(hero = it)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Red,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainHeroInGame(it)
                                    }
                                })
                        }
                    }
            }
        }
    }
}