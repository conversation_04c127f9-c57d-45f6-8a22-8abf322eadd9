package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Checkbox
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.chuanqirensheng.ui.theme.slideWidth
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.ALLY_ROW2_FOURTH
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.logic.role.ENEMY_ROW2_FIRST
import com.moyu.core.logic.role.positionList
import com.moyu.core.model.Award
import com.moyu.core.model.getRaceGroupName
import com.moyu.core.model.getRaceTypeName
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val allRaceIds = (1..5).toList()
val allGroupsIds = (1..11).toList()

@Composable
fun DebugScreen() {
    BackPressHandler {
        DebugManager.dryTest = false
        DebugManager.debugBattle = false
        goto(LOGIN_SCREEN)
    }
    val enemySpeciesDialog = remember {
        mutableStateOf(false)
    }
    val enemyGroupDialog = remember {
        mutableStateOf(false)
    }
    val enemySpecies = remember {
        mutableStateOf(1)
    }
    val enemyGroup = remember {
        mutableStateOf(1)
    }
    val enemySkills = remember {
        mutableListOf<Skill>()
    }
    val enemyMap = remember {
        mutableStateListOf(ALLY_ROW1_FIRST, ENEMY_ROW1_FIRST)
    }
    val enemyNumDialog = remember {
        mutableStateOf(false)
    }
    val playerSkills = remember {
        mutableListOf<Skill>()
    }
    GameBackground(title = "调试页") {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(top = padding8)
                .verticalScroll(rememberScrollState())
        ) {
            Row {
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(padding8)
                ) {
                    DebugItem(
                        DebugManager.easySkill, "技能概率100%"
                    ) {
                        DebugManager.easySkill = it
                    }
                    DebugItem(
                        DebugManager.dodge100, "格挡率100%"
                    ) {
                        DebugManager.dodge100 = it
                    }
                    DebugItem(
                        DebugManager.fatal100, "暴击率100%"
                    ) {
                        DebugManager.fatal100 = it
                    }
                    DebugItem(
                        DebugManager.attack100, "攻击100倍"
                    ) {
                        DebugManager.attack100 = it
                    }
                    DebugItem(
                        DebugManager.defense100, "防御100倍"
                    ) {
                        DebugManager.defense100 = it
                    }
                    DebugItem(
                        DebugManager.hp100, "100倍血量"
                    ) {
                        DebugManager.hp100 = it
                    }
                    DebugItem(
                        DebugManager.singleStep, "单步调试"
                    ) {
                        DebugManager.singleStep = it
                    }
                    DebugItem(
                        DebugManager.unbreakable, "无敌秒杀"
                    ) {
                        DebugManager.unbreakable = it
                    }
                    DebugItem(
                        DebugManager.uploadRank, "Lite包上传排行榜"
                    ) {
                        DebugManager.uploadRank = it
                    }
                }
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(padding8)
                ) {
                    DebugItem(
                        DebugManager.unlockAll, "解锁全部(不限次修改名字)"
                    ) {
                        DebugManager.unlockAll = it
                    }
                    DebugItem(
                        DebugManager.easyEvent, "事件条件满足"
                    ) {
                        DebugManager.easyEvent = it
                    }
                    DebugItem(
                        DebugManager.oneCentShop, "商品1分钱"
                    ) {
                        DebugManager.oneCentShop = it
                    }
                    DebugItem(
                        DebugManager.freeShop, "付费商品直接获得"
                    ) {
                        DebugManager.freeShop = it
                    }
                    DebugItem(
                        DebugManager.eventWin, "事件必定胜利"
                    ) {
                        DebugManager.eventWin = it
                    }
                    DebugItem(
                        DebugManager.eventLose, "事件必定失败"
                    ) {
                        DebugManager.eventLose = it
                    }
                    DebugItem(
                        DebugManager.allEvent, "显示所有事件"
                    ) {
                        DebugManager.allEvent = it
                    }
                    DebugItem(
                        DebugManager.validEventsAll, "显示所有可用事件"
                    ) {
                        DebugManager.validEventsAll = it
                    }
                    DebugItem(
                        DebugManager.repeatEvent, "可重复进入事件"
                    ) {
                        DebugManager.repeatEvent = it
                    }
                    DebugItem(
                        DebugManager.questDone, "新手任务解锁全部"
                    ) {
                        DebugManager.questDone = it
                    }
                }
            }
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 1
                            }
                            repo.allyManager.gain(allies)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "1星所有", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 1
                            }
                            repo.allyManager.gain(allies.map { it.copy(num = 100) })
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "1星100", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Award(outAllies = listOf(repo.gameCore.getAllyPool().first().copy(num = 1000))).let {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    AwardManager.gainAward(award = it)
                                }
                            }
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "枪兵1000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Award(outAllies = listOf(repo.gameCore.getAllyPool().first { it.isHero() }
                                .copy(num = 1000))).let {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    AwardManager.gainAward(award = it)
                                }
                            }
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "欧灵1000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            repo.skillManager.data.clear()
                            repo.skillManager.save()
                            repo.allyManager.data.clear()
                            repo.allyManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除军团", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TalentManager.talents.clear()
                            TalentManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        EndingManager.deleteAll()
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除记录", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(Award(couponHero = 20000))
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "2w英雄抽", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(Award(couponAlly = 20000))
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "2w兵种抽", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.couponHistory.value += 100000
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "累抽+10w", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.couponHistory.value = 0
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除累计抽", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                repeat(20) { star ->
                    EffectButton(modifier = Modifier
                        .height(bigButtonHeight)
                        .background(B50),
                        onClick = {
                            GameApp.globalScope.launch {
                                increaseIntValueByKey(
                                    KEY_GAME_LOGIN_DAY,
                                    star + 1
                                )
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "登录${star + 1}天", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding4))
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            val listState = rememberLazyListState()
            val targetItem = TowerManager.maxLevel.value
            // Scroll to the target item
            LaunchedEffect(targetItem) {
                listState.animateScrollToItem(targetItem)
            }
            LazyRow(state = listState) {
                items(2000) { star ->
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .background(B50)
                            .padding(end = padding4), // 用右边距替代 Spacer
                        onClick = { TowerManager.maxLevel.value = star }
                    ) {
                        Text(
                            text = "爬塔${star}层",
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(pvpDiamond = 50).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "Pvp货币50", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(realMoney = 1000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "英雄币1000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(lotteryMoney = 100).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "红宝石100", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        TalentManager.talents.clear()
                        repo.gameCore.getTalentPool().filter {
                            it.level == 1
                        }.forEach {
                            TalentManager.talents[it.mainId] = if (it.levelLimit >= 10) 10 else it.levelLimit
                        }
                        TalentManager.save()
                        "已10级".toast()
                    }) {
                    Text(
                        text = "10级天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        TalentManager.talents.clear()
                        repo.gameCore.getTalentPool().filter {
                            it.level == it.levelLimit
                        }.forEach {
                            TalentManager.talents[it.mainId] = it.level
                        }
                        TalentManager.save()
                        "已满级".toast()
                    }) {
                    Text(
                        text = "满级天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        TalentManager.talents.clear()
                        TalentManager.save()
                        "已清除".toast()
                    }) {
                    Text(
                        text = "删除天赋", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(diamond = 100000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "荣誉点10w", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.diamond.value = 0
                        }
                    }) {
                    Text(
                        text = "删除荣誉点", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(key = 36).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "钻石36", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(key = 30000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "钻石3w", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.lotteryMoney.value += 20000
                        }
                    }) {
                    Text(
                        text = "2w转盘币",
                        style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(KEY_WAR_PASS_UNLOCK_EVIDENCE)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁战令1", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(KEY_WAR_PASS2_UNLOCK_EVIDENCE)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁战令2", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(holidayMoney = 500).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "圣诞币500", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                Spacer(modifier = Modifier.size(padding26))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value += 10
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数+10", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value -= 10
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数-10", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value += 3
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数+3", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value -= 3
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数-3", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            Row {
                val electric = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            electric.value += 1
                        }
                        GameButton(
                            text = if (electric.value == 0f) "特权值" else "${electric.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(electric = electric.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                        GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            electric.value -= 1
                        }
                    }

                    Slider(
                        modifier = Modifier
                            .size(slideWidth,slideHeight),
                        steps = 10,
                        value = electric.value,
                        onValueChange = { electric.value = it.toInt().toFloat() },
                        valueRange = 0f..80000f,
                    )
                }
            }
            Row {
                val warpass = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            warpass.value += 1
                        }
                        GameButton(
                            text = if (warpass.value == 0f) "通行证" else "${warpass.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass = warpass.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                        GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            warpass.value -= 1
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .size(slideWidth,slideHeight),
                        steps = 10,
                        value = warpass.value,
                        onValueChange = { warpass.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
            }
            Row {
                val warpass2 = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(text = "+1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            warpass2.value += 1
                        }
                        GameButton(
                            text = if (warpass2.value == 0f) "通行证2" else "${warpass2.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass2 = warpass2.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                        GameButton(text = "-1", clickGap = 0, buttonSize = ButtonSize.Small) {
                            warpass2.value -= 1
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .size(slideWidth,slideHeight),
                        steps = 10,
                        value = warpass2.value,
                        onValueChange = { warpass2.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySkills.clear()
                        debugSkillDialog.value = {
                            enemySkills.add(it)
                        }
                    }) {
                    Text(
                        text = "敌人技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySpeciesDialog.value = true
                    }) {
                    Text(
                        text = "敌人种族", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemyGroupDialog.value = true
                    }) {
                    Text(
                        text = "敌人阵营", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemyNumDialog.value = true
                    }) {
                    Text(
                        text = "双方阵型", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        playerSkills.clear()
                        debugSkillDialog.value = {
                            playerSkills.add(it)
                        }
                    }) {
                    Text(
                        text = "玩家技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        DebugManager.debugBattle = true
                        val roleHashMap = mutableMapOf<Int, Role?>()
                        positionList.forEach {
                            roleHashMap[it] = null
                        }
                        enemyMap.forEachIndexed { index, position ->
                            if (position in ALLY_ROW1_FIRST..ALLY_ROW2_FOURTH) {
                                DefaultAllyCreator.create(
                                    repo.gameCore.getRacePool().filter { it.star == 1 }[index],
                                    Property(),
                                    identifier = Identifier.player(name = "玩家$index")
                                ).apply {
                                    setSkills(emptyList())
                                    if (roleHashMap.values.mapNotNull { it }
                                            .none { it.isPlayerSide() }) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        playerSkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(2001), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (roleHashMap.values.mapNotNull { it }
                                                .filter { it.isPlayerSide() }.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        }
                                    }
                                    roleHashMap[position] = this
                                }
                            } else {
                                DefaultRoleCreator.create(
                                    repo.gameCore.getRacePool().filter {
                                        it.raceType == enemySpecies.value && it.raceType2 == enemyGroup.value && it.star == 1
                                    }.getOrElse(index, {repo.gameCore.getRacePool().first()}), Property(), identifier =  Identifier.enemy(name = "敌人$index")
                                ).apply {
                                    setSkills(emptyList())
                                    if (roleHashMap.values.mapNotNull { it }
                                            .none { !it.isPlayerSide() }) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        setSkills(emptyList())
                                        enemySkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(2001), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (roleHashMap.values.mapNotNull { it }
                                                .filter { !it.isPlayerSide() }.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(2001),
                                                this.roleIdentifier
                                            )
                                        }
                                    }
                                    roleHashMap[position] = this
                                }
                            }
                        }
                        repo.battleRoles.clear()
                        repo.battleRoles.putAll(roleHashMap)
                        repo.startBattle()
                        goto(DEBUG_BATTLE)
                    }) {
                    Text(
                        text = "开始战斗", style = MaterialTheme.typography.h4
                    )
                }
            }
        }
    }
    if (enemySpeciesDialog.value) {
        PanelDialog(onDismissRequest = {
            enemySpeciesDialog.value = false
        }) {
            LazyVerticalGrid(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                columns = GridCells.Fixed(5)
            ) {
                items(allRaceIds.size) { index ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = (index + 1).getRaceTypeName(),
                            style = MaterialTheme.typography.h4
                        )
                        GameButton(text = "选择") {
                            enemySpecies.value = allRaceIds[index]
                            enemySpeciesDialog.value = false
                        }
                    }
                }
            }
        }
    }
    if (enemyGroupDialog.value) {
        PanelDialog(onDismissRequest = {
            enemyGroupDialog.value = false
        }) {
            LazyVerticalGrid(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                columns = GridCells.Fixed(5)
            ) {
                items(allGroupsIds.size) { index ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = (index + 1).getRaceGroupName(),
                            style = MaterialTheme.typography.h4
                        )
                        GameButton(text = "选择") {
                            enemyGroup.value = allGroupsIds[index]
                            enemyGroupDialog.value = false
                        }
                    }
                }
            }
        }
    }
    if (enemyNumDialog.value) {
        PanelDialog(onDismissRequest = {
            enemyNumDialog.value = false
        }) {
            FlowRow(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 4
            ) {
                repeat(4) { index ->
                    val position = ENEMY_ROW2_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有敌人"
                    } else {
                        "无敌人"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
                repeat(4) { index ->
                    val position = ENEMY_ROW1_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有敌人"
                    } else {
                        "无敌人"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
                Spacer(modifier = Modifier
                    .height(gapLarge)
                    .fillMaxWidth())
                repeat(8) { index ->
                    val position = ALLY_ROW1_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有盟友"
                    } else {
                        "无盟友"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DebugItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Checkbox(
            checked = checked, onCheckedChange = {
                GameApp.globalScope.launch {
                    onChecked(it)
                }
            }, modifier = Modifier.size(
                buttonHeight
            )
        )
        Spacer(modifier = Modifier.size(padding4))
        Text(
            text = content.trimIndent(), style = MaterialTheme.typography.h4
        )
    }
}
