import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlinx-serialization'
    id 'kotlin-kapt'
}

if (getGradle().getStartParameter().getTaskRequests()
        .toString().contains("Oversea")) {
    apply plugin: 'com.google.gms.google-services'
    apply plugin: 'com.google.firebase.crashlytics'
}
//apply from: '../gradle/tools/mcimage.gradle'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.moyu.sanguorensheng"
        minSdk 26
        targetSdk 35
        versionCode 12404
        versionName "1.24.4"

        // 添加编译时间戳到BuildConfig
        buildConfigField "long", "BUILD_TIMESTAMP", "${System.currentTimeMillis()}L"

        vectorDrawables {
            useSupportLibrary true
        }

        // Similar to other properties in the defaultConfig block,
        // you can configure the ndk block for each product flavor
        // in your build configuration.
        ndk {
            // Specifies the ABI configurations of your native
            // libraries Grad<PERSON> should build and package with your app.
            abiFilters 'x86', 'x86_64', 'armeabi-v7a', 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file('../wujindeyuansushi.keystore')
            storePassword 'tujiulong123'
            keyAlias 'key0'
            keyPassword 'tujiulong123'
        }
    }
    productFlavors {
        taptap {
            dimension "platform"
            applicationId "com.moyu.rongyaojuntan"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/647984")
            resValue("string", "platform_channel", "taptap")
            resValue("string", "csjCodeId", "964945644")
            resValue("string", "csjAppId", "5677280")
            resValue("bool", "has_billing", "false")
            resValue("bool", "need_privacy_check", "true")
            resValue("bool", "need_anti_addict_check", "true")
        }

        toutiao {
            dimension "platform"
            applicationId "com.moyu.rongyaojuntan_toutiao"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/647984")
            resValue("string", "platform_channel", "toutiao")
            resValue("string", "csjCodeId", "0")
            resValue("string", "csjAppId", "0")
            resValue("bool", "has_billing", "true")
            resValue("bool", "need_privacy_check", "false")
            resValue("bool", "need_anti_addict_check", "false")
        }

//        xiaomi {
//            multiDexEnabled true
//            dimension "platform"
//            applicationId "com.moyu.diguoshengyuan.mi"
//            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
//            resValue("string", "platform_channel", "xiaomi")
//            resValue("string", "csjCodeId", "960104348")
//            resValue("string", "csjAppId", "5600150")
//        }

        taiwan {
            dimension "platform"
            applicationId "com.moyu.rongyaojuntan_taiwan"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "googleplay")
            resValue("string", "csjCodeId", "953443412")
            resValue("string", "csjAppId", "5428167")
        }

        china {
            dimension "region"
            resValue("bool", "has_google_service", "false")
            resValue("string", "serverUrl", "http://124.223.36.172:9795/yuansuqiu/api/v2/")
//            resValue("string", "serverUrl", "http://192.168.31.30:9795/yuansuqiu/api/v2/")
        }

        oversea {
            dimension "region"
            resValue("bool", "has_google_service", "true")
            resValue("bool", "has_billing", "true")
            resValue("bool", "need_privacy_check", "false")
            resValue("bool", "need_anti_addict_check", "false")
            resValue("string", "serverUrl", "http://43.134.0.148:9795/yuansuqiu/api/v2/")
        }

        lite {
            dimension "debugable"
        }
        product {
            dimension "debugable"
        }
    }
    flavorDimensions "platform", "debugable", "region"
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_19
        targetCompatibility JavaVersion.VERSION_19
    }
    kotlinOptions {
        jvmTarget = '19'
    }
    buildFeatures {
        compose true
        buildConfig = true
    }
    kotlin {
        jvmToolchain(19)
    }
    composeOptions {
        kotlinCompilerExtensionVersion "1.5.15"
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    bundle {
        language {
            enableSplit = false
        }
    }
    namespace 'com.moyu.chuanqirensheng'
}

dependencies {
    // projects
    implementation project(path: ':core')
    // androidx
//    xiaomiImplementation 'androidx.core:core:1.13.1'
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.3'
    implementation 'androidx.activity:activity-compose:1.8.2'
    // compose
    implementation "androidx.compose.ui:ui:1.7.6"
    implementation "androidx.compose.material:material:1.7.6"
    // navigation
    implementation("androidx.navigation:navigation-compose:2.7.7")

    // data store
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    // timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // coil with compose 仅taptap版本需要网络图片
    implementation 'io.coil-kt:coil-compose:2.5.0'
    implementation 'io.coil-kt:coil:2.5.0'

    // charts
    implementation('io.github.bytebeats:compose-charts:0.1.2')
    // zip4j
    implementation("net.lingala.zip4j:zip4j:2.11.5")
    // ktor
    implementation("io.ktor:ktor-client-core:$ktor_version")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    // json
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
    // uuid
    implementation("app.softwork:kotlinx-uuid-core:0.0.12")
    // flip
    implementation "com.wajahatkarim:flippable:1.5.4"
    //exoplayer播放器
    implementation 'androidx.media3:media3-exoplayer:1.4.1'
    implementation 'androidx.media3:media3-ui:1.4.1'

    // bugly
    chinaImplementation 'com.tencent.bugly:crashreport:*******'
    // 穿山甲SDK
    taptapImplementation 'com.pangle.cn:ads-sdk-pro:*******'
    // root
    chinaImplementation 'com.scottyab:rootbeer-lib:0.1.0'
    // taptap
    taptapImplementation 'com.taptap:lc-storage-android:8.2.24'
    taptapImplementation 'com.taptap:lc-realtime-android:8.2.24'
    chinaImplementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    taptapImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/taptap/libs')
    // toutiao
    toutiaoImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/toutiao/libs')

    // Add the Crashlytics Gradle plugin
    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    overseaImplementation 'com.google.android.play:review:2.0.1'
    // For Kotlin users, also add the Kotlin extensions library for Play In-App Review:
    overseaImplementation 'com.google.android.play:review-ktx:2.0.1'

    overseaImplementation platform('com.google.firebase:firebase-bom:32.8.0')
    overseaImplementation('com.google.firebase:firebase-crashlytics-ktx')
    overseaImplementation('com.google.firebase:firebase-analytics-ktx')
    overseaImplementation 'com.android.billingclient:billing-ktx:7.0.0'
    overseaImplementation 'com.google.android.gms:play-services-auth:21.0.0'

    overseaImplementation 'com.google.android.gms:play-services-ads:23.2.0'
    overseaImplementation("com.google.ads.mediation:mintegral:16.7.81.0")
    overseaImplementation("com.unity3d.ads:unity-ads:4.11.3")
    overseaImplementation("com.google.ads.mediation:unity:4.12.0.0")
    overseaImplementation("com.google.ads.mediation:pangle:6.0.0.5.0")
    overseaImplementation("com.google.ads.mediation:applovin:********")

    overseaImplementation "com.android.installreferrer:installreferrer:2.2"
//    overseaImplementation 'com.facebook.android:facebook-android-sdk:latest.release'
    overseaImplementation 'com.adjust.sdk:adjust-android:5.4.1'
    overseaImplementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

//    def lastVersion = "3.4.3"
//    xiaomiImplementation "com.google.code.gson:gson:2.8.6"
//    xiaomiImplementation "com.google.zxing:core:3.2.0"
//    xiaomiImplementation "com.alipay.sdk:alipaysdk-android:15.8.09@aar"
//    xiaomiImplementation "com.xiaomi.gamecenter.sdk:mioauth:$lastVersion"
//    xiaomiImplementation "com.xiaomi.gamecenter.sdk:onetrack-sdk:2.2.1"
}

allprojects {
    tasks.withType(KotlinCompile).configureEach {
        kotlinOptions {
            allWarningsAsErrors = false
            freeCompilerArgs += [
                    "-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
                    "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                    "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
                    "-opt-in=androidx.media3.common.util.UnstableApi",
                    "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi",
            ]
        }
    }
}

