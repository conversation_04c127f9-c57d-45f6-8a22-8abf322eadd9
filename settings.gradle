dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        flatDir {
            dirs 'src/main/libs'
        }
        mavenCentral()
        maven { url 'https://repo1.maven.org/maven2/' }
        maven {
            url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        }
        maven {
            url = uri("https://artifact.bytedance.com/repository/pangle/")
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
        maven {
            url "https://repos.xiaomi.com/maven"
            credentials {
                username 'mi-gamesdk'
                password 'AKCp8mYeLuhuaGj6bK1XK7t2w4CsPuGwg6GpQdZ9cat7K59y5sD7Tx3dHjJcFrBGj3TQ4vi7g'
            }
        }
    }
}
rootProject.name = "Composed"
include ':core'
include ':game'
