//apply plugin: 'McImage'
//
//McImageConfig {
//    isCheckSize false //Whether to detect image size，default true
//    optimizeType "ConvertWebp" //Optimize Type，"ConvertWebp" or "Compress"，default "Compress", "CompressWebp" is a better compression ratio but it don't support api < 18
//    maxSize 1*1024*1024 //big image size threshold，default 1MB
//    enableWhenDebug false //switch in debug build，default true
//    isCheckPixels false // Whether to detect image pixels of width and height，default true
//    maxWidth 1000 //default 1000
//    maxHeight 1000 //default 1000
//    whiteList = [ //do not do any optimization for the images who in the list
//                  "ic_launcher.png"
//    ]
//    mctoolsDir "$rootDir"
//    isSupportAlphaWebp true  //Whether support convert the Image with Alpha chanel to Webp，default false, the images with alpha chanels will be compressed.if config true, its need api level >=18 or do some compatible measures
//    multiThread true  //Whether open multi-thread processing，default true
//    bigImageWhiteList = [] //do not detect big size or large pixels for the images who in the list
//}