package com.moyu.core

import com.moyu.core.config.ConfigHolder
import com.moyu.core.config.GameConfigManager
import com.moyu.core.logic.battle.BattleCallback
import com.moyu.core.logic.battle.BattleController
import com.moyu.core.logic.battle.BattleFactory
import com.moyu.core.logic.battle.BattleHolder
import com.moyu.core.model.role.Role


class GameCore(val callback: BattleCallback, private val gameConfig: GameConfigManager = GameConfigManager()) :
    ConfigHolder by gameConfig, BattleFactory, BattleCallback by callback {

    var battleController: BattleController? = null
    var you: Role = Role()

    companion object {
        lateinit var instance: GameCore
        val EMPTY by lazy { instance.createBattleField(hashMapOf()) }
    }

    init {
        instance = this
    }

    override fun createBattleField(roles: MutableMap<Int, Role?>): BattleHolder {
        return BattleController(roles).apply {
            battleController = this
        }
    }
}