package com.moyu.core.config

import com.moyu.core.model.Pool

class PoolConfigParser : ConfigParser<Pool> {
    override fun parse(line: String): Pool {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val type = words[i++].split(",").map { it.toInt() }
        val pool = words[i++].split(",").map { it.toInt() }
        val num = words[i++].split(",").map { it.toInt() }
        val rate = words[i++].split(",").map { it.toInt() }
        val quality = words[i++].split(",").map { it.toInt() }
        val extraNum = words[i++].split(",").map { it.toInt() }
        val totalNum = words[i].toInt()
//        Log.e("", "$line")
//        if (type.size != num.size) {
//            Log.e("", "pool配置错误，num数量不对 $line")
//        }
        return Pool(
            id,
            type,
            pool,
            num,
            rate,
            quality,
            extraNum,
            totalNum,
        )
    }
}