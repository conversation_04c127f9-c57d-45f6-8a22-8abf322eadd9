package com.moyu.core.config

import com.moyu.core.model.Ally

class AllyConfigParser : ConfigParser<Ally> {
    override fun parse(line: String): Ally {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val mainId = words[i++].toInt()
        val name = words[i++]
        val star = words[i++].toInt()
        val starLimit = words[i++].toInt()
        val quality = words[i++].toInt()
        val type = words[i++].toInt()
        val skillNum = words[i++].toInt()
        val story = words[i++]
        val belong = words[i++].toInt()
        val starUpNum = words[i++].toInt()
        val starUpResourceNum = words[i++].toInt()
        val dropLimit = words[i++].toInt()
        val up = words[i].toInt()
        return Ally(
            id,
            mainId,
            name,
            star,
            starLimit,
            quality,
            type,
            skillNum,
            starUpNum,
            starUpResourceNum,
            dropLimit,
            story,
            belong,
            up
        )
    }
}