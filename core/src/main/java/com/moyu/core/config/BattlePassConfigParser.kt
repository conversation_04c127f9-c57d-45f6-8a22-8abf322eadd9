package com.moyu.core.config

import com.moyu.core.model.BattlePass

class BattlePassConfigParser : ConfigParser<BattlePass> {
    override fun parse(line: String): BattlePass {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val season = words[i++].trim().toInt()
        val seasonName = words[i++].trim()
        val level = words[i++].trim().toInt()
        val levelLimit = words[i++].trim().toInt()
        val exp = words[i++].trim().toInt()
        val expTotal = words[i++].trim().toInt()
        val expRealTotal = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val unlockType = words[i++].trim().toInt()
        val themeReward = words[i++].trim().toInt()
        val isFree = words[i].trim().toInt()
        return BattlePass(
            id,
            season,
            seasonName,
            level,
            levelLimit,
            exp,
            expTotal,
            expRealTotal,
            reward,
            unlockType,
            themeReward,
            isFree,
        )
    }
}