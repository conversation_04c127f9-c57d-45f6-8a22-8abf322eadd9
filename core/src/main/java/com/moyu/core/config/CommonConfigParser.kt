package com.moyu.core.config

import com.moyu.core.model.Common

class CommonConfigParser: ConfigParser<Common> {
    override fun parse(line: String): Common {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val num = words[i++].trim()
        val type = words[i].trim().toInt()
        return Common(
            id = id,
            name = name,
            num = num,
            type = type,
        )
    }
}