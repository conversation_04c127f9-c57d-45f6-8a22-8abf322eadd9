package com.moyu.core.config

import com.moyu.core.model.GameMap

class MapConfigParser : ConfigParser<GameMap> {
    override fun parse(line: String): GameMap {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val time = words[i++].trim().toInt()
        val winReward = words[i].trim().toInt()
        return GameMap(
            id,
            name,
            time,
            winReward,
        )
    }
}