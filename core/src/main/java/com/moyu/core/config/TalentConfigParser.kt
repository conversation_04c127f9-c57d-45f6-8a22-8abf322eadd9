package com.moyu.core.config

import com.moyu.core.model.Talent

class TalentConfigParser : ConfigParser<Talent> {
    override fun parse(line: String): Talent {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val level = words[i++].trim().toInt()
        val levelLimit = words[i++].trim().toInt()
        val position = words[i++].trim().split(",").map { it.toInt() }
        val talentSkill = words[i++].trim().toInt()
        val cost = words[i++].trim().toInt()
        val conditionType = words[i++].trim().toInt()
        val conditionNum = words[i++].trim().toInt()
        val icon = words[i++].trim()
        val costPool = words[i++].trim().toInt()
        val mainName = words[i].trim()
        return Talent(
            id,
            mainId,
            type,
            name,
            level,
            levelLimit,
            position,
            talentSkill,
            cost,
            conditionType,
            conditionNum,
            icon,
            costPool,
            mainName
        )
    }
}