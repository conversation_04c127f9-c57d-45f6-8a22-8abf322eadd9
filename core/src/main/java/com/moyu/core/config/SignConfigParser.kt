package com.moyu.core.config

import com.moyu.core.model.Sign

class SignConfigParser : ConfigParser<Sign> {
    override fun parse(line: String): Sign {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val day = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val type = words[i].trim().toInt()

        return Sign(
            id,
            day,
            reward,
            type
        )
    }
}