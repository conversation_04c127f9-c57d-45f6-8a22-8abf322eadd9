package com.moyu.core.config

import com.moyu.core.model.Arena

class ArenaConfigParser : ConfigParser<Arena> {
    override fun parse(line: String): Arena {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val day = words[i++].trim().toInt()
        val type = words[i++].trim().split(",").map { it.toInt() }
        val desc = words[i].trim()
        return Arena(
            id,
            day,
            type,
            desc
        )
    }
}