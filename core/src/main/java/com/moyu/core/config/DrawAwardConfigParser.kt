package com.moyu.core.config

import com.moyu.core.model.DrawAward

class DrawAwardConfigParser : ConfigParser<DrawAward> {
    override fun parse(line: String): DrawAward {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val level = words[i++].toInt()
        val conditionNum = words[i++].toInt()
        val reward = words[i].toInt()
        return DrawAward(
            id,
            level,
            conditionNum,
            reward,
        )
    }
}