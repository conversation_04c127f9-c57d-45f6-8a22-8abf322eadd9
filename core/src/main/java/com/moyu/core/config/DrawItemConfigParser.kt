package com.moyu.core.config

import com.moyu.core.model.DrawItem

class DrawItemConfigParser : ConfigParser<DrawItem> {
    override fun parse(line: String): DrawItem {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val type = words[i++].toInt()
        val quality = words[i++].toInt()
        val rate = words[i++].toDouble()
        val pool = words[i].toInt()
        return DrawItem(
            id,
            type,
            quality,
            rate,
            pool,
        )
    }
}