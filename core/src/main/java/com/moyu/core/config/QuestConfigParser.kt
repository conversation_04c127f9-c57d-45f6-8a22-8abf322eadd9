package com.moyu.core.config

import com.moyu.core.model.Quest

class QuestConfigParser : ConfigParser<Quest> {
    override fun parse(line: String): Quest {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val taskType = words[i++].toInt()
        val name = words[i++]
        val desc = words[i++]
        val type = words[i++].toInt()
        val subType = words[i++].split(",").map { it.toInt() }
        val num = words[i++].toInt()
        val reward = words[i++].toInt()
        val order = words[i++].toInt()
        val talent = words[i++].split(",").map { it.toInt() }
        val pic = words[i++]
        val dialogId = words[i].toInt()
        return Quest(
            id,
            taskType,
            name,
            desc,
            type,
            subType,
            num,
            reward,
            order,
            talent,
            pic,
            dialogId
        )
    }
}