package com.moyu.core.config

import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.Arena
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.DayReward
import com.moyu.core.model.Difficult
import com.moyu.core.model.DrawAward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.GameMap
import com.moyu.core.model.Gift
import com.moyu.core.model.Mission
import com.moyu.core.model.Pool
import com.moyu.core.model.Pvp
import com.moyu.core.model.Quest
import com.moyu.core.model.Race
import com.moyu.core.model.Sell
import com.moyu.core.model.Sign
import com.moyu.core.model.Talent
import com.moyu.core.model.Title
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Unlock
import com.moyu.core.model.Vip
import com.moyu.core.model.WorldBoss
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill

const val BUFF_FILE_NAME = "buff.txt"
const val ALLY_FILE_NAME = "ally.txt"
const val DIFFICULT_FILE_NAME = "difficult.txt"
const val DRAW_FILE_NAME = "draw.txt"
const val MAP_FILE_NAME = "map.txt"
const val GIFT_FILE_NAME = "gift.txt"
const val COMMON_FILE_NAME = "common.txt"
const val SELL_FILE_NAME = "sell.txt"
const val RACE_FILE_NAME = "race.txt"
const val SKILL_FILE_NAME = "skill.txt"
const val EQUIPMENT_FILE_NAME = "equipment.txt"
const val DRAW_AWARD_FILE_NAME = "drawreward.txt"
const val POSITION_FILE_NAME = "position.txt"
const val TASK_FILE_NAME = "task.txt"
const val UNLOCK_FILE_NAME = "unlock.txt"
const val TALENT_FILE_NAME = "talent.txt"
const val EVENT_FILE_NAME = "event.txt"
const val SCROLL_FILE_NAME = "scroll.txt"
const val POOL_FILE_NAME = "pool.txt"
val VIP_FILE_NAME = if (AppWrapper.isToutiao) "vip_toutiao.txt" else "vip.txt"
const val COMBINEDBUFF_FILE_NAME = "combinedbuff.txt"
const val WAR_PASS_FILE_NAME = "battlepass.txt"
const val WAR_PASS2_FILE_NAME = "battlepass2.txt"
const val SIGN_FILE_NAME = "sign.txt"
const val MISSION_FILE_NAME = "mission.txt"
const val PVP_FILE_NAME = "pvp.txt"
const val DAY_REWARD_FILE_NAME = "dayreward.txt"
const val LUCKY_FILE_NAME = "lucky.txt"
const val TURNTABLE_FILE_NAME = "turntable.txt"
const val ARENA_FILE_NAME = "arena2.txt"
const val TOWER_FILE_NAME = "tower.txt"
const val WORLDBOSS_FILE_NAME = "worldboss.txt"

interface ConfigHolder {
    fun setGameConfig(key: String, pool: List<Any>)

    // basic
    fun getSkillPool(): List<Skill>
    fun getBuffPool(): List<Buff>
    fun getTitlePool(): List<Title>
    fun getEquipPool(): List<Equipment>
    fun getRacePool(): List<Race>
    fun getEventPool(): List<Event>
    fun getAllyPool(): List<Ally>
    fun getDifficultPool(): List<Difficult>
    fun getTalentPool(): List<Talent>
    fun getSellPool(): List<Sell>
    fun getGameTaskPool(): List<Quest>
    fun getUnlockPool(): List<Unlock>
    fun getScrollPool(): List<Scroll>
    fun getPoolPool(): List<Pool>
    fun getVipPool(): List<Vip>
    fun getCombinedBuffPool(): List<Buff>
    fun getBattlePassPool(): List<BattlePass>
    fun getBattlePass2Pool(): List<BattlePass>
    fun getSignPool(): List<Sign>
    fun getMissionPool(): List<Mission>
    fun getPvpPool(type: Int): List<Pvp>
    fun getMapPool(): List<GameMap>
    fun getDrawPool(): List<DrawItem>
    fun getDrawAwardPool(): List<DrawAward>
    fun getGiftPool(): List<Gift>
    fun getDayRewardPool(): List<DayReward>
//    fun getLuckyPool(): List<Lucky>
    fun getTurnTablePool(): List<TurnTable>
    fun getArenaPool(): List<Arena>
    fun getTowerPool(): List<Tower>
    fun getWorldBossPool(): List<WorldBoss>

    // common
    fun getConstA(): Double
    fun getConstB(): Double
    fun getInitGold(): Int
    fun getInitWood(): Int
    fun getInitStone(): Int
    fun getEndingAwardLevel(age: Int): Int
    fun getEndingDiamondLevel(age: Int): Int
    fun getEndingAwardDiamond(difficult: Int, level: Int): Int
    fun getRefreshShopCost(): Int
    fun getKeyToDiamondRate(): Int
    fun getDailyShopRefreshCount(): Int

    // extra
    fun getSkillById(skillId: Int): Skill
    fun getEquipById(equipId: Int): Equipment
    fun getGameTaskById(id: Int): Quest
    fun getAllyById(id: Int): Ally
    fun getEventById(id: Int): Event
    fun getScrollById(id: Int): Scroll
    fun getBuffById(buffId: Int): Buff
    fun getRaceById(raceId: Int): Race
    fun getTalentById(id: Int): Talent
    fun getUnlockById(id: Int): Unlock
    fun getFirstAllyIds(): List<Int>
    fun getFirstEndingAwardPoolId(): Int
    fun getPoolById(id: Int): Pool
    fun getPoolByKeyAny(key: String): List<Any>
    fun getWarPassQuestCount(): Int
    fun getNewQuestCount(): Int
    fun getDailyQuestCount(): Int
    fun getShopDataByIndex(index: Int): List<Int>
    fun getUnlockTalentPageLevel(): Int

    fun getMaxOtherUseYourCount(): Int
    fun getMaxUseOtherCount(): Int
    fun getShareCodeAwardKeyNum(): Int
    fun getMaxOneDayDiamondLimit(): Int
    fun getTextShareAwardNum(): Int
    fun getImageShareAwardNum(): Int
    fun getQuality1AllyNum(difficult: Int, level: Int): Int
    fun getQuality2AllyNum(difficult: Int, level: Int): Int
    fun getQuality3AllyNum(difficult: Int, level: Int): Int
    fun getQuality4AllyNum(difficult: Int, level: Int): Int
    fun getQuality5AllyNum(difficult: Int, level: Int): Int
    fun getQuality1AllyPool(): List<Int>
    fun getQuality2AllyPool(): List<Int>
    fun getQuality3AllyPool(): List<Int>
    fun getQuality4AllyPool(): List<Int>
    fun getQuality5AllyPool(): List<Int>
    fun getFamousDiamond(): Int
    fun getAllyCouponRate(): Int
    fun getHeroCouponRate(): Int
    fun getInitCouponPoolId(): Int
    fun getGiftById(value: Int): Gift?
    fun getCheapLotteryCosts(): List<Int>
    fun getExpensiveLotteryCosts(): List<Int>
    fun getHolidayLotteryCosts(): List<Int>
    fun getTowerAwardKey(): Int

    // WorldBoss convenience methods
    fun getWorldBossById(id: Int): WorldBoss?
    fun getWorldBossByDay(day: Int): WorldBoss?
    fun getValueById(id: Int): String
}