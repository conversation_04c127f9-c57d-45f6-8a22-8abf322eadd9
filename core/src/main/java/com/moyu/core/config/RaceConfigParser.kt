package com.moyu.core.config

import com.moyu.core.model.Race

class RaceConfigParser : ConfigParser<Race> {
    override fun parse(line: String): Race {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val roleId = words[i++].toInt()
        val name = words[i++]
        val raceType = words[i++].toInt()
        val raceType2 = words[i++].toInt()
        val level = words[i++].toInt()
        val star = words[i++].toInt()
        val quality = words[i++].toInt()
        val attribute1 = words[i++].toInt()
        val attribute2 = words[i++].toInt()
        val attribute3 = words[i++].toInt()
        val attribute4 = words[i++].toInt()
        val attribute5 = words[i++].toInt()
        val attribute6 = words[i++].toInt()
        val attribute7 = words[i++].toInt()
        val attribute8 = words[i++].toDouble()
        val attribute9 = words[i++].toDouble()
        val attribute10 = words[i++].toDouble()
        val attribute11 = words[i++].toInt()
        val skillId = words[i++].split(",").map { it.toInt() }
        val randomSkillId = words[i++].split(",").map { it.toInt() }
        val banSkillId = words[i++].split(",").map { it.toInt() }
        val randomSkillNum = words[i++].split(",").map { it.toInt() }
        val pic = words[i++]
        val story = words[i]
        return Race(
            id,
            roleId,
            name,
            raceType,
            raceType2,
            level,
            star,
            quality,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            attribute8,
            attribute9,
            attribute10,
            attribute11,
            skillId,
            randomSkillId,
            banSkillId,
            randomSkillNum,
            pic,
            story,
        )
    }
}