package com.moyu.core.config

import com.moyu.core.model.TurnTable

class TurnTableConfigParser : ConfigParser<TurnTable> {
    override fun parse(line: String): TurnTable {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val price = words[i++].trim().toInt()
        val reward1 = words[i++].trim().toInt()
        val reward2 = words[i++].trim().toInt()
        val reward3 = words[i++].trim().toInt()
        val reward4 = words[i].trim().toInt()
        return TurnTable(
            id,
            type,
            name,
            price,
            reward1,
            reward2,
            reward3,
            reward4,
        )
    }
}