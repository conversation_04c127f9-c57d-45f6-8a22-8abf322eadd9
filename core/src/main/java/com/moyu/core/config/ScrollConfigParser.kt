package com.moyu.core.config

import com.moyu.core.model.skill.Scroll

class ScrollConfigParser : ConfigParser<Scroll> {
    override fun parse(line: String): Scroll {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].toInt()
        val mainId = words[i++].toInt()
        val name = words[i++]
        val star = words[i++].toInt()
        val starLimit = words[i++].toInt()
        val quality = words[i++].toInt()
        val type = words[i++].toInt()
        val starUpNum = words[i++].toInt()
        val dropLimit = words[i++].toInt()
        val story = words[i++]
        val belong = words[i++].toInt()
        val position = words[i++].split(",").map { it.toInt() }
        val cost = words[i++].toInt()
        val conditionType = words[i++].toInt()
        val conditionNum = words[i++].toInt()
        val skillTreeId = words[i++].toInt()
        val mainName = words[i]
        return Scroll(
            id,
            mainId,
            name,
            star,
            starLimit,
            quality,
            type,
            starUpNum,
            dropLimit,
            story,
            belong,
            position,
            cost,
            conditionType,
            conditionNum,
            skillTreeId,
            mainName
        )
    }
}