package com.moyu.core.config

import com.moyu.core.model.Mission

class MissionConfigParser : ConfigParser<Mission> {
    override fun parse(line: String): Mission {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val level = words[i++].trim().toInt()
        val levelLimit = words[i++].trim().toInt()
        val exp = words[i++].trim().toInt()
        val expTotal = words[i++].trim().toInt()
        val expRealTotal = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val chestPic = words[i].trim()
        return Mission(
            id,
            level,
            levelLimit,
            exp,
            expTotal,
            expRealTotal,
            reward,
            chestPic
        )
    }
}