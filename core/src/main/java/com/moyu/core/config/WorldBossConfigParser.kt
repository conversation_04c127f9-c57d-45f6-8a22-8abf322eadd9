package com.moyu.core.config

import com.moyu.core.model.WorldBoss

class WorldBossConfigParser : ConfigParser<WorldBoss> {
    override fun parse(line: String): WorldBoss {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val day = words[i++].trim().toInt()
        val bossIds = words[i++].trim().split(",").map { it.toInt() }
        val desc = words[i++].trim()
        return WorldBoss(
            id,
            day,
            bossIds,
            desc
        )
    }
}
