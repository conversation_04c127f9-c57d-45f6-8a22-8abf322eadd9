package com.moyu.core.json

import com.moyu.core.logic.race.DefaultRaceHolder
import com.moyu.core.model.Race
import com.moyu.core.logic.race.RaceHolder
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

object RaceHolderSerializer : KSerializer<RaceHolder> {
    override val descriptor: SerialDescriptor = Race.serializer().descriptor

    override fun serialize(encoder: Encoder, value: RaceHolder) {
        val dataClass = value.getRace()
        encoder.encodeSerializableValue(Race.serializer(), dataClass)
    }

    override fun deserialize(decoder: Decoder): RaceHolder {
        val surrogate = decoder.decodeSerializableValue(Race.serializer())
        return DefaultRaceHolder().apply {
            setRace(surrogate)
        }
    }
}