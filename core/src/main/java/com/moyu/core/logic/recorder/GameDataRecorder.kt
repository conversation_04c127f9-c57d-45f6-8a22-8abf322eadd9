package com.moyu.core.logic.recorder

import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill

interface GameDataRecorder {
    fun saveDamageResult(roleIdentifier: RoleIdentifier, damageResult: DamageResult)
    fun getDamageResults(roleIdentifier: RoleIdentifier): List<DamageResult>
    fun saveWoundedResult(roleIdentifier: RoleIdentifier, damageResult: DamageResult)
    fun getWoundedResults(roleIdentifier: RoleIdentifier): List<DamageResult>
    fun saveHealResult(roleIdentifier: RoleIdentifier, healResult: HealR<PERSON>ult)
    fun getHealResults(roleIdentifier: RoleIdentifier): List<HealResult>

    fun saveCastSkills(roleIdentifier: RoleIdentifier, skill: Skill)
    fun getCastSkills(roleIdentifier: RoleIdentifier): List<Skill>
    fun saveHealValue(roleIdentifier: RoleIdentifier, healValue: Int)
    fun getHealValue(roleIdentifier: RoleIdentifier): Int

    fun getDamageResultsBySide(roleIdentifier: RoleIdentifier): List<DamageResult>
    fun getHealResultsBySide(roleIdentifier: RoleIdentifier): List<HealResult>
    fun getCastSkillsBySide(roleIdentifier: RoleIdentifier): List<Skill>
}