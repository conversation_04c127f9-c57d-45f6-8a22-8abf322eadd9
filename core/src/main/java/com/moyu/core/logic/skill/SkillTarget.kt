package com.moyu.core.logic.skill

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.info.addFrenzyInfo
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM

fun Skill.getTargets(
    target: Int,
    result: BattleField,
    skillOwner: Role,
    triggerSkill: Skill? = null
): List<Role> {
    val frenzy = skillOwner.isFrenzy() && (this.triggerType == TriggerType.ACTIVE.value || this.isNormalAttackType())
    if (frenzy) {
        GameCore.instance.addFrenzyInfo(skillOwner)
    }
    val peer = result.getDirectPeer(skillOwner) ?: return emptyList()
    val divideAttack = skillOwner.isDivideAttack(this)
    return (when { // 狂暴状态下，target随机，除非是target原本就是双方
        target == 1 -> { // 自己
            listOf(skillOwner)
        }

        target == 2 -> { // 随机一个己方角色（包括自己和召唤物）
            result.getMyTeamRoles(skillOwner).shuffled(RANDOM).firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 3 -> { // 随机一个其他己方角色（不包括自己，包括召唤物）
            result.getMyTeamRoles(skillOwner)
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 4 -> { // 己方所有角色（包括自己和召唤物）
            result.getMyTeamRoles(skillOwner)
        }

        target == 5 -> { // 其他所有己方角色（不包括自己，包括召唤物）
            result.getMyTeamRoles(skillOwner)
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }
        }

        target == 6 -> { // 随机一个己方英雄（包括自己，不包括召唤物）
            result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 7 -> { // 随机一个其他己方英雄（不包括自己，不包括召唤物）
            result.getMyTeamRoles(skillOwner)
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }
                .filter { !it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 8 -> { // 己方所有英雄（包括自己，不包括召唤物）
            result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }
        }

        target == 9 -> {  //其他所有己方英雄（不包括自己，不包括召唤物）
            result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }
        }

        target == 10 -> { // 自己的所有召唤物
            result.getMyTeamRoles(skillOwner)
                .filter { it.isMinion() && it.masterId() == skillOwner.playerId() }
        }

        target == 11 -> { // 自己的随机一个召唤物
            result.getMyTeamRoles(skillOwner)
                .filter { it.isMinion() && it.masterId() == skillOwner.playerId() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 12 -> { // 随机一个己方召唤物
            result.getMyTeamRoles(skillOwner).filter { it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 13 -> { // 己方所有召唤物
            result.getMyTeamRoles(skillOwner).filter { it.isMinion() }
        }

        target in 21..30 -> { // 己方所有种族1-10角色（包括召唤物）
            val raceType = target - 20
            result.getMyTeamRoles(skillOwner).filter { it.getRace().raceType == raceType }
        }

        target in 31..45 -> { // 己方属性1-15最高的一个角色（不包括召唤物）
            val propertyTarget = target - 30
            listOf(result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }.maxBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget)
            })
        }
        target in 46..60 -> { // 己方属性1-15最低的一个角色（不包括召唤物）
            val propertyTarget = target - 45
            listOf(result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }.minBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget)
            })
        }
        target in 631..645 -> { // 己方属性百分比1-15百分最高的一个角色（不包括召唤物）
            val propertyTarget = target - 630
            listOf(result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }.maxBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget) /
                        it.getDefaultProperty().getPropertyByTarget(propertyTarget)
            })
        }
        target in 646..660 -> { // 己方属性百分比1-15百分最低的一个角色（不包括召唤物）
            val propertyTarget = target - 645
            listOf(result.getMyTeamRoles(skillOwner).filter { !it.isMinion() }.minBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget) /
                        it.getDefaultProperty().getPropertyByTarget(propertyTarget)
            })
        }

        // 6v6新增
        target == 61 -> { // 己方所有前排角色
            result.getRow1ByRole(skillOwner)
        }
        target == 62 -> { // 己方所有后排角色
            result.getRow2ByRole(skillOwner)
        }
        target == 63 -> { // 自己所在横排的所有己方角色
            result.getMyRowRoles(skillOwner)
        }
        target == 64 -> { // 自己所在纵列的所有己方角色
            result.getMyColumnRoles(skillOwner)
        }
        target == 65 -> { // 己方随机一个前排角色
            result.getRow1ByRole(skillOwner).shuffled(RANDOM).take(1)
        }
        target == 66 -> { // 己方随机一个后排角色
            result.getRow2ByRole(skillOwner).shuffled(RANDOM).take(1)
        }
        target == 67 -> { // 己方随机2个角色不重复
            result.getMyTeamRoles(skillOwner).shuffled(RANDOM).take(2)
        }
        target == 68 -> { // 己方随机3个角色不重复
            result.getMyTeamRoles(skillOwner).shuffled(RANDOM).take(3)
        }
        target == 69 -> { // 己方随机4个角色不重复
            result.getMyTeamRoles(skillOwner).shuffled(RANDOM).take(4)
        }
        target == 70 -> { // 己方随机5个角色不重复
            result.getMyTeamRoles(skillOwner).shuffled(RANDOM).take(5)
        }
        target == 71 -> { // 自己所在纵列的另一个角色
            result.getMyColumnRoles(skillOwner).filter { it.playerId() != skillOwner.playerId() }
        }
        target in 10000..19999 -> { // 己方所有拥有某个ID的buff或debuff的角色（xxxx为四位数）
            val buffId = target - 10000
            result.getMyTeamRoles(skillOwner).filter { it.getBuffList().any { it.id == buffId } }
        }

        // 6v6新增
        target == 151 -> { // 敌方所有前排角色
            result.getPeerRow1ByRole(skillOwner).let {
                if (it.isEmpty()) {
                    // 如果同行没有目标，则攻击随机一个敌方后排
                    result.getPeerRow2ByRole(skillOwner).shuffled(RANDOM).take(1)
                } else it
            }
        }
        target == 152 -> { // 敌方所有后排角色
            result.getPeerRow2ByRole(skillOwner).let {
                if (it.isEmpty()) {
                    // 如果同行没有目标，则攻击随机一个敌方前排
                    result.getPeerRow1ByRole(skillOwner).shuffled(RANDOM).take(1)
                } else it
            }
        }
        target == 153 -> { // 敌方目标所在列的后排角色
            result.getPeerColumnBackRole(skillOwner).let {
                if (it.isEmpty()) {
                    // 如果同列没有目标，则攻击peer的后排
                    result.getMyColumnBackRole(peer)
                } else it
            }
        }
        target == 154 -> { // 自己所在纵列的所有敌方角色
            result.getPeerColumnRoles(skillOwner).let {
                if (it.isEmpty()) {
                    // 如果同列没有目标，则攻击peer的整列
                    result.getMyColumnRoles(peer)
                } else it
            }
        }
        target == 155 -> { // 敌方随机一个前排角色
            result.getPeerRow1ByRole(skillOwner).shuffled(RANDOM).take(1)
        }
        target == 156 -> { // 敌方随机一个后排角色
            result.getPeerRow2ByRole(skillOwner).shuffled(RANDOM).take(1)
        }
        target == 157 -> { // 敌方随机2个角色不重复
            result.getPeerTeamRoles(skillOwner).shuffled(RANDOM).take(2)
        }
        target == 158 -> { // 敌方随机3个角色不重复
            result.getPeerTeamRoles(skillOwner).shuffled(RANDOM).take(3)
        }
        target == 159 -> { // 敌方随机4个角色不重复
            result.getPeerTeamRoles(skillOwner).shuffled(RANDOM).take(4)
        }
        target == 160 -> { // 敌方随机5个角色不重复
            result.getPeerTeamRoles(skillOwner).shuffled(RANDOM).take(5)
        }
        target in 20000..29999 -> { // 敌方所有拥有某个ID的buff或debuff的角色（xxxx为四位数）
            val buffId = target - 20000
            result.getPeerTeamRoles(skillOwner).filter { it.getShowBothBuff().any { it.id == buffId } }
        }


        target == 101 && !divideAttack && !frenzy -> { // 目标敌人（单体）
            val enemies = result.getMyTeamRoles(peer)
            enemies.find { it.isTaunt() && !skillOwner.isTauntImmune(this) }?.let { // 有嘲讽的仆从
                listOf(it)
            } ?: listOf(result.getMinion(peer) ?: peer)
        }

        target == 102 -> { // 随机一个敌方角色（包括召唤物）
            result.getPeerTeamRoles(skillOwner).shuffled(RANDOM).firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 103 || (target == 101 && divideAttack) -> { // 敌方所有角色（包括召唤物）
            result.getPeerTeamRoles(skillOwner)
        }

        target == 104 -> { // 随机一个敌方英雄（不包括召唤物）
            result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 105 -> { // 敌方所有英雄（不包括召唤物）
            result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }
        }

        target == 106 -> { // 目标敌人的所有召唤物
            result.getPeerTeamRoles(skillOwner)
                .filter { it.isMinion() && it.masterId() == skillOwner.playerId() }
        }

        target == 107 -> { // 目标敌人的随机一个召唤物
            result.getPeerTeamRoles(skillOwner)
                .filter { it.isMinion() && it.masterId() == skillOwner.playerId() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 108 -> { // 随机一个敌方召唤物
            result.getPeerTeamRoles(skillOwner).filter { it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 109 -> { // 敌方的所有召唤物
            result.getPeerTeamRoles(skillOwner).filter { it.isMinion() }
        }

        target in 111..120 -> { // 敌方所有种族1-10角色（包括召唤物）
            val targetRaceType = target - 110
            result.getPeerTeamRoles(skillOwner).filter { it.getRace().raceType == targetRaceType }
        }
        target in 121..135 -> { // 敌方属性1-15最高的一个角色（不包括召唤物）
            val propertyTarget = target - 120
            listOf(result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.maxBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget)
            })
        }
        target in 136..150 -> { // 敌方属性1-15最低的一个角色（不包括召唤物）
            val propertyTarget = target - 135
            listOf(result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.minBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget)
            })
        }

        target in 721..735 -> { // 敌方属性百分比1-15最高的一个角色（不包括召唤物）
            val propertyTarget = target - 720
            listOf(result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.maxBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget) / it.getDefaultProperty().getPropertyByTarget(propertyTarget)
            })
        }
        target in 736..750 -> { // 敌方属性百分比1-15最低的一个角色（不包括召唤物）
            val propertyTarget = target - 735
            listOf(result.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.minBy {
                it.getCurrentProperty().getPropertyByTarget(propertyTarget) / it.getDefaultProperty().getPropertyByTarget(propertyTarget)
            })
        }

        target == 201 -> { // 双方所有角色（包括召唤物）
            result.getAllRoles()
        }

        target == 202  || (target == 101 && frenzy) -> { // 双方随机一个角色（包括召唤物）
            result.getAllRoles().shuffled(RANDOM).firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 203 -> { // 除自己外的双方所有角色（包括召唤物）
            result.getAllRoles().filter { it.roleIdentifier != skillOwner.roleIdentifier }
        }

        target == 204 -> { // 除自己外的双方随机一个角色（包括召唤物）
            result.getAllRoles().filter { it.roleIdentifier != skillOwner.roleIdentifier }
                .shuffled(RANDOM).firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 205 -> { // 双方所有英雄（不包括召唤物）
            result.getAllRoles().filter { !it.isMinion() }
        }

        target == 206 -> { // 双方随机一个英雄（不包括召唤物）
            result.getAllRoles().filter { !it.isMinion() }.shuffled(RANDOM).firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 207 -> { // 除自己外的双方所有英雄（不包括召唤物）
            result.getAllRoles().filter { !it.isMinion() }
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }
        }

        target == 208 -> { // 除自己外的双方随机一个英雄（不包括召唤物）
            result.getAllRoles().filter { !it.isMinion() }
                .filter { it.roleIdentifier != skillOwner.roleIdentifier }.shuffled(RANDOM)
                .firstOrNull()?.let {
                listOf(it)
            } ?: emptyList()
        }

        target == 209 -> { // 双方所有召唤物
            result.getAllRoles().filter { it.isMinion() }
        }

        target == 210 -> { // 双方随机一个召唤物
            result.getAllRoles().filter { it.isMinion() }.shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 211 -> { // 除自己外的双方所有召唤物
            result.getAllRoles().filter { it.isMinion() && it.masterId() != skillOwner.playerId() }
        }

        target == 212 -> { // 除自己外的双方随机一个召唤物
            result.getAllRoles().filter { it.isMinion() && it.masterId() != skillOwner.playerId() }
                .shuffled(RANDOM)
                .firstOrNull()?.let {
                    listOf(it)
                } ?: emptyList()
        }

        target == 213 -> { // 伤害来源
            triggerSkill?.damageResult?.let {
                result.getRole(it.attacker)?.let { listOf(it) }
            } ?: listOf()
        }

        else -> {
            // 也是8，所有人随机1个
            listOf(result.getAllRoles().shuffled(RANDOM).first())
        }
    }).apply {
//        this.takeIf { it.isEmpty() }?.let {
//            GameCore.instance.onBattleInfo(
//                BattleInfo(
//                    content = skillOwner.getSideName() + AppWrapper.getString(R.string.release1) + <EMAIL>() + AppWrapper.getString(
//                                            R.string.no_target1),
//                    type = BattleInfoType.TriggerSkill,
//                    skill = this@getTargets,
//                    doer = skillOwner
//                )
//            )
//        }
    }
}