package com.moyu.core.logic.battle

import com.moyu.core.debug.CoreDebugConfig
import com.moyu.core.model.GameSpeed
import com.moyu.core.model.environment.Environment
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect

interface BattleCallback {
    fun onBattleInfo(info: BattleInfo)
    fun onBattleEffect(type: SoundEffect)
    fun onToast(string: String)
    fun onTurnBegin()
    suspend fun onBattleEnd(gameOver: Boolean, turn: Int, allies: List<Role>, enemies: List<Role>)
    suspend fun onBattleUIUpdate(battleField: BattleField)
    fun extraIsGameFailed(): Boolean
    fun extraIsGameWin(): Boolean
    fun onCastSkill(skill: Skill)
    fun getDebugConfig(): CoreDebugConfig
    fun gameSpeed(): GameSpeed
    fun onPermanentDiff(target: Role, diff: Property)

    fun onEnvironmentUpdate(environment: Environment)
    fun getUpgradeSkillPool(): List<Pair<String, Skill>>
    fun getLearnedBattleTreeSkillPool(): List<Skill>
    fun getCurrentDifficultLevel(): Int
    fun canLearnSkill(skill: Skill, poolId: Int): Boolean
}