package com.moyu.core.logic.damage.processor

import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageStatus
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

class ElementDamageProcessor(
    override val damageType: DamageType,
    override val attacker: Role,
    override val victim: Role,
    override val initDamage: Int,
    override val skill: Skill,
    override val initDamageStatus: DamageStatus = DamageStatus(),
) : DamageProcessor {
    override fun process(): DamageResult {
        var damageStatus = initDamageStatus.copy()
        val isFatal = attacker.isFatal(skill)

        damageStatus = damageStatus.copy(isFatal = isFatal)
        damageStatus = damageStatus.copy(isDodge = !attacker.isDodgeImmune(skill) && victim.isDodge())
        damageStatus = damageStatus.copy(isImmune = (victim.isThisTurnImmune() || victim.isDamageImmune(damageType)) && !attacker.isImmuneAvoid(skill))
        damageStatus = damageStatus.copy(isHolyShield = victim.hasHolyShield())

        return innerProcess(damageStatus)
    }
}

