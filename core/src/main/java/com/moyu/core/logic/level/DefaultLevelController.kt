package com.moyu.core.logic.level

import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect

/**
 * 经验等级相关model
 */
class DefaultLevelController: LevelController {
    private var level: Int = 1
    private var exp: Long = 0

    override fun setExp(setExp: Long) {
        exp = 0
        level = 1
        gainExp(setExp, true)
    }

    override fun gainExp(gainExp: Long, noSound: Boolean): Int {
//        var targetLevel = level
//        if (isMaxLevel()) return 0
//
//        while (targetLevel < MAX_ROLE_LEVEL && GameCore.instance.getRoleLevelPool()[targetLevel].totalExp <= gainExp + this.exp) {
//            targetLevel++
//        }
//        val levelDiff = targetLevel - level
//        if (targetLevel != level && !noSound) {
//            GameCore.instance.onBattleEffect(SoundEffect.LevelUp)
//        }
//        level = targetLevel
//        exp += gainExp
//        return levelDiff
        return 0
    }

    override fun isMaxLevel(): Boolean {
        return MAX_ROLE_LEVEL == level
    }

    override fun toDataClass(): GameLevelData {
        return GameLevelData(level, exp)
    }

    override fun fromDataClass(gameLevelData: GameLevelData) {
        level = gameLevelData.level
        exp = gameLevelData.exp
    }

    override fun currentExp(): Long {
        return exp
    }

    override fun nextLevelExp(): Long {
        return 0//GameCore.instance.getRoleLevelPool()[level].totalExp
    }

    override fun currentLevelExp(): Long {
        return 0//GameCore.instance.getRoleLevelPool()[level - 1].totalExp
    }

    override fun getLevel(): Int {
        return level
    }

    override fun setLevel(level: Int) {
        this.level = level
    }

    override fun nextLevel() {
        level += 1
    }
}