package com.moyu.core.logic.action

import com.moyu.core.model.action.ActionState
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType

interface ActionHolder {
    fun getStateList(): List<ActionState>
    fun setBeingAttackState(skill: Skill, damage: DamageResult, index: Int)
    fun setBeingBuffState(buff: Buff, skill: Skill, index: Int)
    fun setDispelState(skill: Skill, index: Int)
    fun setDeathState()
    fun setSummonState(skill: Skill, index: Int)
    fun setEnvironmentState(skill: Skill, index: Int)
    fun setBeingHeal(skill: Skill, realHealPoint: HealResult, index: Int)
    fun clearAnimationState()
    fun setDoAttackState(skill: Skill, targets: List<String>, index: Int)
    fun doSkillState(skill: Skill, skillType: SkillEffectType, index: Int)
    fun hasState(beingAttack: ActionStateType): Boolean
    fun getState(beingAttack: ActionStateType): ActionState?
}