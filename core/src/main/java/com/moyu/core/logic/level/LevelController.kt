package com.moyu.core.logic.level

import com.moyu.core.json.LevelSerializer
import kotlinx.serialization.Serializable

const val MAX_ROLE_LEVEL = 200

@Serializable(with = LevelSerializer::class)
interface LevelController {
    fun setExp(setExp: Long)
    fun getLevel(): Int
    fun setLevel(level: Int)
    fun nextLevel()
    fun gainExp(gainExp: Long, noSound: Boolean): Int
    fun isMaxLevel(): Boolean
    fun toDataClass(): GameLevelData
    fun fromDataClass(gameLevelData: GameLevelData)
    fun currentExp(): Long
    fun nextLevelExp(): Long
    fun currentLevelExp(): Long
}