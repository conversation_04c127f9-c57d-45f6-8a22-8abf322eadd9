package com.moyu.core.logic.damage.consumer

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.info.addDamageInfo
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import java.lang.Integer.max

interface DamageConsumer {
    suspend fun consumeDamage(
        field: BattleField,
        damage: DamageResult,
        doer: Role,
        target: Role,
        index: Int
    ): DamageResult
}

fun saveDamageToRole(
    battleField: BattleField,
    doer: Role,
    target: Role,
    damage: DamageResult,
    damageResult: DamageResult,
    index: Int
) {
    // 收到伤害纪录和事件分发
    battleField.saveWoundedResult(target, damageResult)
    battleField.saveDamageResult(doer, damageResult)
    if (target.isDeathAvoid() && !doer.isImmuneAvoid(damage.damageSkill)) {
        // 免死会锁血1
        target.setCurrentHp(
            max(
                1,
                target.getCurrentProperty().hp - damageResult.damageValue.finalDamage
            )
        )
    } else {
        target.setCurrentHp(
            max(
                0,
                target.getCurrentProperty().hp - damageResult.damageValue.finalDamage
            )
        )
    }
//    doer.setDoAttackState(damage.damageSkill, damageResult)
    target.setBeingAttackState(damage.damageSkill, damageResult, index)
    target.reduceHolyShield()
    // 战报处理
    GameCore.instance.addDamageInfo(damageResult, doer, target)
}

fun consumeDamageWithShield(
    damage: Int,
    target: Role,
    damageType: DamageType,
    skill: Skill,
    doer: Role
): Int {
    if (doer.isShieldIgnore(skill)) return damage
    val shield = target.shield(damageType)
    val leftDamage = maxOf(0, damage - shield)
    if (leftDamage > 0) target.removeShield(damageType)
    else target.updateShield(shield - damage, damageType)
    return leftDamage
}

fun consumeDamageWithAllShield(damage: Int, target: Role, skill: Skill, doer: Role): Int {
    if (doer.isShieldIgnore(skill)) return damage
    val allShield = target.allShield()
    val leftDamage = maxOf(0, damage - allShield)
    if (leftDamage > 0) target.removeAllShield()
    else target.updateAllShield(allShield - damage)
    return leftDamage
}