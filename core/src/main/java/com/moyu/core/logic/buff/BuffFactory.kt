package com.moyu.core.logic.buff

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.logic.skill.getReference
import com.moyu.core.model.Buff
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

private val createRandomBuffList = listOf(54, 69, 70)

object BuffFactory {

    fun getCombinedBuff(combinedBuffId: Int): Buff {
        return GameCore.instance.getCombinedBuffPool().first { it.id == combinedBuffId }.copy(combinedId = combinedBuffId)
    }
    fun create(
        skill: Skill,
        skillOwner: Role,
        index: Int,
        field: BattleField,
        skillTrigger: Skill?,
        peer: Role?,
    ): Buff {
        return GameCore.instance.getBuffById(skill.subType[index]).setValue(
            field,
            skillOwner,
            skill,
            index,
            skillTrigger,
            peer
        ).let {
            if (skill.combinedBuffId.getOrElse(index) { 0 } != 0) {
                it.copy(combinedId = skill.combinedBuffId[index])
            } else {
                it
            }
        }
    }

    fun createSpecificBuff(
        skill: Skill,
        buffId: Int
    ): Buff {
        return GameCore.instance.getBuffById(buffId).copy(
            skill = skill,
            continueMaxTurn = 1,
            continueCurrentTurn = 0,
            buffMaxLayer = 1,
            buffCurrentLayer = 1,
            disposable = false
        )
    }

    fun genBuffValue(
        field: BattleField,
        ownerIdentifier: RoleIdentifier,
        referenceIndex: String,
        effectNum: Double,
        skillTrigger: Skill?,
        peer: Role?,
    ): Double {
        field.getRole(ownerIdentifier)?.let {
            val reference = getReference(referenceIndex, field, it, triggerSkill = skillTrigger, target = peer)
            return (effectNum * reference / 100)
        }
        return 0.0
    }
}