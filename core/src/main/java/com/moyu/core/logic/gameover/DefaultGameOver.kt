package com.moyu.core.logic.gameover

import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.logic.identifier.RoleIdentifier

class DefaultGameOver: GameJudge {
    private var gameOver: Boolean = false
    private var terminated: Boolean = false
    private var winFlag = false
    private var winFlagPlayer : RoleIdentifier = NoneRoleIdentifier

    override fun isGameOver(): <PERSON><PERSON><PERSON> {
        return gameOver
    }

    override fun setGameOver() {
        gameOver = true
    }

    override fun addWinFlag(player: RoleIdentifier) {
        winFlag = true
        winFlagPlayer = player
    }

    override fun getWinFlag(): Boolean {
        return winFlag
    }

    override fun getWinFlagPlayer(): RoleIdentifier {
        return winFlagPlayer
    }

    override fun terminate() {
        terminated = true
    }

    override fun terminated(): <PERSON><PERSON><PERSON> {
        return terminated
    }
}