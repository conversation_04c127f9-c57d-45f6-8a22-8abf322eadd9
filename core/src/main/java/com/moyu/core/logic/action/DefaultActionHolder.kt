package com.moyu.core.logic.action

import com.moyu.core.GameCore
import com.moyu.core.logic.buff.AVOID_DEATH
import com.moyu.core.model.Buff
import com.moyu.core.model.action.ActionState
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType
import com.moyu.core.music.SoundEffect
import java.util.concurrent.CopyOnWriteArrayList

fun getEffectAudioByDamageType(damageType: DamageType): SoundEffect {
    return when (damageType) {
        DamageType.DamageType1 -> SoundEffect.Damage1
        DamageType.DamageType2 -> SoundEffect.Damage2
        DamageType.DamageType3 -> SoundEffect.Damage3
        DamageType.DamageType4 -> SoundEffect.Damage4
        else -> SoundEffect.Damage5
    }
}

class DefaultActionHolder : ActionHolder {
    private var stateList = CopyOnWriteArrayList<ActionState>()

    override fun getStateList(): List<ActionState> {
        return stateList
    }

    override fun setBeingAttackState(skill: Skill, damage: DamageResult, index: Int) {
        GameCore.instance.onBattleEffect(getEffectAudioByDamageType(damage.type))
        stateList.add(
            ActionState(
                ActionStateType.BeingAttack,
                skill,
                damage,
                damage.damageValue.finalDamage,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun setBeingBuffState(buff: Buff, skill: Skill, index: Int) {
        val sound = if (buff.id == AVOID_DEATH) SoundEffect.AvoidDeath
        else if (buff.isControl()) SoundEffect.Control
        else if (buff.buffType == 1) SoundEffect.GetBuff else SoundEffect.GetDebuff
        GameCore.instance.onBattleEffect(sound)
        if (hasState(ActionStateType.BeingBuff)) return
        stateList.add(
            ActionState(
                ActionStateType.BeingBuff,
                buff = buff,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun setDispelState(skill: Skill, index: Int) {
        GameCore.instance.onBattleEffect(SoundEffect.Dispel)
        stateList.add(
            ActionState(
                ActionStateType.BeingDispel,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun setDeathState() {
        stateList.add(ActionState(ActionStateType.Death))
    }


    override fun setSummonState(skill: Skill, index: Int) {
        GameCore.instance.onBattleEffect(SoundEffect.Summon)
        stateList.add(
            ActionState(
                ActionStateType.Summoning,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun setEnvironmentState(skill: Skill, index: Int) {
        stateList.add(
            ActionState(
                ActionStateType.Environment,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun setBeingHeal(skill: Skill, realHealPoint: HealResult, index: Int) {
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
        stateList.add(
            ActionState(
                ActionStateType.BeingHeal, skill,
                healResult = realHealPoint,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun clearAnimationState() {
        stateList = CopyOnWriteArrayList<ActionState>()
    }

    override fun setDoAttackState(skill: Skill, targets: List<String>, index: Int) {
        stateList.add(
            ActionState(
                ActionStateType.DoAttack,
                skill = skill,
                targets = targets,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun doSkillState(skill: Skill, skillType: SkillEffectType, index: Int) {
        stateList.add(
            ActionState(
                ActionStateType.DoSkill, skill,
                effectNum = skill.skillEffectNum.getOrNull(index) ?: 0,
                effect = skill.skillEffect.getOrNull(index) ?: "0",
            )
        )
    }

    override fun hasState(beingAttack: ActionStateType): Boolean {
        return stateList.find { it.type == beingAttack } != null
    }

    override fun getState(beingAttack: ActionStateType): ActionState? {
        return stateList.firstOrNull { it.type == beingAttack }
    }
}
