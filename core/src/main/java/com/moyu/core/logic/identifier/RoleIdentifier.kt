package com.moyu.core.logic.identifier

import kotlinx.serialization.Serializable
import java.util.UUID

val NoneRoleIdentifier = Identifier("", "", null)


@Serializable
data class Identifier(
    override val roleId: String,
    override val name: String,
    override val masterId: String?,
) : RoleIdentifier {
    companion object {
        fun player(name: String = "", masterId: String? = null) =
            Identifier("p_" + UUID.randomUUID().toString(), name, masterId)

        fun enemy(name: String = "", masterId: String? = null) =
            Identifier("e_" + UUID.randomUUID().toString(), name, masterId)
    }

    fun identifier(): RoleIdentifier {
        return Identifier(roleId, name, masterId)
    }

    override fun playerId(): String {
        return roleId
    }

    override fun isPlayer(): Boolean {
        return roleId.startsWith("p_") && masterId == null
    }

    override fun isPlayerSide(): Boolean {
        return roleId.startsWith("p_")
    }

    override fun isMinion(): Boolean {
        return masterId != null
    }
}

interface RoleIdentifier {
    val roleId: String
    val name: String
    val masterId: String?

    fun playerId(): String
    fun masterId(): String = masterId ?: ""
    fun isPlayer(): Boolean
    fun getSideName(): String = name
    fun isPlayerSide(): Boolean
    fun isMinion(): Boolean

    fun toStore(): Identifier {
        return Identifier(roleId, name, masterId)
    }

    companion object {
        fun isPlayerSide(key: String): Boolean {
            return key.startsWith("p_") || key.startsWith("q_")
        }

        fun isMinion(key: String): Boolean {
            return key.startsWith("f_") || key.startsWith("q_")
        }
    }
}