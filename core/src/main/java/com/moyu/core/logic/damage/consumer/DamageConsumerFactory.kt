package com.moyu.core.logic.damage.consumer

import com.moyu.core.model.damage.DamageType

object DamageConsumerFactory {
    fun getDamageConsumer(
        damageType: DamageType
    ): DamageConsumer {
        return when(damageType.value) {
            1,2 -> PhysicDamageConsumer(damageType)
            in 3..4 -> ElementDamageConsumer(damageType)
            else -> RealDamageConsumer(damageType)
        }
    }
}