package com.moyu.core.logic.damage.processor

import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageStatus
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

/**
 * 这是一次普通攻击的全部信息
 * 物理伤害值=物理技能系数%×（1+攻击/100）×（1+本技能增伤总和%）×（1+物理增伤总和%）×（1+所有类型增伤总和%）
 */
class PhysicDamageProcessor(
    override val damageType: DamageType,
    override val attacker: Role,
    override val victim: Role,
    override val initDamage: Int,
    override val skill: Skill,
    override val initDamageStatus: DamageStatus,
) : DamageProcessor {

    override fun process(): DamageResult {
        var damageStatus = initDamageStatus.copy()
        val isFatal = attacker.isFatal(skill)

        damageStatus = damageStatus.copy(isFatal = isFatal)
        damageStatus = damageStatus.copy(isDodge = !attacker.isDodgeImmune(skill) && victim.isDodge())
        damageStatus = damageStatus.copy(isImmune = (victim.isThisTurnImmune() || victim.isDamageImmune(damageType)) && !attacker.isImmuneAvoid(skill))
        damageStatus = damageStatus.copy(isHolyShield = victim.hasHolyShield())

        return innerProcess(damageStatus)
    }
}