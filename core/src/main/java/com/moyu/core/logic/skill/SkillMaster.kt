package com.moyu.core.logic.skill

import com.moyu.core.json.SkillMasterSerializer
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEnhancementType
import com.moyu.core.model.Timing
import kotlinx.serialization.Serializable

@Serializable(with = SkillMasterSerializer::class)
interface SkillMaster {
    suspend fun triggerSkillByType(
        field: BattleField,
        triggerType: List<Int>,
        skillOwner: Role,
        triggerSkill: Skill? = null,
        triggerChainEnabled: Boolean = true
    )
    fun learnSkill(skill: Skill, ownerIdentifier: RoleIdentifier)
    fun getSkills(): List<Skill>
    fun setSkills(skills: List<Skill>)
    fun setGraveSkills(skills: List<Skill>)
    fun getGraveSkills(): List<Skill>
    fun addGraveSkill(skill: Skill)
    fun clearGrave(timing: Timing)
    fun getOnlyNormalSkills(): List<Skill>
    fun hasLearnedSkill(skill: Skill): Boolean
    fun enhanceSkill(skill: Skill, enhancementId: SkillEnhancementType, value: Double)
    fun markSkillNewTurn(role: Role)
    fun checkIfAddGraveSkill(skill: Skill, skillOwner: Role): Skill
    fun markReCoolDown(skill: Skill, skillOwner: Role): Skill
    fun clearCoolDown()
    fun replaceSkillsFromPool(role: Role)
    fun forgetSkill(skill: Skill)
    fun oneYearPass()
    fun toPersistData(): SkillData
}