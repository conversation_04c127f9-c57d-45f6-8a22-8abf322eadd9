package com.moyu.core.logic.info

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.logic.skill.getNameInfo
import com.moyu.core.logic.skill.isDot
import com.moyu.core.logic.skill.isHot
import com.moyu.core.model.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill


fun Int.getElementTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.skill_typ1)
        2 -> AppWrapper.getString(R.string.skill_type2)
        3 -> AppWrapper.getString(R.string.skill_type3)
        4 -> AppWrapper.getString(R.string.skill_type4)
        else -> AppWrapper.getString(R.string.skill_type5)
    }
}

fun GameCore.addInfo(info: String, type: BattleInfoType) {
    onBattleInfo(BattleInfo(info, type = type))
}

fun GameCore.addTurnBeginInfo(role: Role, turn: Int) {
    if (role.isPlayer()) {
        addInfo(
            (if (turn == 1) "" else "\n") + "> " + role.getSideName() + AppWrapper.getString(R.string.ones_turn) + turn,
            type = BattleInfoType.Battle
        )
    } else {
        addInfo(
            "\n> " + role.getSideName() + AppWrapper.getString(R.string.ones_turn) + turn,
            type = BattleInfoType.Battle
        )
    }
}

fun GameCore.addBanInfo(role: Role, elementType: Int) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.ban_skill_tip1) + elementType.getElementTypeName() + AppWrapper.getString(
            R.string.ban_skill_tip2
        ),
        BattleInfoType.Battle
    )
}


fun GameCore.addPalsyInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.palsy_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addFrozenInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.silent_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addDisarmInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.disarm_tips),
        BattleInfoType.Battle
    )
}

fun GameCore.addFrenzyInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.frenzy_tips), BattleInfoType.Battle
    )
}

fun GameCore.addForbidHealInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.forbid_heal_tip), BattleInfoType.Battle
    )
}

fun GameCore.addDoubleSkillInfo(role: Role) {
    addInfo(
        role.getSideName() + AppWrapper.getString(R.string.trigger_multi_cast),
        BattleInfoType.Battle
    )
}

fun GameCore.addControlImmuneInfo(newBuff: Buff) {
    onBattleInfo(
        BattleInfo(
            AppWrapper.getString(R.string.control_immune) + newBuff.name, type = BattleInfoType.Buff
        )
    )
}

fun GameCore.addHealInfo(skill: Skill, doer: Role, target: Role, heal: Int) {
    val content =
        if (skill.isHot()) skill.getNameInfo() + AppWrapper.getString(R.string.let) + target.getSideName() + AppWrapper.getString(
            R.string.heal
        ) + heal
        else doer.getSideName() + AppWrapper.getString(R.string.release) + skill.getNameInfo() + "," + AppWrapper.getString(
            R.string.let
        ) + target.getSideName() + AppWrapper.getString(R.string.heal) + heal + AppWrapper.getString(
            R.string.hp
        )
    onBattleInfo(
        BattleInfo(
            skill = skill,
            content = content,
            type = BattleInfoType.Heal,
            doer = doer,
            target = target
        )
    )
}

fun GameCore.addDamageInfo(damage: DamageResult, doer: Role, target: Role) {
    val skill = damage.damageSkill
    val damageValue = damage.damageValue.finalDamage
    val content = when {
        skill.isDot() -> skill.buffInfo!!.getNameInfo() + AppWrapper.getString(R.string.let) + target.getSideName() + AppWrapper.getString(
            R.string.lost
        ) + damageValue + AppWrapper.getString(R.string.hp)

        else -> doer.getSideName() + AppWrapper.getString(R.string.release) + skill.getNameInfo() + "," + AppWrapper.getString(
            R.string.let
        ) + target.getSideName() + AppWrapper.getString(R.string.lost) + damageValue + AppWrapper.getString(
            R.string.hp
        )
    }
    onBattleInfo(
        BattleInfo(
            skill = skill,
            content = content,
            type = BattleInfoType.Damage,
            doer = doer,
            target = target,
            damageData = damage
        )
    )
}

fun GameCore.addExtraSkillInfo(content: String, skill: Skill) {
    onBattleInfo(
        BattleInfo(
            content = AppWrapper.getString(R.string.you_release) + skill.getNameInfo() + if (content.isNotEmpty()) ",$content" else "",
            type = BattleInfoType.ExtraSkill,
            skill = skill,
        )
    )
}

fun GameCore.addSkillCastInfo(
    skillOwner: Role, content: String, skill: Skill
) {
    onBattleInfo(
        BattleInfo(
            content = skillOwner.getSideName() + AppWrapper.getString(R.string.release) + skill.getNameInfo() + content,
            type = BattleInfoType.TriggerSkill,
            skill = skill,
            doer = skillOwner
        )
    )
}

fun GameCore.addBuffInfo(skillOwner: Role, skill: Skill, innerTarget: Role, buff: Buff) {
    onBattleInfo(
        BattleInfo(
            content = skillOwner.getSideName() + AppWrapper.getString(R.string.release) + skill.getNameInfo() + innerTarget.getSideName() + AppWrapper.getString(
                R.string.gain
            ) + buff.getNameInfo(),
            type = BattleInfoType.TriggerSkill,
            skill = skill,
            doer = skillOwner
        )
    )
}


fun GameCore.addFailMaxTurn() {
    addInfo(
        AppWrapper.getString(R.string.over_turn), type = BattleInfoType.Battle
    )
}