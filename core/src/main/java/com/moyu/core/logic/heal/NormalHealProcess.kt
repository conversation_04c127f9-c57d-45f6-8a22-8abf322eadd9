package com.moyu.core.logic.heal

import com.moyu.core.GameCore
import com.moyu.core.logic.info.addForbidHealInfo
import com.moyu.core.logic.skill.isSuckBlock
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.heal.HealStatus
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlin.math.max
import kotlin.math.roundToInt

class NormalHealProcess(
    override val attacker: Role,
    override val victim: Role,
    override val skill: Skill,
    override val initHeal: Int,
    override val healStatus: HealStatus = HealStatus()
) : HealProcessor {
    override fun process(): HealResult {
        val finalHeal: Int = if (victim.isHealForbidden()) {
            GameCore.instance.addForbidHealInfo(victim)
            0
        } else {
            max(1, (initHeal.toDouble() * (1 + attacker.getHealRate())).roundToInt())
        }
        val status = if (skill.isSuckBlock()) healStatus.copy(isSuckBlood = true) else healStatus
        return HealResult(status, initHeal, "", skill, finalHeal, victim = victim.roleIdentifier)
    }
}