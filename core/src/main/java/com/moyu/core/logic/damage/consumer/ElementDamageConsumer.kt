package com.moyu.core.logic.damage.consumer

import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.damage.DamageValue
import com.moyu.core.model.role.Role

class ElementDamageConsumer(val damageType: DamageType) : DamageConsumer {
    override suspend fun consumeDamage(
        field: BattleField,
        damage: DamageResult,
        doer: Role,
        target: Role,
        index: Int
    ): DamageResult {
        val leftDamage = consumeDamageWithShield(
            damage.rawDamage,
            target,
            damageType,
            damage.damageSkill,
            doer
        )
        val finalDamage = consumeDamageWithAllShield(leftDamage, target, damage.damageSkill, doer)
        val damageResult = damage.copy(
            damageValue = DamageValue(
                finalDamage = finalDamage,
                normalShieldBlockedDamage = damage.rawDamage - leftDamage,
                allShieldBlockedDamage = leftDamage - finalDamage,
            )
        )

        saveDamageToRole(field, doer, target, damage, damageResult, index)

        // 技能吸血处理，注意保存攻击者恢复的血量
        doer.suckBloodRate(damage.damageSkill).takeIf { it > 0 }?.let {
            if (!target.isSuckBloodImmune() && !doer.isDeath()) {
                field.doSuckBlood(
                    (damageResult.damageValue.finalDamage * it).toInt(),
                    doer,
                    doer,
                    field,
                    index
                )
            }
        }
        return damageResult.checkSelfHarm(field, doer, target)
    }
}