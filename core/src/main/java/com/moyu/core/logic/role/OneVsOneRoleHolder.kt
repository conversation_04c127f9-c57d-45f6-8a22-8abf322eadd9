package com.moyu.core.logic.role

import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.role.Role
import com.moyu.core.util.RANDOM
// 通过hashMap来定义位置
const val ALLY_ROW1_FIRST = 0
const val ALLY_ROW1_SECOND = 1
const val ALLY_ROW1_THIRD = 2
const val ALLY_ROW1_FOURTH = 3
const val ALLY_ROW2_FIRST = 4
const val ALLY_ROW2_SECOND = 5
const val ALLY_ROW2_THIRD = 6
const val ALLY_ROW2_FOURTH = 7

const val ENEMY_ROW1_FIRST = 10
const val ENEMY_ROW1_SECOND = 11
const val ENEMY_ROW1_THIRD = 12
const val ENEMY_ROW1_FOURTH = 13
const val ENEMY_ROW2_FIRST = 14
const val ENEMY_ROW2_SECOND = 15
const val ENEMY_ROW2_THIRD = 16
const val ENEMY_ROW2_FOURTH = 17

val positionList = listOf(
    ALLY_ROW1_FIRST,
    ALLY_ROW1_SECOND,
    ALLY_ROW1_THIRD,
    ALLY_ROW1_FOURTH,
    ALLY_ROW2_FIRST,
    ALLY_ROW2_SECOND,
    ALLY_ROW2_THIRD,
    ALLY_ROW2_FOURTH,
    ENEMY_ROW1_FIRST,
    ENEMY_ROW1_SECOND,
    ENEMY_ROW1_THIRD,
    ENEMY_ROW1_FOURTH,
    ENEMY_ROW2_FIRST,
    ENEMY_ROW2_SECOND,
    ENEMY_ROW2_THIRD,
    ENEMY_ROW2_FOURTH
)


val positionListAllies = listOf(
    ALLY_ROW1_FIRST,
    ALLY_ROW1_SECOND,
    ALLY_ROW1_THIRD,
    ALLY_ROW1_FOURTH,
    ALLY_ROW2_FIRST,
    ALLY_ROW2_SECOND,
    ALLY_ROW2_THIRD,
    ALLY_ROW2_FOURTH,
)

// 第一排中间放前面，选出最强的放中间
val positionOrderedListAllies = listOf(
    ALLY_ROW1_SECOND,
    ALLY_ROW1_FIRST,
    ALLY_ROW1_THIRD,
    ALLY_ROW1_FOURTH,
    ALLY_ROW2_FIRST,
    ALLY_ROW2_SECOND,
    ALLY_ROW2_THIRD,
    ALLY_ROW2_FOURTH,
)

val positionListEnemy = listOf(
    ENEMY_ROW1_FIRST,
    ENEMY_ROW1_SECOND,
    ENEMY_ROW1_THIRD,
    ENEMY_ROW1_FOURTH,
    ENEMY_ROW2_FIRST,
    ENEMY_ROW2_SECOND,
    ENEMY_ROW2_THIRD,
    ENEMY_ROW2_FOURTH,
)

val battleEnemyNoMasterList = listOf(
    ENEMY_ROW1_FIRST,
    ENEMY_ROW2_FIRST,
    ENEMY_ROW1_SECOND,
    ENEMY_ROW1_THIRD,
    ENEMY_ROW2_THIRD,
    ENEMY_ROW1_FOURTH,
    ENEMY_ROW2_FOURTH,
)

const val HERO_POSITION = ALLY_ROW2_SECOND
const val ENEMY_HERO_POSITION = ENEMY_ROW2_SECOND

class OneVsOneRoleHolder(private val roles: MutableMap<Int, Role?>) : GameRoleHolder {
    override fun getRole(roleIdentifier: RoleIdentifier): Role? {
        return getAllRoles().filterNot { it.isOver() }
            .firstOrNull { it.playerId() == roleIdentifier.playerId() }
    }

    override fun getRoleMap(): Map<Int, Role?> {
        return roles.toMap()
    }
    override fun getAllRoles(): List<Role> {
        return roles.values.mapNotNull { it }.filterNot { it.isOver() }
    }

    override fun randomRole(): Role {
        return roles.values.mapNotNull { it }.filterNot { it.isOver() }.shuffled(RANDOM).first()
    }

    override fun getEnemies(): List<Role> {
        return roles.values.mapNotNull { it }.filterNot { it.isOver() }.filterNot { it.isPlayerSide() }
    }

    override fun getPlayers(): List<Role> {
        return roles.values.mapNotNull { it }.filterNot { it.isOver() }.filter { it.isPlayerSide() }
    }

    override fun getAllPlayers(): List<Role> {
        return roles.values.mapNotNull { it }.filter { it.isPlayerSide() }
    }

    override fun getAllEnemies(): List<Role> {
        return roles.values.mapNotNull { it }.filterNot { it.isPlayerSide() }
    }

    override fun getPlayersRow1(): List<Role?> {
        return listOf(
            roles[ALLY_ROW1_FIRST],
            roles[ALLY_ROW1_SECOND],
            roles[ALLY_ROW1_THIRD],
            roles[ALLY_ROW1_FOURTH],
        )
    }

    override fun getPlayersRow2(): List<Role?> {
        return listOf(
            roles[ALLY_ROW2_FIRST],
            roles[ALLY_ROW2_SECOND],
            roles[ALLY_ROW2_THIRD],
            roles[ALLY_ROW2_FOURTH],
        )
    }

    override fun getEnemiesRow1(): List<Role?> {
        return listOf(
            roles[ENEMY_ROW1_FIRST],
            roles[ENEMY_ROW1_SECOND],
            roles[ENEMY_ROW1_THIRD],
            roles[ENEMY_ROW1_FOURTH],
        )
    }

    override fun getEnemiesRow2(): List<Role?> {
        return listOf(
            roles[ENEMY_ROW2_FIRST],
            roles[ENEMY_ROW2_SECOND],
            roles[ENEMY_ROW2_THIRD],
            roles[ENEMY_ROW2_FOURTH],
        )
    }

    override fun getMyRowRoles(role: Role): List<Role> {
        return (if (role.isPlayerSide()) {
            if (getPlayersRow1().mapNotNull { it }.any { it.playerId() == role.playerId() }) getPlayersRow1().mapNotNull { it } else getPlayersRow2().mapNotNull { it }
        } else {
            if (getEnemiesRow1().mapNotNull { it }.any { it.playerId() == role.playerId() }) getEnemiesRow1().mapNotNull { it } else getEnemiesRow2().mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getMyColumnRoles(role: Role): List<Role> {
        val indexOfYou = getIndexOfRole(role)
        return (if (role.isPlayerSide()) {
            listOf(
                roles[ALLY_ROW1_FIRST + indexOfYou],
                roles[ALLY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        } else {
            listOf(
                roles[ENEMY_ROW1_FIRST + indexOfYou],
                roles[ENEMY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getPeerColumnBackRole(role: Role): List<Role> {
        val indexOfYou = getIndexOfRole(role)
        return (if (role.isPlayerSide()) {
            listOf(
                roles[ENEMY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        } else {
            listOf(
                roles[ALLY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getMyColumnBackRole(role: Role): List<Role> {
        val indexOfYou = getIndexOfRole(role)
        return (if (role.isPlayerSide()) {
            listOf(
                roles[ALLY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        } else {
            listOf(
                roles[ENEMY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getPeerColumnRoles(role: Role): List<Role> {
        val indexOfYou = getIndexOfRole(role)
        return (if (role.isPlayerSide()) {
            listOf(
                roles[ENEMY_ROW1_FIRST + indexOfYou],
                roles[ENEMY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        } else {
            listOf(
                roles[ALLY_ROW1_FIRST + indexOfYou],
                roles[ALLY_ROW2_FIRST + indexOfYou],
            ).mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getRow1ByRole(role: Role): List<Role> {
        return (if (role.isPlayerSide()) {
            getPlayersRow1().mapNotNull { it }
        } else {
            getEnemiesRow1().mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getRow2ByRole(role: Role): List<Role> {
        return (if (role.isPlayerSide()) {
            getPlayersRow2().mapNotNull { it }
        } else {
            getEnemiesRow2().mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getPeerRow1ByRole(role: Role): List<Role> {
        return (if (role.isPlayerSide()) {
            getEnemiesRow1().mapNotNull { it }
        } else {
            getPlayersRow1().mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getPeerRow2ByRole(role: Role): List<Role> {
        return (if (role.isPlayerSide()) {
            getEnemiesRow2().mapNotNull { it }
        } else {
            getPlayersRow2().mapNotNull { it }
        }).filter { !it.isOver() }
    }

    override fun getMyTeamRoles(attacker: RoleIdentifier): List<Role> {
        return if (attacker.isPlayerSide()) getPlayers() else getEnemies()
    }

    override fun getPeerTeamRoles(attacker: RoleIdentifier): List<Role> {
        return if (attacker.isPlayerSide()) getEnemies() else getPlayers()
    }

    override fun getMinion(role: Role): Role? {
        return getAllRoles().filterNot { it.isOver() }
            .firstOrNull { it.masterId() == role.playerId() }
    }

    override fun addMinion(role: Role) {
        if (role.isPlayerSide()) {
            positionListAllies.forEach {
                if (roles[it] == null) {
                    roles[it] = role
                    return
                }
            }
        } else {
            positionListEnemy.forEach {
                if (roles[it] == null) {
                    roles[it] = role
                    return
                }
            }
        }
    }

    private fun getIndexOfRole(role: Role): Int {
        return if (role.isPlayerSide()) {
            getPlayersRow1().indexOfFirst { role.playerId() == it?.playerId() }.takeIf { it >= 0 } ?: getPlayersRow2().indexOfFirst { role.playerId() == it?.playerId() }
        } else {
            getEnemiesRow1().indexOfFirst { role.playerId() == it?.playerId() }.takeIf { it >= 0 } ?: getEnemiesRow2().indexOfFirst { role.playerId() == it?.playerId() }
        }
    }

    override fun getDirectPeer(role: Role): Role? {
        val indexOfYou = getIndexOfRole(role)
        val firstRowTarget = if (role.isPlayerSide()) {
            roles[ENEMY_ROW1_FIRST + indexOfYou]?.takeIf { !it.isOver() }
        } else {
            roles[ALLY_ROW1_FIRST + indexOfYou]?.takeIf { !it.isOver() }
        }
        val secondRowTarget = if (role.isPlayerSide()) {
            roles[ENEMY_ROW2_FIRST + indexOfYou]?.takeIf { !it.isOver() }
        } else {
            roles[ALLY_ROW2_FIRST + indexOfYou]?.takeIf { !it.isOver() }
        }
        val otherWiseTarget = if (role.isPlayerSide()) {
            getEnemiesRow1().mapNotNull { it }.firstOrNull { !it.isOver() } ?: getEnemiesRow2().mapNotNull { it }.firstOrNull { !it.isOver() }
        } else {
            getPlayersRow1().mapNotNull { it }.firstOrNull { !it.isOver() } ?: getPlayersRow2().mapNotNull { it }.firstOrNull { !it.isOver() }
        }
        val peer = firstRowTarget ?: secondRowTarget

        return if (peer == null) {
            // 不存在对面
            otherWiseTarget
        } else {
            // 存在
            if (peer.isOver()) {
                // 但是已经死了
                otherWiseTarget
            } else {
                getMyMaster(peer) ?: peer
            }
        }
    }

    override fun getMyMaster(role: Role): Role? {
        return getAllRoles().firstOrNull { it.playerId() == role.masterId() }
    }

    override fun getMyMinion(role: Role): Role? {
        return getAllRoles().firstOrNull { it.masterId() == role.playerId() }
    }
}