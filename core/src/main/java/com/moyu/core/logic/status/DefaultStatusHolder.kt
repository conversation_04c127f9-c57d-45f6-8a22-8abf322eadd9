package com.moyu.core.logic.status

class DefaultStatusHolder: StatusHolder {
    private var _deathChecked = false
    private var _actioned = false
    private var _over = false
    override fun isDeathChecked(): Boolean {
        return _deathChecked
    }

    override fun setDeathChecked(value: <PERSON><PERSON><PERSON>) {
        _deathChecked = value
    }

    override fun isActioned(): <PERSON><PERSON><PERSON> {
        return _actioned
    }

    override fun setActioned(value: <PERSON><PERSON><PERSON>) {
        _actioned = value
    }

    override fun isOver(): <PERSON><PERSON><PERSON> {
        return _over
    }

    override fun setOver(value: Boolean) {
        _over = value
    }
}