package com.moyu.core.logic.battle

import com.moyu.core.logic.gameover.GameJudge
import com.moyu.core.logic.recorder.GameDataRecorder
import com.moyu.core.logic.role.GameRoleHolder
import com.moyu.core.logic.turn.GameTurnHolder

interface BattleHolder :
    <PERSON><PERSON>udge,
    GameDataRecorder,
    GameTurnHolder,
    GameRoleHolder {
    suspend fun startBattle()
    suspend fun nextStep()
    fun getBattleField(): BattleField
}