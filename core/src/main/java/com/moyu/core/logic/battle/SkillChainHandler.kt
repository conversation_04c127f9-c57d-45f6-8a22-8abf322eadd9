package com.moyu.core.logic.battle

import com.moyu.core.model.skill.Skill

/**
 * 如果释放一个技能后，需要执行触发链，就是利用这个handler实现的
 * example:
 * triggerSkillHandler.invoke(SelfHarm.copy(ownerIdentifier = doer), field)
 * 这样，就可以把'自伤'事件发出去，对应的技能就会被自动触发执行
 */
val skillChainHandler: suspend (Skill, BattleField) -> Unit by lazy {
    { skillTriggered, battleField ->
        val passiveTurn = battleField.isPassiveTurn()
        // 仆从可能死亡了，所以这里要过滤下
        if (battleField.getAllRoles()
                .find { it.playerId() == skillTriggered.ownerIdentifier.playerId() } != null
        ) {
            battleField.getAllRoles().forEach {
                if (!battleField.gameFinished()) {
                    it.triggerSkillByType(
                        field = battleField,
                        triggerType = if (passiveTurn) listOf(2) else listOf(1, 2),
                        skillOwner = it,
                        triggerSkill = skillTriggered,
                    )
                }
            }
        }
    }
}