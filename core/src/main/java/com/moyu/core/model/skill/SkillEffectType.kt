package com.moyu.core.model.skill

@JvmInline
value class SkillEffectType(val type: Int) {
    fun isOtherSkill(): Boolean {
        return type >= normalBuff.type && type <= genEffect.type
    }
}

val directDamage = SkillEffectType(1)
val directHeal = SkillEffectType(2)
val normalBuff = SkillEffectType(3)
val permanentBuff = SkillEffectType(4)
val specialEffect = SkillEffectType(5)
val genEffect = SkillEffectType(6)
val enhancementSkill = SkillEffectType(7)