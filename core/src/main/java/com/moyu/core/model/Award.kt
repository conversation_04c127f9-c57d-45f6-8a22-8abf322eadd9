package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.perPlusI
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

// todo 新增国家，要改下面两个定义
val EMPTY_REPUTATION = listOf(
    0, 0, 0, 0, 0, 0, 0, 0
)
// todo 新增资源，要改下面两个定义
val EMPTY_RESOURCES = listOf(
    0, 0, 0, 0, 0, 0, 0, 0
)

// todo Award不会作为局内的数据序列化，所以可以屏蔽一些非关键数据
@Serializable
data class Award(
    @Transient
    val code: String = "",
    @Transient
    val message: String = "",
    @Transient
    val allies: List<Ally> = emptyList(),
    @Transient
    val loseAllies: List<Ally> = emptyList(),
    val outAllies: List<Ally> = emptyList(),
    val skills: List<Skill> = emptyList(),
    @Transient
    val upgradeSkills: List<Pair<String, Skill>> = emptyList(), // 英雄无敌废弃
    @Transient
    val upgradeAllies: List<Ally> = emptyList(),
    @Transient
    val loseSkills: List<Skill> = emptyList(),
    @Transient
    val equips: List<Equipment> = emptyList(),
    @Transient
    val advProperty: AdventureProps = EMPTY_ADV_PROPS,
    @Transient
    val battleProperty: Property = EMPTY_PROPERTY,
    @Transient
    val reputations: List<Int> = EMPTY_REPUTATION,
    @Transient
    val resources: List<Int> = EMPTY_RESOURCES,
    val diamond: Int = 0,
    val pvpDiamond: Int = 0,
    val pvpScore: Int = 0,
    val pvp2Score: Int = 0,
    val key: Int = 0,
    val realMoney: Int = 0,
    val lotteryMoney: Int = 0,
    val couponAlly: Int = 0,
    val couponHero: Int = 0,
    @Transient
    val titleId: Int = 0,
    @Transient
    val titleLevel: Int = 0,
    val warPass: Int = 0,
    val warPass2: Int = 0,
    val warPass3: Int = 0,
    val electric: Int = 0,
    val holidayMoney: Int = 0,
    @Transient
    val exp: Int = 0,
    @Transient
    val allHeal: Int = 0,
    @Transient
    val battleHeal: Int = 0,
    val roadExp: Int = 0,
    @Transient
    val extraDiamond: Int = 0,// 仅vip显示，实际已经加入money
    @Transient
    val extraKey: Int = 0, // 仅vip显示，实际已经加入exp
    val unlockList: List<Int> = emptyList(),
    val sellId: Int = 0,
    @Transient
    val showQuestion: Boolean = false,
    @Transient
    val valid: Boolean = false,
) {
    operator fun plus(singleAward: Award): Award {
        return copy(
            equips = mutableListOf<Equipment>().apply {
                addAll(equips)
                addAll(singleAward.equips)
            },
            skills = mutableListOf<Skill>().apply {
                addAll(skills)
                addAll(singleAward.skills)
            },
            upgradeSkills = mutableListOf<Pair<String, Skill>>().apply {
                addAll(upgradeSkills)
                addAll(singleAward.upgradeSkills)
            },
            upgradeAllies = mutableListOf<Ally>().apply {
                addAll(upgradeAllies)
                addAll(singleAward.upgradeAllies)
            },
            loseSkills = mutableListOf<Skill>().apply {
                addAll(loseSkills)
                addAll(singleAward.loseSkills)
            },
            allies = mutableListOf<Ally>().apply {
                addAll(allies)
                addAll(singleAward.allies)
            },
            loseAllies = mutableListOf<Ally>().apply {
                addAll(loseAllies)
                addAll(singleAward.loseAllies)
            },
            outAllies = mutableListOf<Ally>().apply {
                addAll(outAllies)
                addAll(singleAward.outAllies)
            },
            unlockList = mutableListOf<Int>().apply {
                addAll(unlockList)
                addAll(singleAward.unlockList)
            },
            reputations = reputations.perPlusI(singleAward.reputations),
            resources = resources.perPlusI(singleAward.resources),
            key = key + singleAward.key,
            lotteryMoney = lotteryMoney + singleAward.lotteryMoney,
            realMoney = realMoney + singleAward.realMoney,
            couponAlly = couponAlly + singleAward.couponAlly,
            couponHero = couponHero + singleAward.couponHero,
            titleId = titleId + singleAward.titleId,
            titleLevel = titleLevel + singleAward.titleLevel,
            diamond = diamond + singleAward.diamond,
            pvpDiamond = pvpDiamond + singleAward.pvpDiamond,
            holidayMoney = holidayMoney + singleAward.holidayMoney,
            pvpScore = pvpScore + singleAward.pvpScore,
            pvp2Score = pvp2Score + singleAward.pvp2Score,
            allHeal = allHeal + singleAward.allHeal,
            battleHeal = battleHeal + singleAward.battleHeal,
            electric = electric + singleAward.electric,
            warPass = warPass + singleAward.warPass,
            warPass2 = warPass2 + singleAward.warPass2,
            warPass3 = warPass3 + singleAward.warPass3,
            exp = exp + singleAward.exp,
            roadExp = roadExp + singleAward.roadExp,
            extraDiamond = extraDiamond + singleAward.extraDiamond,
            extraKey = extraKey + singleAward.extraKey,
            advProperty = advProperty + singleAward.advProperty,
            battleProperty = battleProperty + singleAward.battleProperty,
            showQuestion = showQuestion || singleAward.showQuestion,
            sellId = if (sellId == 0) singleAward.sellId else sellId
        )
    }

    fun isEmpty(): Boolean {
        return this == Award()
    }

    operator fun unaryMinus(): Award {
        return this.copy(
            loseAllies = allies,
            allies = emptyList(),
            loseSkills = skills,
            skills = emptyList(),
            key = -key,
            lotteryMoney = -lotteryMoney,
            couponAlly = -couponAlly,
            couponHero = -couponHero,
            realMoney = -realMoney,
            diamond = -diamond,
            pvpDiamond = -pvpDiamond,
            pvpScore = -pvpScore,
            pvp2Score = -pvp2Score,
            exp = -exp,
            allHeal = -allHeal,
            battleHeal = -battleHeal,
            reputations = reputations.map { -it },
            resources = resources.map { -it },
            advProperty = EMPTY_ADV_PROPS - advProperty,
            battleProperty = EMPTY_PROPERTY - battleProperty,
        )
    }

    fun recreate(): Award {
        return copy(outAllies = this.outAllies.map {
            GameCore.instance.getAllyById(it.id).copy(num = it.num)
        })
    }

    fun merge(): Award {
        val realOutAllies = mutableListOf<Ally>()
        outAllies.groupBy { it.id }.forEach {
            val ally = it.value.first()
            val size = it.value.sumOf { it.num }
            realOutAllies.add(ally.copy(num = size))
        }
        return copy(outAllies = realOutAllies)
    }

    fun multipleHeroAndAlly(multiple: Int): Award {
        return copy(
            outAllies = outAllies.map { it.copy(num = it.num * multiple) },
            allies = allies.map { it.copy(num = it.num * multiple) },
        )
    }

    fun isGift(): Boolean {
        return sellId != 0 && sellId in GameCore.instance.getGiftPool().map { it.id }
    }

    fun isMonthCard(): Boolean {
        return sellId in listOf(9520001,
            9520002,
            9520003)
    }
}
