package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import com.moyu.core.R

enum class DamageType(val display: String, val defenseDisplay: String, val defenseName: String, val value: Int) {
    DamageType1(AppWrapper.getString(R.string.damage1), AppWrapper.getString(R.string.defense1),  AppWrapper.getString(R.string.defense_name1),1),
    DamageType2(AppWrapper.getString(R.string.damage2), AppWrapper.getString(R.string.defense2),  AppWrapper.getString(R.string.defense_name2), 2),
    DamageType3(AppWrapper.getString(R.string.damage3), AppWrapper.getString(R.string.defense2),  AppWrapper.getString(R.string.defense_name3), 3),
    DamageType4(AppWrapper.getString(R.string.damage4), AppWrapper.getString(R.string.defense2),  AppWrapper.getString(R.string.defense_name4), 4),
    DamageType5(AppWrapper.getString(R.string.damage5), AppWrapper.getString(R.string.defense2),  AppWrapper.getString(R.string.defense_name5), 5),;

    companion object {
        fun fromTypeValue(type: Int): DamageType? {
            return entries.firstOrNull { it.value == type }
        }

        fun fromDisplayValue(display: String): DamageType? {
            return entries.firstOrNull { it.display == display }
        }
    }

    fun getDamageName(): String {
        return when (this) {
            DamageType1 -> DamageType1.display
            DamageType2 -> DamageType2.display
            DamageType3 -> DamageType3.display
            DamageType4 -> DamageType4.display
            DamageType5 -> DamageType5.display
        }
    }
}