package com.moyu.core.model.property

import com.moyu.core.AppWrapper
import com.moyu.core.R
import kotlinx.serialization.Serializable

val EMPTY_ADV_PROPS = AdventureProps()

@Serializable
data class AdventureProps(
    val age: Int = 0,
    // 冒险属性
    val science: Int = 0,
    val politics: Int = 0,
    val military: Int = 0,
    val religion: Int = 0,
    val commerce: Int = 0,
    val art: Int = 0,
    val population: Int = 0,

    val gainExp: Int = 0,
    val gainMoney: Int = 0,
    val gainReputation: Int = 0,
    val shopPriceMercenary: Int = 0,
    val winEventPlayIds: List<Int> = emptyList(),
    val failEventPlayIds: List<Int> = emptyList()
) {
    companion object {
        fun getRoleProperty(type: Int, diff: Int): AdventureProps {
            return when (type) {
                1 -> AdventureProps(science = diff)
                2 -> AdventureProps(politics = diff)
                3 -> AdventureProps(military = diff)
                4 -> AdventureProps(religion = diff)
                5 -> AdventureProps(commerce = diff)
                6 -> AdventureProps(art = diff)
                7 -> AdventureProps(population = diff)
                else -> AdventureProps()
            }
        }

        fun createNew(): AdventureProps {
            return AdventureProps()
        }
    }

    operator fun plus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science + diffProperty.science,
            politics = politics + diffProperty.politics,
            military = military + diffProperty.military,
            religion = religion + diffProperty.religion,
            commerce = commerce + diffProperty.commerce,
            age = age + diffProperty.age,
            art = art + diffProperty.art,
            population = population + diffProperty.population,


            gainExp = gainExp + diffProperty.gainExp,
            gainMoney = gainMoney + diffProperty.gainMoney,
            gainReputation = gainReputation + diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary + diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds + diffProperty.winEventPlayIds,
            failEventPlayIds = failEventPlayIds + diffProperty.failEventPlayIds,
        )
    }

    operator fun minus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science - diffProperty.science,
            politics = politics - diffProperty.politics,
            military = military - diffProperty.military,
            religion = religion - diffProperty.religion,
            commerce = commerce - diffProperty.commerce,
            age = age - diffProperty.age,
            art = art - diffProperty.art,
            population = population - diffProperty.population,

            gainExp = gainExp - diffProperty.gainExp,
            gainMoney = gainMoney - diffProperty.gainMoney,
            gainReputation = gainReputation - diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary - diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds - diffProperty.winEventPlayIds.toSet(),
            failEventPlayIds = failEventPlayIds - diffProperty.failEventPlayIds.toSet(),
        )
    }


    operator fun times(times: Int): AdventureProps {
        return this.copy(
            science = science * times,
            politics = politics * times,
            military = military * times,
            commerce = commerce * times,
            religion = religion * times,
            art = art * times,
            population = population * times,
        )
    }

    fun isNotEmpty(): Boolean {
        return this != EMPTY_ADV_PROPS
    }

    fun getPropertyByTarget(propertyEnum: Int): Int {
        return when (propertyEnum) {
            1 -> {
                science
            }

            2 -> {
                politics
            }

            3 -> {
                military
            }

            4 -> {
                religion
            }

            5 -> {
                commerce
            }

            6 -> {
                art
            }

            7 -> {
                population
            }

            else -> {
                error("无效effectReference")
            }
        }
    }

    fun perBiggerI(property: AdventureProps): Boolean {
        return this.science >= property.science &&
                this.politics >= property.politics &&
                this.military >= property.military &&
                this.religion >= property.religion &&
                this.commerce >= property.commerce &&
                this.art >= property.art &&
                this.population >= property.population
    }

    fun ensureNonNegative(): AdventureProps {
        return copy(
            science = science.coerceAtLeast(0),
            politics = politics.coerceAtLeast(0),
            military = military.coerceAtLeast(0),
            religion = religion.coerceAtLeast(0),
            commerce = commerce.coerceAtLeast(0),
            art = art.coerceAtLeast(0),
            population = population,
        )
    }

    fun ensureStartPopulation(): AdventureProps {
        return copy(
            population = population.coerceAtLeast(1),
        )
    }

    fun getValueByIndex(indexFromZero: Int): Int {
        return when (indexFromZero) {
            0 -> science
            1 -> politics
            2 -> military
            3 -> religion
            4 -> commerce
            5 -> art
            6 -> population
            else -> error("无效属性")
        }
    }

    operator fun compareTo(advProperty: AdventureProps): Int {
        return (if (science >= advProperty.science) 1 else -999) + (if (politics >= advProperty.politics) 1 else -999) +
                (if (military >= advProperty.military) 1 else -999) + (if (religion >= advProperty.religion) 1 else -999) +
                (if (commerce >= advProperty.commerce) 1 else -999) + (if (art >= advProperty.art) 1 else -999) +
                (if (population >= advProperty.population) 1 else -999)
    }

    fun getNonZeroString(): String {
        val result = StringBuilder()

        if (science != 0) {
            result.append("${AppWrapper.getString(R.string.prop1)}$science")
        }
        if (politics != 0) {
            if (result.isNotEmpty()) result.append(", ") // 添加分隔符
            result.append("${AppWrapper.getString(R.string.prop2)}$politics")
        }
        if (military != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.prop3)}$military")
        }
        if (religion != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.prop4)}$religion")
        }
        if (commerce != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.prop5)}$commerce")
        }
        if (art != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.prop6)}$art")
        }
        return if (result.isEmpty()) "" else result.toString()
    }
}
