package com.moyu.core.model.damage

import com.moyu.core.logic.battle.skillChainHandler
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.role.Role
import com.moyu.core.logic.skill.SelfHarm
import com.moyu.core.model.skill.Skill

data class DamageResult(
    val type: DamageType,
    val damageStatus: DamageStatus = DamageStatus(),
    val rawDamage: Int = 0,
    val damageInfo: String = "",
    val damageSkill: Skill,
    val damageValue: DamageValue = DamageValue(),
    val attacker: RoleIdentifier,
    val victim: RoleIdentifier,
) {
    suspend fun checkSelfHarm(field: BattleField, doer: Role, target: Role): DamageResult {
        if (doer.isPlayer() == target.isPlayer()) {
            if (damageValue.finalDamage > 0) {
                skillChainHandler.invoke(
                    SelfHarm.copy(ownerIdentifier = doer),
                    field
                )
            }
        }
        return this
    }

    fun getDesc(): String {
        return damageStatus.getDesc()
    }
}
