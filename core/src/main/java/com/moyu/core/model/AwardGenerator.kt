package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdvProfession
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleProfession
import com.moyu.core.model.skill.isHalo
import com.moyu.core.model.skill.isMagic
import com.moyu.core.util.RANDOM
import java.util.Random


// todo 装备提供的魔法白名单PoolId
val whiteList = listOf(250381,250382,250383,250384,250385, 250391,250392,250393,250394,250395, 250401,250402,250403,250404,250405, 250411,250412,250413,250414,250415, 25047)
val gainSkillPools = listOf(250381,250382,250383,250384,250385,250391,250392,250393,250394,250395,250401,250402,250403,250404,250405,250411,250412,250413,250414,250415,260161,260162,260163,260261,260262,260263,25038,25039,25040,25041,290081,290082,290083,290084,290271,290272,290273,290274,290275,290471,290472,290473,290474,290475,290571,290661,290662,290663,290664,290665,290861,290862,290863,290864,290865,291051,291052,291053,291054,291055,291241,291242,291243,291451,291452,291453,291651,291652,291653,291654,291655,1091751,81006159,81006160,81006161,81006162,81006193,81006194,81006195,81006196,81006197,81006229,81006230,81006231,81006232,81006233,81006264,81006265,81006266,81006267,81006268,81006301,81006302,81006303,81006304,81006305,81006336,81006337,81006338,81006339,81006340,81006371,81006372,81006373,81006406,81006407,81006408,81006440,81006441,81006442,81006443,81006444,250381,250382,250383,250384,250385,250391,250392,250393,250394,250395,250401,250402,250403,250404,250405,250411,250412,250413,250414,250415)
val eventGainSkillPools = listOf(81006159,81006160,81006161,81006162,81006193,81006194,81006195,81006196,81006197,81006229,81006230,81006231,81006232,81006233,81006264,81006265,81006266,81006267,81006268,81006301,81006302,81006303,81006304,81006305,81006336,81006337,81006338,81006339,81006340,81006371,81006372,81006373,81006406,81006407,81006408,81006440,81006441,81006442,81006443,81006444)

// 废弃矿井poolId
val feiQiKuangJingPoolIds = listOf(81004691,81004692,81004693,81004694,81004695,81004696,81004697,81004698,81004699,81004700,81004701,81004702,81004703,81004704,81004705,81004706,81004707,81004708,81004709,81004710,81004711,81004712,81004713,81004714,81004715,81004716,81004717,81004718,81004719,81004720,81004721,81004722)
// 盗贼之家poolId
val daoZeiZhiJiaPoolIds = listOf(81002033,81002034,81002035,81002036,81002037,81002038,81002039,81002040,81002041,81002042,81002043,81002044,81002045,81002046,81002047,81002048,81002049,81002050,81002051,81002052,81002053,81002054,81002055,81002056,81002057,81002058,81002059,81002060,81002061,81002062,81002063,81002064,81002065)

fun ReputationLevel.toAward(typeIndex: Int): Award {
    return if (awardId(typeIndex) == 0) Award() else GameCore.instance.getPoolById(awardId(typeIndex))
        .toAward()
}

fun Sign.toAward(): Award {
    return GameCore.instance.getPoolById(this.reward).toAward()
}

fun Sell.toAward(): Award {
    val pool = GameCore.instance.getPoolById(itemId)
    return pool.toAward() + if (isAifadian()) {
        // 爱发电商品，需要默认加入一个电量
        Award(electric = price)
    } else Award()
}

fun Sell.toAwards(): List<Award> {
    val pool = GameCore.instance.getPoolById(itemId)
    return pool.toAwards()
}

fun List<Award>.toAward(): Award {
    return this.reduceOrNull { acc, award -> acc + award } ?: Award()
}

fun Pool.toAward(forceNum: Int? = null, random: Random = RANDOM): Award {
    return toAwards(forceNum, random).toAward()
}


fun Vip.toAward(): Award {
    return if (effectType == 1) {
        GameCore.instance.getPoolById(effectId).toAward(effectNum)
    } else {
        Award()
    }
}

fun BattlePass.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}

fun Pool.toAwards(
    forceNum: Int? = null,
    random: Random = RANDOM,
    onLoseAlly: (() -> List<Ally>)? = null,
    onLoseSkill: (() -> List<Skill>)? = null
): List<Award> {
    // todo 解决女巫小屋重复技能的bug
    val tempSkills: MutableList<Int> = mutableListOf()
    val result = List(type.size) { index ->
        val realNum = forceNum ?: num[index]
        when (type[index]) {
            1 -> {
                if (pool[index].isEnum()) {
                    Award(
                        allies = (onLoseAlly?.invoke() ?: GameCore.instance.getAllyPool())
                            .filter { it.star == 1 && !it.isHero() }
                            .filter { it.getRaceType() == pool[index] || pool[index] == 0 }
                            .filter { quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }
                            .shuffled(random).take(enumNum.first()).map {
                                it.copy(num = if (realNum == 0) 1 else realNum)
                            }
                    )
                } else {
                    Award(
                        allies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            2 -> {
                if (pool[index].isEnum()) {
                    Award(
                        allies = (onLoseAlly?.invoke() ?: GameCore.instance.getAllyPool())
                            .filter { it.star == 1 && it.isHero() }
                            .filter { it.getRaceType() == pool[index] || pool[index] == 0 }
                            .filter { quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        allies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            3 -> {
                Award(resources = EMPTY_RESOURCES.toMutableList().apply {
                    set(pool[index] - 1, realNum)
                })
            }

            4 -> {
                // 【魔法】
                if (pool[index].isEnum()) {
                    val skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                        .filter { it.isMagic() }
                        .filter { quality[index] == 0 || it.quality() == quality[index] }
                        .filter { it.elementType == pool[index] || pool[index] == 0 }
                        .filter { GameCore.instance.canLearnSkill(it, this.id) }
                        .filter { it.id !in tempSkills}
                        .shuffled(random).take(enumNum.first()).map {
                            it.copy(num = if (realNum == 0) 1 else realNum)
                        }
                    skills.firstOrNull()?.let {
                        tempSkills.add(it.id)
                    }
                    Award(skills = skills)
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        ).filter { GameCore.instance.canLearnSkill(it, this.id) }
                    )
                }
            }

            5 -> {
                // 【专业】
                if (pool[index].isEnum()) {
                    val skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                        .filter { it.isBattleProfession() || it.isAdvProfession() }
                        .filter { quality[index] == 0 || it.quality() == quality[index] }
                        .filter { it.elementType == pool[index] || pool[index] == 0 }
                        .filter { GameCore.instance.canLearnSkill(it, this.id) }
                        .filter { it.id !in tempSkills}
                        .shuffled(random).take(enumNum.first()).map {
                            it.copy(num = if (realNum == 0) 1 else realNum)
                        }
                    skills.firstOrNull()?.let {
                        tempSkills.add(it.id)
                    }
                    Award(skills = skills)
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        ).filter { GameCore.instance.canLearnSkill(it, this.id) }
                    )
                }
            }

            6 -> {
                // 宝物，也就是装备
                if (pool[index].isEnum()) {
                    var result = Award()
                    while (result.equips.size < realNum) {
                        result += Award(
                            equips = GameCore.instance.getEquipPool()
                                .filter { it.star == 1 }
                                .filter { quality[index] == 0 || it.quality == quality[index] }
                                .filter { if (pool[index] < 0) it.type != -pool[index] else if (pool[index] == 0) true else it.type == pool[index] }
                                .filter { it.dropLimit != 1 }
//                                .filter { it.belong == 0 || it.belong in GameCore.instance.getStoryIds() }
                                .shuffled(random).take(realNum - result.equips.size)
                        )
                    }
                    result
                } else {
                    val ally = GameCore.instance.getEquipById(pool[index])
                    val allies = List(realNum) { ally }
                    Award(equips = allies)
                }
            }

            7 -> {
                // 【主角战斗属性】
                Award(battleProperty = Property.getPropertyByEnum(pool[index], realNum.toDouble()))
            }

            8 -> {
                // 【冒险技能卡】
                if (pool[index].isEnum()) {
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isAdventure() }
                            .filter { quality[index] == 0 || it.quality() == quality[index] }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }
            9 -> {
                // 【冒险技能卡】
                if (pool[index].isEnum()) {
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isAdventure() }
                            .filter { quality[index] == 0 || it.quality() == quality[index] }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }
            10 -> {
                Award(allHeal = realNum)
            }

            11 -> {
                Award(exp = realNum)
            }

            12 -> {
                // halo
                if (pool[index].isEnum()) {
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isHalo() }
                            .filter { it.level == 0 }
                            .filter { quality[index] == 0 || it.quality() == quality[index] }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            13 -> {
                Award(titleLevel = realNum)
            }

            14 -> {
                val mainId = pool[index]
                val allies = GameCore.instance.getAllyPool().filter { it.mainId == mainId }
                Award(upgradeAllies = allies)
            }

            15 -> {
                if (pool[index].isEnum()) {
                    val skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                        .filter { quality[index] == 0 || it.quality() == quality[index] }
                        .filter { it.elementType == pool[index] || pool[index] == 0 }
                        .filter { GameCore.instance.canLearnSkill(it, this.id) }
                        .filter { it.id !in tempSkills}
                        .shuffled(random).take(enumNum.first()).map {
                            it.copy(num = if (realNum == 0) 1 else realNum)
                        }
                    skills.firstOrNull()?.let {
                        tempSkills.add(it.id)
                    }
                    Award(skills = skills)
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        ).filter { GameCore.instance.canLearnSkill(it, this.id) }
                    )
                }
            }

            21 -> {
                Award(diamond = realNum)
            }

            22 -> {
                Award(key = realNum)
            }

            23 -> {
                Award(couponAlly = realNum)
            }

            24 -> {
                Award(couponHero = realNum)
            }

            25 -> {
                // 局外兵种卡
                if (pool[index].isEnum()) {
                    Award(
                        outAllies = GameCore.instance.getAllyPool().asSequence()
                            .filter { it.star == 1 && !it.isHero() }
                            .filter { pool[index] == 0 || it.getRaceType() == pool[index] }
                            .filter { quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }.toList()
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        outAllies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            26 -> {
                // 局外英雄卡
                if (pool[index].isEnum()) {
                    Award(
                        outAllies = GameCore.instance.getAllyPool().asSequence()
                            .filter { it.star == 1 && it.isHero() }
                            .filter { pool[index] == 0 || it.getRaceType() == pool[index] }
                            .filter { quality[index] == 0 || it.quality == quality[index] }
                            .filter { it.dropLimit != 1 }.toList()
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        outAllies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            27 -> {
                Award(electric = realNum)
            }

            28 -> {
                Award(warPass = realNum)
            }

            29 -> {
                Award(roadExp = realNum)
            }

            30 -> {
                Award(unlockList = listOf(pool[index]))
            }

            31 -> {
                Award(warPass2 = realNum)
            }

            32 -> {
                Award(warPass3 = realNum)
            }

            33 -> {
                Award(pvpDiamond = realNum)
            }

            34 -> {
                Award(pvpScore = realNum)
            }

            35 -> {
                Award(lotteryMoney = realNum)
            }
            36 -> {
                Award(holidayMoney = realNum)
            }
            else -> {
                Award()
//                error("不存在的枚举${type[index]}")
            }
        }
    }
    return result.filter { !it.isEmpty() }.let {
        // 如果总数太多，要随机，否则固定
        if (it.size <= totalNum) it else it.shuffled(random).take(totalNum)
    }
}

// todo pool表的第二个字段有双重含义，如果是100以内，是枚举，否则是固定id
fun Int.isEnum(): Boolean {
    return this < 100
}

fun Quest.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}