package com.moyu.core.model

import kotlinx.serialization.Serializable
import com.moyu.core.model.ConfigData

data class Dialog(
    override val id: Int = 0,
    val mainId: Int = 0,
    val type: Int = 0,
    val desc: String = "",
    val answer1: String = "",
    val answer2: String = "",
    val answer3: String = "",
    val correct: Int = 0,
    val npcPic: String = "",
    val npcName: String = "",
    val condition: List<Int> = emptyList(),
): ConfigData {
    fun isDungeonDialog(): Boolean {
        return type == 1 && mainId != 1
    }

    fun isMovieDialog(): Boolean {
        return mainId == 1
    }

    fun isQuestionDialog(): Boolean {
        return type == 3
    }

    fun isAfter(): Boolean {
        // dungeon结束对话
        return id % 1000 > 100
    }

    fun isAnswerBegin(): Boolean {
        return id == 10000
    }

    fun isAnswerCorrect(): Boolean {
        return id == 19999
    }

    fun isAnswerWrong(): Boolean {
        return id == 20000
    }
}