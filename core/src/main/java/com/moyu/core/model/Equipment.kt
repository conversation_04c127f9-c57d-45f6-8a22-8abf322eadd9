package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID

@Serializable
data class Equipment(
    override val id: Int = 0,
    @Transient val mainId: Int = 0,
    @Transient val name: String = "",
    @Transient val requestId: Int = 0,
    @Transient val requestNum: Int = 0,
    @Transient val type: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Int = 0,
    @Transient val attribute5: Int = 0,
    @Transient val attribute6:Int = 0,
    @Transient val attribute7: Int = 0,
    @Transient val attribute8: Double = 0.0,
    @Transient val attribute9: Double = 0.0,
    @Transient val attribute10: Double = 0.0,
    @Transient val attribute11: Int = 0,
    @Transient val star: Int = 0,
    @Transient val starLimit: Int = 0,
    @Transient val quality: Int = 0,
    @Transient val skillEffect: Int = 0,
    @Transient val starUpNum: Int = 0,
    @Transient val starUpResourceNum: Int = 0,
    @Transient val dropLimit: Int = 0,
    @Transient val story: String = "",
    @Transient val belong: Int = 0,
    @Transient val pic: String = "",
    @Transient val peek: Boolean = false,
    @SerialName("x") val life: Int = 0,
    override val new: Boolean = true,
    @SerialName("w") override val uuid: String = ""
): GameItem, ConfigData {
    override fun create(): Equipment {
        return GameCore.instance.getEquipPool().first { it.id == id }.copy(
            uuid = this.uuid.takeIf { it.isNotEmpty() } ?: UUID.generateUUID().toString(),
            new = new,
            life = life,
        )
    }

    override fun setUnNew(): Equipment {
        return copy(new = false)
    }

    fun getProperty(): Property {
        val defense = listOf(attribute2, attribute3, attribute4, attribute5, attribute6)
        return Property(
            attack = attribute1,
            defenses = defense,
            hp = attribute7,
            fatalRate = attribute8,
            fatalDamage = attribute9,
            dodgeRate = attribute10,
            speed = attribute11
        )
    }

    fun nextYear(): Equipment {
        return copy(life = life + 1)
    }

    fun starUp(): Equipment {
        return GameCore.instance.getEquipPool()
            .firstOrNull { it.mainId == mainId && it.star == star + 1 }
            ?.copy(
                life = life,
                uuid = uuid,
            ) ?: this
    }

    fun getSkill(): Skill? {
        return GameCore.instance.getSkillPool().firstOrNull { it.id == skillEffect }
    }

    fun getNextLevelEquip(): Equipment {
        return GameCore.instance.getEquipPool()
            .firstOrNull { it.mainId == mainId && it.star == star + 1 }?: this
    }

    fun isEquipReplaceable(): Boolean {
        // todo 前8个都是装备，后面是法宝
        return type <= 8
    }

    fun isSameAttributes(targetEquip: Equipment): Boolean {
        return attribute1 == targetEquip.attribute1 &&
                attribute2 == targetEquip.attribute2 &&
                attribute3 == targetEquip.attribute3 &&
                attribute4 == targetEquip.attribute4 &&
                attribute5 == targetEquip.attribute5 &&
                attribute6 == targetEquip.attribute6 &&
                attribute7 == targetEquip.attribute7 &&
                attribute8 == targetEquip.attribute8 &&
                attribute9 == targetEquip.attribute9 &&
                attribute10 == targetEquip.attribute10 &&
                attribute11 == targetEquip.attribute11
    }
}