package com.moyu.core.model

import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Story(
    val id: Int,
    @Transient
    val name: String = "",
    @Transient
    val unlockId: Int = 0,
    @Transient
    val desc: String = "",
    @Transient
    val pic: String = "",
    @Transient
    val showCard: List<Int> = emptyList(),
    @Transient
    val selected: Boolean = false,
    @Transient
    val peek: Boolean = false,
) {
    fun switchSelection(): Story {
        return copy(selected = !selected)
    }
}
