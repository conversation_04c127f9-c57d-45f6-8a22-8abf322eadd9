package com.moyu.core.model.role

import com.moyu.core.GameCore
import com.moyu.core.logic.action.ActionHolder
import com.moyu.core.logic.action.DefaultActionHolder
import com.moyu.core.logic.buff.BuffCarrier
import com.moyu.core.logic.buff.DefaultBuffCarrier
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.logic.info.addBanInfo
import com.moyu.core.logic.info.addDisarmInfo
import com.moyu.core.logic.info.addFrozenInfo
import com.moyu.core.logic.info.addPalsyInfo
import com.moyu.core.logic.level.DefaultLevelController
import com.moyu.core.logic.level.LevelController
import com.moyu.core.logic.property.DefaultPropertyHolder
import com.moyu.core.logic.property.PropertyHolder
import com.moyu.core.logic.race.DefaultRaceHolder
import com.moyu.core.logic.race.RaceHolder
import com.moyu.core.logic.role.BattlePower
import com.moyu.core.logic.role.RoleComposer
import com.moyu.core.logic.skill.DefaultSkillMaster
import com.moyu.core.logic.skill.SkillMaster
import com.moyu.core.logic.status.DefaultStatusHolder
import com.moyu.core.logic.status.StatusHolder
import com.moyu.core.model.Ally
import com.moyu.core.model.damage.DamageType
import com.moyu.core.util.perMultiI
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.serialization.json.Json

/**
 * 角色model
 */
@Serializable
data class Role(
    @Transient
    val property: PropertyHolder = DefaultPropertyHolder(),
    val raceHolder: RaceHolder = DefaultRaceHolder(),
    val roleLevel: LevelController = DefaultLevelController(),
    val extraInfo: RoleExtraInfo = RoleExtraInfo(),
    val skillMaster: SkillMaster = DefaultSkillMaster(),
    @Transient
    val status: StatusHolder = DefaultStatusHolder(),
    val roleIdentifier: Identifier = NoneRoleIdentifier,
    @Transient
    val actionHolder: ActionHolder = DefaultActionHolder(),
    @Transient
    val buffCarrier: BuffCarrier = DefaultBuffCarrier(),
    @Transient
    val roleComposer: RoleComposer = RoleComposer(property, buffCarrier),
    @Transient
    val updateId: Long = 0L, // 如果所有的值都是不可变，则无法更新动画(Compose无法感知变化)
) : LevelController by roleLevel,
    ActionHolder by actionHolder,
    PropertyHolder by property,
    RaceHolder by raceHolder,
    BuffCarrier by buffCarrier,
    RoleIdentifier by roleIdentifier,
    SkillMaster by skillMaster,
    BattlePower by roleComposer,
    StatusHolder by status {

    fun setPropertyToDefault(debuff: Int = 100) {
        var targetProperty = getInitProperty() * (debuff / 100.0)
        if (GameCore.instance.getDebugConfig().hp100) {
            targetProperty = targetProperty.copy(hp = targetProperty.hp * 100)
        }
        if (GameCore.instance.getDebugConfig().attack100) {
            targetProperty = targetProperty.copy(
                attack = targetProperty.attack * 100,
            )
        }
        if (GameCore.instance.getDebugConfig().defense100) {
            targetProperty = targetProperty.copy(
                defenses = targetProperty.defenses.perMultiI(100),
            )
        }
        if (GameCore.instance.getDebugConfig().dodge100) {
            targetProperty = targetProperty.copy(
                dodgeRate = 100.0
            )
        }
        if (GameCore.instance.getDebugConfig().fatal100) {
            targetProperty = targetProperty.copy(
                fatalRate = 100.0,
                fatalDamage = 200.0
            )
        }
        setAllProperty(targetProperty)
    }

    fun checkStatusCanActiveSkill(addBattleInfo: Boolean = true): Boolean {
        if (isFrozen()) {
            if (addBattleInfo) {
                GameCore.instance.addFrozenInfo(this)
            }
            return false
        }
        return true
    }

    fun checkStatusCanNormalAttack(addBattleInfo: Boolean = true): Boolean {
        if (isDisarm()) {
            if (addBattleInfo) {
                GameCore.instance.addDisarmInfo(this)
            }
            return false
        }
        return true
    }

    fun checkStatusCanTriggerSkill(addBattleInfo: Boolean = true): Boolean {
        if (isPalsy()) {
            if (addBattleInfo) {
                GameCore.instance.addPalsyInfo(this)
            }
            return false
        }
        return true
    }

    fun checkCanElementSkill(elementType: Int, addInfo: Boolean): Boolean {
        if (isBanSkill(elementType)) {
            if (addInfo) {
                GameCore.instance.addBanInfo(this, elementType)
            }
            return false
        }
        return true
    }

    fun totalShield(): Int {
        return shield(DamageType.DamageType1) + shield(DamageType.DamageType2) + shield(DamageType.DamageType3) + shield(
            DamageType.DamageType4
        ) + shield(DamageType.DamageType5) + allShield()
    }

    fun double(): Role {
        val jsonString = Json.encodeToString(serializer(), this)
        return Json.decodeFromString(serializer(), jsonString)
    }

    fun getAlly(): Ally {
        return GameCore.instance.getAllyById(getRace().id)
    }
}