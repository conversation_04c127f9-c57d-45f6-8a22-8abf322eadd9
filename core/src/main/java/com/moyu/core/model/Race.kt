package com.moyu.core.model

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.model.property.Property
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

const val RACE_SIZE = 11

fun Int.getRaceTreeTypeName(): String {
    return "[" + getRaceTypeName() + AppWrapper.getString(R.string.skill_tree) + "]"
}

fun Int.getRaceTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.race1)
        2 -> AppWrapper.getString(R.string.race2)
        3 -> AppWrapper.getString(R.string.race3)
        4 -> AppWrapper.getString(R.string.race4)
        else -> AppWrapper.getString(R.string.race5)
    }
}

fun Int.getRaceGroupName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.group1)
        2 -> AppWrapper.getString(R.string.group2)
        3 -> AppWrapper.getString(R.string.group3)
        4 -> AppWrapper.getString(R.string.group4)
        5 -> AppWrapper.getString(R.string.group5)
        6 -> AppWrapper.getString(R.string.group6)
        7 -> AppWrapper.getString(R.string.group7)
        8 -> AppWrapper.getString(R.string.group8)
        9 -> AppWrapper.getString(R.string.group9)
        10 -> AppWrapper.getString(R.string.group10)
        else -> AppWrapper.getString(R.string.group11)
    }
}

@Serializable
data class Race(
    override val id: Int = 1,
    @Transient
    val roleId: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val raceType: Int = 0,
    @Transient
    val raceType2: Int = 0,
    @Transient
    val level: Int = 0,
    @Transient
    val star: Int = 0,
    @Transient
    val quality: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Int = 0,
    @Transient val attribute5: Int = 0,
    @Transient val attribute6: Int = 0,
    @Transient val attribute7: Int = 0,
    @Transient val attribute8: Double = 0.0,
    @Transient val attribute9: Double = 0.0,
    @Transient val attribute10: Double = 0.0,
    @Transient val attribute11: Int = 0,

    @Transient
    val skillId: List<Int> = emptyList(),
    @Transient
    val randomSkillId: List<Int> = emptyList(),
    @Transient
    val banSkillId: List<Int> = emptyList(),
    @Transient
    val randomSkillNum: List<Int> = emptyList(),
    @Transient
    val pic: String = "",
    @Transient
    val story: String = "",
): ConfigData {
    fun getProperty(): Property {
        val defense = listOf(attribute2, attribute3, attribute4, attribute5, attribute6)
        return Property(
            attack = attribute1,
            defenses = defense,
            hp = attribute7,
            fatalRate = attribute8,
            fatalDamage = attribute9,
            dodgeRate = attribute10,
            speed = attribute11
        )
    }

    /**
     * 获取种族的国家索引，返回0-N
     */
    fun getRaceCountryIndex(): Int? {
        return skillId.firstOrNull { it in 4201..4209 }?.let {
            it - 4201
        }
    }

    fun getHeadIcon(): String {
        return pic// + "_headicon"
    }

    fun isMelee(): Boolean {
        return raceType !in listOf(2,4,5)
    }

    fun getAlly() = GameCore.instance.getAllyById(id)
}