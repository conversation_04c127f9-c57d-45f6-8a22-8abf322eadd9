package com.moyu.core.model

import com.moyu.core.model.property.Property

data class Tower(
    val id: Int,
    val layer: Int,
    val type: List<Int>,
    val playPara1: List<Int>,
    val playPara2: Int,
    val eventAttribute1: Int,
    val eventAttribute2: Int,
    val eventAttribute3: Int,
    val eventAttribute4: Int,
    val eventAttribute5: Int,
    val eventAttribute6: Int,
    val eventAttribute7: Double,
    val eventAttribute8: Double,
    val eventAttribute9: Double,
    val eventAttribute10: Double,
    val eventAttribute11: Int,
    val reward: Int,
) {
    fun getDiffProperty(): Property {
        return Property(
            attack = eventAttribute1, // todo
            defenses = listOf(eventAttribute2, eventAttribute3, eventAttribute4, eventAttribute5, eventAttribute6),
            hp = eventAttribute7.toInt(),
            fatalRate = eventAttribute8,
            fatalDamage = eventAttribute9,
            dodgeRate = eventAttribute10,
            speed = eventAttribute11,
        )
    }
}