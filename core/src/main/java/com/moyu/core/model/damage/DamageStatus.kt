package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import com.moyu.core.R


data class DamageStatus(
    val isFatal: <PERSON>olean = false,
    val isRage: <PERSON>olean = false,
    val isDodge: <PERSON>olean = false,
    val isImmune: <PERSON><PERSON><PERSON> = false,
    val isHolyShield: <PERSON>olean = false,
    val isSelfHarm: Boolean = false
) {
    fun getDesc(): String {
        return when {
            isRage -> AppWrapper.getString(R.string.rage)
            isImmune -> AppWrapper.getString(R.string.unbreakable)
            isHolyShield -> AppWrapper.getString(R.string.holy_shield)
            isDodge -> AppWrapper.getString(R.string.dodge)
            isFatal -> AppWrapper.getString(R.string.fatal)
            else -> ""
        }
    }

}