package com.moyu.core.model

import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import java.lang.Integer.max
import kotlin.math.min

fun Int.getStar(): Int {
    return this % 100
}

fun Int.allyIdToMainId(): Int {
    return this / 100
}

fun Int.allyIdIsHero(): Boolean {
    return this.allyIdToMainId() < 1000
}

@Serializable
data class Ally(
    @SerialName("x")
    val id: Int,
    @Transient
    val mainId: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val star: Int = 0,
    @Transient
    val starLimit: Int = 0,
    @Transient
    val quality: Int = 0,
    @Transient
    val type: Int = 0,
    @Transient
    val skillNum: Int = 0,
    @Transient
    val starUpNum: Int = 0,
    @Transient
    val starUpRes: Int = 0,
    @Transient
    val dropLimit: Int = 0,
    @Transient
    val story: String = "",
    @Transient
    val belong: Int = 0,
    @Transient
    val up: Int = 0,
    @SerialName("z")
    val num: Int = 1,
    override val new: Boolean = true,
    @SerialName("u")
    val selected: Boolean = false,
    @SerialName("uxx")
    val selectedTime: Long = 0,
    // 商店信息
    @Transient
    val peek: Boolean = false,
    // 局内信息
    @SerialName("o")
    val equipSkills: List<Skill> = emptyList(),
    @SerialName("r")
    val gameHp: Int = 100, // 0是死亡，改为百分比，100%是满血
    @SerialName("p")
    val exerciseProperty: Property? = null,
    @SerialName("c")
    val extraInfo: String = "", // 用来标记特殊文案，比如协助战斗，保护对象
    @SerialName("a")
    val battlePosition: Int = -1, // 是否参战
    @SerialName("b")
    val inGame: Boolean = false, // 仅局内
    @SerialName("ks")
    val temp: Boolean = false, // 战斗后移除
    @SerialName("l")
    val roleIdentifier: Identifier = NoneRoleIdentifier,
    @SerialName("w")
    override val uuid: String = ""
) : GameItem {
    /**
     * 选中去游戏中，是一个新的实例，带uuid，和局外军团卡无关
     */
    fun copyToGame(): Ally {
        return copy(
            uuid = UUID.generateUUID().toString(),
            roleIdentifier = Identifier.player(name),
            inGame = true
        )
    }

    fun switchSelect(): Ally {
        return copy(selected = !selected,
            selectedTime = System.currentTimeMillis(),
            uuid = uuid.ifEmpty { UUID.generateUUID().toString() })
    }

    fun relive(): Ally {
        return copy(gameHp = 100)
    }

    fun hurt(percent: Int): Ally {
        return copy(gameHp = max(gameHp - percent, 1))
    }

    fun heal(percent: Int): Ally {
        return copy(gameHp = min(gameHp + percent, 100))
    }

    fun isDead(): Boolean {
        return gameHp <= 0
    }

    fun isHurt(): Boolean {
        return gameHp > 0 && gameHp != 100
    }

    fun getRaceType(): Int {
        return GameCore.instance.getRaceById(id).raceType
    }

    fun getRaceType2(): Int {
        return GameCore.instance.getRaceById(id).raceType2
    }

    fun starUp(): Ally {
        val starUp =
            GameCore.instance.getAllyPool()
                .firstOrNull { it.mainId == mainId && it.star == star + 1 }
        return starUp?.copy(
            num = num,
            selected = selected,
            selectedTime = selectedTime,
            equipSkills = equipSkills,
            gameHp = 100,
            exerciseProperty = exerciseProperty,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        ) ?: this
    }

    fun switchSelectToBattle(position: Int): Ally {
        return copy(
            battlePosition = position,
        )
    }

    override fun create(): Ally {
        return GameCore.instance.getAllyPool().first { it.id == id }.copy(
            num = this.num,
            selected = this.selected,
            selectedTime = this.selectedTime,
            uuid = this.uuid,
            roleIdentifier = Identifier.player(name),
            new = false,
            equipSkills = equipSkills.map {
                it.create()
            },
            gameHp = gameHp,
            exerciseProperty = exerciseProperty,
            extraInfo = extraInfo,
            battlePosition = battlePosition,
            inGame = inGame,
        )
    }

    override fun setUnNew(): GameItem {
        return copy(new = false)
    }

    fun getRace(): Race {
        return GameCore.instance.getRaceById(id)
    }

    fun isHero(): Boolean {
        return this.mainId < 1000
    }

    fun upgrade(): Ally {
        val upgraded = GameCore.instance.getAllyPool().first { it.mainId == this.up && it.star == this.star }
        return upgraded.copy(
            num = num,
            selected = selected,
            selectedTime = selectedTime,
            equipSkills = equipSkills,
            gameHp = 100,
            exerciseProperty = exerciseProperty,
            roleIdentifier = roleIdentifier,
            battlePosition = battlePosition,
            uuid = uuid,
        )
    }

    fun isAlly7(): Boolean {
        return !isHero() && quality == 7
    }

    fun isAlly6(): Boolean {
        return isHero() && quality >= 6
    }

    fun isImbaAlly(): Boolean {
        return mainId == 1178	//无畏机甲
    }
}