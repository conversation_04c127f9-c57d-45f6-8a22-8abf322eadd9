package com.moyu.core.model.skill

data class Scroll(
    val id: Int,
    val mainId: Int,
    val name: String,
    val star: Int,
    val starLimit: Int,
    val quality: Int,
    val starUpNum: Int,
    val starUpResourceNum: Int,
    val dropLimit: Int,
    val story: String = "",
    val belong: Int = 0,
    val position: List<Int> = emptyList(),
    val cost: Int = 0,
    val conditionType: Int = 0,
    val conditionNum: Int = 0,
    val skillTreeId: Int = 0,
    val mainName: String = "",
    val life: Int = 0,
) {
    fun isBlue(): Boolean {
        return quality == 1
    }

    fun isPurple(): Boolean {
        return quality == 2
    }

    fun isOrange(): Boolean {
        return quality == 3
    }
}