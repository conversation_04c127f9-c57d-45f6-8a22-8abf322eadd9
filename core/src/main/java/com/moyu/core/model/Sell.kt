package com.moyu.core.model

import com.moyu.core.GameCore
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import java.util.UUID

const val TYPE_FREE = 1
const val TYPE_STORY = 2
const val TYPE_ALLY = 3
const val TYPE_KEY = 4

@Serializable
data class Sell(
    @SerialName("i")
    override val id: Int,
    @Transient
    val name: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val regularType: Int = 0,
    @Transient
    val desc2: String = "",
    @Transient
    val priceType: Int = 0,
    @Transient
    val price: Int = 0,
    @Transient
    val priceDollar: Double = 0.0,
    @SerialName("z")
    val storage: Int = 0,
    @Transient
    val storageType: Int = 0,
    @Transient
    val itemId: Int = 0,
    @SerialName("p")
    val num: Int = 0,
    @Transient
    val pic: String = "",
    @Transient
    val condition: List<Int> = emptyList(),
    @Transient
    val desc: String = "",
    @Transient
    val unlock: Int = 0,
    @Transient
    val itemType: Int = 0,
    @Transient
    val priority: Int = 0,
    @Transient
    val title: String = "",
    @Transient
    val canRefresh: Boolean = false,
    @Transient
    val googleItemId: String = "0",
    @Transient
    val showCondition: List<Int> = emptyList(),
    @Transient
    val xiaoMiItemId: String = "0",
    @Transient
    val itemId2: Int = 0,
    @SerialName("k")
    val opened: Boolean = false,
    @SerialName("o")
    val award: Award? = null,
    @Transient
    val uuid: String = UUID.randomUUID().toString().take(12)
): ConfigData {

    fun isPackageExp() = itemType == 2
    fun isPackageSingle() = itemType == 3

    fun isAifadian(): Boolean {
        return priceType == 5
    }

    // todo 英雄无敌无法立刻把所有的付费项目改成realMoney，所以先仅仅处理爬塔模式的
    fun isAifadianTower(): Boolean {
        return isAifadian() && id in 9800005..9802000
    }

    fun isAd(): Boolean {
        return priceType == 3
    }

    fun isDailyGift(): Boolean {
        return type == 1
    }

    fun isStory(): Boolean {
        return type == TYPE_STORY
    }

    fun isRandom(): Boolean {
        return itemType == 1
    }

    fun isFreeGift(): Boolean {
        // 免费或者广告
        return priceType == 0 || priceType == 3
    }

    fun isPvp(): Boolean {
        return type == 6
    }

    fun isGift(): Boolean {
        return GameCore.instance.getGiftPool().any { it.id == this.id }
    }

    fun isFirstCharge(): Boolean {
        return GameCore.instance.getGiftPool().first().id == this.id
    }

    fun isNewTaskPackage(): Boolean {
        return type == 21
    }

    fun isLotteryGift(): Boolean {
        return type == 30
    }

    fun isHoliday(): Boolean {
        return type == 22
    }

    fun isTower(): Boolean {
        return type == 23
    }

    fun isPvpMoney(): Boolean {
        return priceType == 4
    }

    fun isKeyMoney(): Boolean {
        return priceType == 2
    }

    fun isDiamondMoney(): Boolean {
        return priceType == 1
    }

    fun isGoogle(): Boolean {
        return type == 101
    }

    fun isTaptap(): Boolean {
        return type == 102
    }

    fun isMonthCard(): Boolean {
        return type == 31
    }
}