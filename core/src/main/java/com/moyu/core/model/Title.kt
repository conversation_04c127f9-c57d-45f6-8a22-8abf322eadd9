package com.moyu.core.model

import com.moyu.core.GameCore
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlin.math.min

@Serializable
data class Title(
    override val id: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val level: Int = 0,
    @Transient
    val conditionType: List<Int> = emptyList(),
    @Transient
    val conditionNum: List<Int> = emptyList(),
    @Transient
    val reward: Int = 0,
    @Transient
    val desc: String = "",
) : ConfigData {
    companion object {
        fun getLevelExp(level: Int): Int {
            return GameCore.instance.getTitlePool()[level].conditionNum.first().toInt()
        }

        fun getPreLevelExt(level: Int): Int {
            return GameCore.instance.getTitlePool().take(level).sumOf { it.conditionNum.first() }
        }

        fun getTitleLevel(exp: Int): Int {
            val pool = GameCore.instance.getTitlePool()
            val maxLevel = pool.last().level
            if (exp < pool.first().conditionNum.first()) {
                return 0
            }
            var targetLevel = 1
            var found = false
            (1..maxLevel).forEach {
                if (!found && exp < pool.take(it).sumOf { it.conditionNum.first() }) {
                    targetLevel = it - 1
                    found = true
                    return@forEach
                }
            }
            return min(
                maxLevel,
                targetLevel
            )
        }
    }
}
