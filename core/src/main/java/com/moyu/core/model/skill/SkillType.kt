package com.moyu.core.model.skill

/**
"1=角色专属战斗技能
2=战斗技能树技能
3=兵种技能
4=战场技能
5=装备技能
6=锦囊冒险技能
7=兵符技能树技能
8=天赋技能
9=附魔技能
10=政令技能
11=阵法技能
12=阵营技能"
 */
fun Skill.isAllySpecial() = skillType == 1
fun Skill.isBattleTree() = skillType == 2
fun Skill.isTroopSkill() = skillType == 999 // todo 废弃，这个游戏没有
fun Skill.isMagic() = skillType == 4
fun Skill.isHalo() = skillType == 5
fun Skill.isBattleProfession() = skillType == 6
fun Skill.isAdvProfession() = skillType == 7
fun Skill.isAdvForAlly() = skillType == 9
fun Skill.isBattleForAlly() = skillType == 8
fun Skill.isProfession() = isBattleProfession() || isAdvProfession()
fun Skill.isAdvBuilding() = skillType == 13
fun Skill.isAdvInGame() = skillType == 14


fun Skill.isTalentAdv() = skillType == 12
fun Skill.isEquipBattle() = skillType == 10
fun Skill.isEquipAdv() = skillType == 11

fun Skill.isAdventure() = isAdvBuilding()
        || isAdvInGame()
        || isAdvProfession()
        || isTalentAdv()
        || isAdvForAlly()
        || isEquipAdv()