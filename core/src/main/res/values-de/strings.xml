<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
    <string name="cast_skill">Freigegebene Fähigkeiten</string>																			
    <string name="gain_permanent_debuff">Einen permanenten Debuff erhalten</string>																			
    <string name="gain_permanent_buff">Einen dauerhaften Gewinn erhalten</string>																			
    <string name="execute_failed">Die Enthauptung wurde freigegeben, aber das Ziel wurde vor dem Tod gerettet und die Enthauptung war wirkungslos.</string>																			
    <string name="execute_not_work">Die Hinrichtung wird freigegeben, aber die aktuellen HP des Gegners sind größer als Ihre maximale Gesundheit und die Hinrichtung ist ungültig.</string>																			
    <string name="execute_done">Freigelassen, geköpft und erfolgreich getötet</string>																			
    <string name="gain_avoid_death">Immunität gegen den Tod erlangt</string>																			
    <string name="avoid_death_failed">Erhielt Immunität vor dem Tod, aber der Gegner ignorierte die Immunität vor dem Tod und die Immunität vor dem Tod war ungültig.</string>																			
    <string name="direct_win">Wenn du freigelassen wirst, gewinnst du direkt den Kampf</string>																			
    <string name="gain_enhancement">Fähigkeitsverbesserung erhalten</string>																			
    <string name="enhancement_skip">Aber die Stärkungszielfähigkeit wird nicht erlernt</string>																			
    <string name="damage_record">Verletzungsbilanz:</string>																			
    <string name="attacker_with_comma">Angreifer:</string>																			
    <string name="defender_with_comma">Verteidiger:</string>																			
    <string name="initial_damage_with_comma">Erstschaden:</string>																			
    <string name="fatal_with_comma">Kritischer Treffer:</string>																			
    <string name="fatal_damage_with_comma">Kritische Verletzung:</string>																			
    <string name="race_damage_with_comma">Typ Schadenserhöhung:</string>																			
    <string name="normal_attack_with_comma">Erhöhung des Grundangriffsschadens:</string>																			
    <string name="skill_damage_with_comma">Andere Schadenserhöhungen:</string>																			
    <string name="attacker_damage_inc">Schadenszunahme oder -abnahme (offensive Seite):</string>																			
    <string name="attacker_damage_inc_all">Sämtlicher Schaden nimmt zu und ab (offensive Seite):</string>																			
    <string name="pierce_attacker">Durchdringung (offensive Seite):</string>																			
    <string name="damage_value">Schadenswert:</string>																			
    <string name="defender_init_defense">Die erste Verteidigung des Verteidigers</string>																			
    <string name="defender_real_defense">Die tatsächliche Verteidigung des Verteidigers</string>																			
    <string name="defender_reduce_damage">Effekt zur Reduzierung des Verteidigungsschadens (Verteidiger):</string>																			
    <string name="defender_reduce_value">Schadensreduzierung (Verteidiger):</string>																			
    <string name="defender_race_reduce">Schadensreduzierung der Truppen des Verteidigers:</string>																			
    <string name="defender_all_reduce">Sämtliche Schadensreduzierung für den Verteidiger:</string>																			
    <string name="defender_immune">Immunität des Verteidigers:</string>																			
    <string name="defender_holy_shield">Heiliger Schild des Verteidigers:</string>																			
    <string name="defender_dodge">ausweichen:</string>																			
    <string name="single_damage">Einzelschadensgrenze:</string>																			
    <string name="final_damage">Endgültiger Schaden:</string>																			
    <string name="single_damage_protect">Einzelschadenschutz:</string>																			
    <string name="yes">Ja</string>																			
    <string name="no">NEIN</string>																			
    <string name="skill_typ1">Physik</string>																			
    <string name="skill_type2">Qi-System</string>																			
    <string name="skill_type3">Feuer</string>																			
    <string name="skill_type4">Wassersystem</string>																			
    <string name="skill_type5">Erdsystem</string>																			
    <string name="ones_turn">runden:</string>																			
    <string name="over_turn">Du hast es nicht geschafft, deinen Gegner innerhalb der begrenzten Runde zu besiegen, und der Kampf ist gescheitert.</string>																			
    <string name="release">freigeben</string>																			
    <string name="gain">erhalten</string>																			
    <string name="you_release">Du lässt los</string>																			
    <string name="let">machen</string>																			
    <string name="lost">Verlust</string>																			
    <string name="control_immune">Kontrollimmunität und kann nicht sein</string>																			
    <string name="trigger_multi_cast">löste einen Moralschub aus</string>																			
    <string name="forbid_heal_tip">Unter dem [Angst]-Effekt kann das Leben nicht wiederhergestellt werden.</string>																			
    <string name="frenzy_tips">Das von [Wut] betroffene Ziel wird zufällig ausgewählt.</string>																			
    <string name="disarm_tips">Unter dem Effekt von [Entangle] können keine normalen Angriffe ausgeführt werden.</string>																			
    <string name="silent_tips">Unter dem Effekt von [Versteinerung] können aktive Fertigkeiten nicht freigesetzt werden.</string>																			
    <string name="palsy_tips">Unter dem Effekt von [Blindheit] können die auslösenden Fähigkeiten nicht freigegeben werden.</string>																			
    <string name="ban_skill_tip1">von [Siegel</string>																			
    <string name="ban_skill_tip2">]-Effekt kann diese Fertigkeit nicht ausgelöst werden.</string>																			
    <string name="heal">genesen</string>																			
    <string name="layer">Schicht</string>																			
    <string name="unstack">Nicht stapelbar</string>																			
    <string name="infinite">unbegrenzt</string>																			
    <string name="rage">gewalttätig</string>																			
    <string name="unbreakable">Unbesiegbar</string>																			
    <string name="prop1">Tapferkeit</string>																			
    <string name="prop2">Weisheit</string>																			
    <string name="prop3">Oberbefehlshaber</string>																			
    <string name="prop4">innere Angelegenheiten</string>																			
    <string name="prop5">Charme</string>																			
    <string name="prop6">Leerer</string>																			
    <string name="holy_shield">Heiliger Schild</string>																			
    <string name="race1">Nahkampf</string>																			
    <string name="race2">Fernkampf</string>																			
    <string name="race3">Flug</string>																			
    <string name="race4">Zauberei</string>																			
    <string name="race5">Held</string>																			
    <string name="group1">Schloss</string>																			
    <string name="group2">Barriere</string>																			
    <string name="group3">Turm</string>																			
    <string name="group4">Hölle</string>																			
    <string name="group5">Friedhof</string>																			
    <string name="group6">Unterird.</string>																			
    <string name="group7">Hochburg</string>																			
    <string name="group8">Festung</string>																			
    <string name="group9">Element</string>																			
    <string name="group10">Buchtstadt</string>																			
    <string name="group11">Fabrik</string>																			
    <string name="group19">keiner</string>																			
    <string name="dispelled">zerstreut</string>																			
    <string name="cant_dispel">Immun gegen Bannungsdebuffs, kann nicht bannen</string>																			
    <string name="cant_dispel2">Immun gegen Buff-Dispersion und kann nicht gebannt werden</string>																			
    <string name="defeated_the_enemy">Du hast den Feind besiegt</string>																			
    <string name="level">Klasse</string>																			
    <string name="level1">Klasse</string>																			
    <string name="death">sterben</string>																			
    <string name="continuous_damage">erlittenen Schaden</string>																			
    <string name="continued_treatment">laufende Behandlung</string>																			
    <string name="drink">ziehen</string>																			
    <string name="i_achieved_positive_results">Ich bekomme einen Buff</string>																			
    <string name="i_achieved_negative_effects">Ich bekomme einen Debuff</string>																			
    <string name="dispel_the_opponent_buff">Bannen Sie den Buff des Gegners</string>																			
    <string name="dispel_your_own_buff">Selbstgewinn vertreiben</string>																			
    <string name="damage_self">Verletze dich selbst</string>																			
    <string name="cure_yourself">das Leben wiederherstellen</string>																			
    <string name="free_sb_from_death">frei vom Tod</string>																			
    <string name="release_a_skill">Release-Fähigkeiten</string>																			
    <string name="got_damage">verletzt sein</string>																			
    <string name="got_hurt">Schäden verursachen</string>																			
    <string name="successful_block">Erfolgreich ausweichen</string>																			
    <string name="start_of_the_round">Runde beginnt</string>																			
    <string name="end_of_the_round">Ende der Runde</string>																			
    <string name="call_upon_a_servant">Kreatur beschwören</string>																			
    <string name="no_dispelling_of_target_buffs">Kein Bannzieleffekt</string>																			
    <string name="minion_already_exists">Das beschworene Objekt existiert bereits und kann nicht mehr beschworen werden.</string>																			
    <string name="no_position_for_minion">Kein verfügbarer Platz</string>																			
    <string name="summoned">Einberufen</string>																			
    <string name="designated_skill_strike_rate_increase">Kritische Trefferquote erhöht</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">Erhöhte Release-Zeiten</string>																			
    <string name="immunity_to_blocking_for_specified_skills">Immunität gegen Ausweichen</string>																			
    <string name="designated_skills_ignore_stance">Schild ignorieren</string>																			
    <string name="designated_skills_ignore_invincibility">Ignorieren Sie die Unbesiegbarkeit/vermeiden Sie den Tod</string>																			
    <string name="designated_skills_ignore_taunts">Ignorieren Sie Verspottungen</string>																			
    <string name="cannot_be_dispersed">Buffs/Debuffs können nicht aufgehoben werden</string>																			
    <string name="increased_damage_dealt_by_designated_skills">Schaden erhöht</string>																			
    <string name="designated_skills_deal_less_damage">Schaden verringert</string>																			
    <string name="designated_skill_cd_increase">Verbesserte Kühlung</string>																			
    <string name="designated_skill_cd_decrease">Abklingzeit reduziert</string>																			
    <string name="increased_number_of_rounds">Erhöhte Buff-/Debuff-Dauerrunden</string>																			
    <string name="decreased_number_of_rounds">Die Dauer von Buffs/Debuffs wird verkürzt.</string>																			
    <string name="increased_probability_of_releasing_designated_skills">Erhöhte Freisetzungswahrscheinlichkeit</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">Reduzieren Sie die Wahrscheinlichkeit einer Freisetzung</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">Erhöhte Anzahl von Veröffentlichungen pro Runde</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">Reduziertes Freigabelimit pro Runde</string>																			
    <string name="increase_in_the_number_of_restricted_releases">Die Anzahl der Veröffentlichungen im gesamten Kampf wurde erhöht.</string>																			
    <string name="reduced_limit_on_number_of_releases">Die Anzahl der Veröffentlichungen im gesamten Kampf wurde reduziert.</string>																			
    <string name="increased_blood_absorption_rate_of_skills">Absorbieren und verbessern</string>																			
    <string name="designated_skills_ignore_enemies">Ignorieren Sie die Verteidigung des Feindes</string>																			
    <string name="skill_damage_type_changes_to">Die Schadensart ändert sich zu</string>																			
    <string name="designated_skills_vs_race">Paartyp</string>																			
    <string name="damage_increase">Schaden erhöht</string>																			
    <string name="damage_decrease">Schaden verringert</string>																			
    <string name="gain_split_effect">Holen Sie sich 【Splash】</string>																			
    <string name="fatal">Kritischer Treffer</string>																			
    <string name="damage1">Physik</string>																			
    <string name="damage2">Qi-System</string>																			
    <string name="damage3">Feuer</string>																			
    <string name="damage4">Wassersystem</string>																			
    <string name="damage5">Erdsystem</string>																			
    <string name="defense1">Verteidigung</string>																			
    <string name="defense2">Widerstand</string>																			
    <string name="defense_name1">Vtg.</string>																			
    <string name="defense_name2">Lwf.</string>																			
    <string name="defense_name3">Fwf.</string>																			
    <string name="defense_name4">Wwf.</string>																			
    <string name="defense_name5">Ewf.</string>																			
    <string name="attack">Angriff</string>																			
    <string name="attack_tips">Bestimmt den Intensitätswert verschiedener Schadensarten</string>																			
    <string name="defense1_tips">Spielt die Rolle, den Schaden zu reduzieren, wenn man physischen Schaden erleidet</string>																			
    <string name="defense2_tips">Es spielt die Rolle, den Schaden zu reduzieren, wenn es durch das Luftsystem beschädigt wird.</string>																			
    <string name="defense3_tips">Spielt die Rolle der Schadensreduzierung bei Feuerschaden</string>																			
    <string name="defense4_tips">Es spielt die Rolle, den Schaden bei einem Wasserschaden zu reduzieren.</string>																			
    <string name="defense5_tips">Es spielt die Rolle der Schadensminderung bei Erdschäden.</string>																			
    <string name="hp">Leben</string>																			
    <string name="hp_tips">Wenn das Leben Null erreicht, stirbt der Held oder die Einheit.</string>																			
    <string name="fatal_rate">Krit</string>																			
    <string name="fatal_rate_tips">Wahrscheinlichkeit, dass zusätzlich große Schäden entstehen</string>																			
    <string name="fatal_damage">KS</string>																			
    <string name="fatal_damage_tips">Zusätzliches Schadensverhältnis beim Auslösen eines kritischen Treffers</string>																			
    <string name="dodge">Dodge</string>																			
    <string name="dodge_tips">Wenn Sie erfolgreich ausweichen, wird dieser Schaden vollständig vermieden.</string>																			
    <string name="speed">Geschw.</string>																			
    <string name="speed_tips">Bestimmt die Priorität der Aktionen von Helden oder Einheiten. Je höher die Geschwindigkeit, desto früher handeln sie.</string>																			
    <string name="skill_tree">System</string>																			
</resources>																			