<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
    <string name="cast_skill">Liberou habilidade</string>																			
    <string name="gain_permanent_debuff">Penalidade permanente obtida</string>																			
    <string name="gain_permanent_buff">Bônus permanente obtido</string>																			
    <string name="execute_failed">Execução lançada, mas alvo imortal - efeito anulado</string>																			
    <string name="execute_not_work">Liberou execução, mas HP atual do alvo excede seu HP máximo, execução inválida</string>																			
    <string name="execute_done">Liberou execução, matou com sucesso</string>																			
    <string name="gain_avoid_death">Ganhou imortalidade</string>																			
    <string name="avoid_death_failed">Obteve imortalidade, mas o inimigo a ignora - efeito anulado</string>																			
    <string name="direct_win">Foi liberado, você obteve vitória direta na batalha</string>																			
    <string name="gain_enhancement">Habilidade reforçada obtida</string>																			
    <string name="enhancement_skip">Mas habilidade de fortalecimento do alvo não aprendida</string>																			
    <string name="damage_record">Registro de dano: </string>																			
    <string name="attacker_with_comma">Atacante:</string>																			
    <string name="defender_with_comma">Defensor:</string>																			
    <string name="initial_damage_with_comma">Dano inicial:</string>																			
    <string name="fatal_with_comma">Crítico:</string>																			
    <string name="fatal_damage_with_comma">Dano CRÍT:</string>																			
    <string name="race_damage_with_comma">Aumento de dano por tipo:</string>																			
    <string name="normal_attack_with_comma">Dano extra de ataque normal:</string>																			
    <string name="skill_damage_with_comma">Outros aumentos de dano:</string>																			
    <string name="attacker_damage_inc">Variação de dano (Atacante):</string>																			
    <string name="attacker_damage_inc_all">Todos os danos (Atacante):</string>																			
    <string name="pierce_attacker">Penetração (Atacante):</string>																			
    <string name="damage_value">Valor do dano:</string>																			
    <string name="defender_init_defense">Defesa inicial do defensor</string>																			
    <string name="defender_real_defense">Defesa real do defensor</string>																			
    <string name="defender_reduce_damage">Redução de dano por defesa (defensor):</string>																			
    <string name="defender_reduce_value">Redução de dano (defensor):</string>																			
    <string name="defender_race_reduce">Redução de dano por unidade do defensor:</string>																			
    <string name="defender_all_reduce">Redução de dano total do defensor:</string>																			
    <string name="defender_immune">Defensor imune:</string>																			
    <string name="defender_holy_shield">Escudo sagrado do defensor:</string>																			
    <string name="defender_dodge">Esquiva:</string>																			
    <string name="single_damage">Limite de dano único:</string>																			
    <string name="final_damage">Dano final:</string>																			
    <string name="single_damage_protect">Proteção contra dano único:</string>																			
    <string name="yes">Sim</string>																			
    <string name="no">Não</string>																			
    <string name="skill_typ1">Físico</string>																			
    <string name="skill_type2">Elemento Ar</string>																			
    <string name="skill_type3">Fogo</string>																			
    <string name="skill_type4">Água</string>																			
    <string name="skill_type5">Terra</string>																			
    <string name="ones_turn">Turno de:</string>																			
    <string name="over_turn">Você não derrotou o oponente dentro dos turnos limitados, batalha perdida</string>																			
    <string name="release">Lançamento</string>																			
    <string name="gain">Obter</string>																			
    <string name="you_release">Você lançou</string>																			
    <string name="let">Causa</string>																			
    <string name="lost">Perda</string>																			
    <string name="control_immune">Imunidade a controle, não pode ser</string>																			
    <string name="trigger_multi_cast">Ativou moral elevado</string>																			
    <string name="forbid_heal_tip">Afetado por 【Terror】, não pode recuperar vida.</string>																			
    <string name="frenzy_tips">Afetado por 【Fúria】, seleciona alvo aleatoriamente.</string>																			
    <string name="disarm_tips">Afetado por [Enredar], incapaz de realizar ataques normais.</string>																			
    <string name="silent_tips">Afetado por 【Petrificação】, não pode liberar habilidades ativas.</string>																			
    <string name="palsy_tips">Afetado por [Cegueira], incapaz de ativar habilidades.</string>																			
    <string name="ban_skill_tip1">Afetado por [Selamento</string>																			
    <string name="ban_skill_tip2">] efeito, incapaz de lançar esta habilidade.</string>																			
    <string name="heal">Cura</string>																			
    <string name="layer">camada</string>																			
    <string name="unstack">Não acumulável</string>																			
    <string name="infinite">Infinito</string>																			
    <string name="rage">Fúria</string>																			
    <string name="unbreakable">Invencível</string>																			
    <string name="prop1">Bravura</string>																			
    <string name="prop2">Estratégia</string>																			
    <string name="prop3">Comando</string>																			
    <string name="prop4">Administração</string>																			
    <string name="prop5">Carisma</string>																			
    <string name="prop6">Vazio</string>																			
    <string name="holy_shield">Escudo Sagrado</string>																			
    <string name="race1">Melee</string>																			
    <string name="race2">Alcance</string>																			
    <string name="race3">Voo</string>																			
    <string name="race4">Conjuração</string>																			
    <string name="race5">Herói</string>																			
    <string name="group1">Castelo</string>																			
    <string name="group2">Fortaleza</string>																			
    <string name="group3">Torre</string>																			
    <string name="group4">Inferno</string>																			
    <string name="group5">Necrópole</string>																			
    <string name="group6">Subsolo</string>																			
    <string name="group7">Cidadela</string>																			
    <string name="group8">Fortaleza</string>																			
    <string name="group9">Elemento</string>																			
    <string name="group10">Porto</string>																			
    <string name="group11">Fábrica</string>																			
    <string name="group19">Nenhum</string>																			
    <string name="dispelled">Dispersou</string>																			
    <string name="cant_dispel">Imune a dissipação de efeitos negativos, não pode ser dissipado</string>																			
    <string name="cant_dispel2">Imune a dispersão de bônus, não pode ser disperso</string>																			
    <string name="defeated_the_enemy">Você derrotou o inimigo</string>																			
    <string name="level">nível</string>																			
    <string name="level1">nível</string>																			
    <string name="death">Morte</string>																			
    <string name="continuous_damage">Dano contínuo</string>																			
    <string name="continued_treatment">Cura contínua</string>																			
    <string name="drink">Drenagem</string>																			
    <string name="i_achieved_positive_results">Eu ganho benefícios</string>																			
    <string name="i_achieved_negative_effects">Recebi penalidade</string>																			
    <string name="dispel_the_opponent_buff">Dissipa benefícios do oponente</string>																			
    <string name="dispel_your_own_buff">Dispersar próprios bônus</string>																			
    <string name="damage_self">Auto-dano</string>																			
    <string name="cure_yourself">Restaura sua própria vida</string>																			
    <string name="free_sb_from_death">Lançar imortalidade</string>																			
    <string name="release_a_skill">Liberar habilidade</string>																			
    <string name="got_damage">Dano recebido</string>																			
    <string name="got_hurt">Causar dano</string>																			
    <string name="successful_block">Esquiva bem-sucedida</string>																			
    <string name="start_of_the_round">Início do turno</string>																			
    <string name="end_of_the_round">Fim do turno</string>																			
    <string name="call_upon_a_servant">Criatura invocada</string>																			
    <string name="no_dispelling_of_target_buffs">Nenhum efeito de alvo dissipável</string>																			
    <string name="minion_already_exists">Já existe uma criatura invocada, não é possível invocar mais</string>																			
    <string name="no_position_for_minion">Sem espaço vago</string>																			
    <string name="summoned">Convocou</string>																			
    <string name="designated_skill_strike_rate_increase">Aumento de taxa de crítico</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">Aumento de liberações</string>																			
    <string name="immunity_to_blocking_for_specified_skills">Imunidade a esquiva</string>																			
    <string name="designated_skills_ignore_stance">Ignora escudo</string>																			
    <string name="designated_skills_ignore_invincibility">Ignora invencibilidade/imortalidade</string>																			
    <string name="designated_skills_ignore_taunts">Ignorar provocação</string>																			
    <string name="cannot_be_dispersed">Bônus/Debuff não pode ser dissipado</string>																			
    <string name="increased_damage_dealt_by_designated_skills">Aumento de dano</string>																			
    <string name="designated_skills_deal_less_damage">Redução de dano</string>																			
    <string name="designated_skill_cd_increase">Aumento de recarga</string>																			
    <string name="designated_skill_cd_decrease">Redução de recarga</string>																			
    <string name="increased_number_of_rounds">Duração de bônus/penalidades aumentada</string>																			
    <string name="decreased_number_of_rounds">Redução de turnos de bônus/penalidades</string>																			
    <string name="increased_probability_of_releasing_designated_skills">Probabilidade de lançamento aumentada</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">Redução de probabilidade de lançamento</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">Limite de lançamentos por turno aumentado</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">Redução de limite de liberações por turno</string>																			
    <string name="increase_in_the_number_of_restricted_releases">Aumento de limite de liberação por batalha</string>																			
    <string name="reduced_limit_on_number_of_releases">Redução de limite de usos por batalha</string>																			
    <string name="increased_blood_absorption_rate_of_skills">Aumento de drenagem</string>																			
    <string name="designated_skills_ignore_enemies">Ignora defesa do inimigo</string>																			
    <string name="skill_damage_type_changes_to">Tipo de dano alterado para</string>																			
    <string name="designated_skills_vs_race">Contra tipo</string>																			
    <string name="damage_increase">Aumento de dano</string>																			
    <string name="damage_decrease">Redução de dano</string>																			
    <string name="gain_split_effect">Obter [Efeito de respingo]</string>																			
    <string name="fatal">CRÍT</string>																			
    <string name="damage1">Físico</string>																			
    <string name="damage2">Elemento Ar</string>																			
    <string name="damage3">Fogo</string>																			
    <string name="damage4">Água</string>																			
    <string name="damage5">Terra</string>																			
    <string name="defense1">Defesa</string>																			
    <string name="defense2">Resistência</string>																			
    <string name="defense_name1">Defesa</string>																			
    <string name="defense_name2">Res.A</string>																			
    <string name="defense_name3">Res.F</string>																			
    <string name="defense_name4">Res.Ag</string>																			
    <string name="defense_name5">Res.T</string>																			
    <string name="attack">Ataque</string>																			
    <string name="attack_tips">Determina o valor de intensidade de todos os tipos de dano</string>																			
    <string name="defense1_tips">Reduz dano físico recebido</string>																			
    <string name="defense2_tips">Reduz dano quando sofre dano de ar</string>																			
    <string name="defense3_tips">Reduz dano quando sofre dano de fogo</string>																			
    <string name="defense4_tips">Reduz dano ao sofrer dano de água</string>																			
    <string name="defense5_tips">Reduz dano ao sofrer dano de terra</string>																			
    <string name="hp">Vida</string>																			
    <string name="hp_tips">Quando a vida chega a zero, o herói ou unidade morre</string>																			
    <string name="fatal_rate">CRÍT</string>																			
    <string name="fatal_rate_tips">Probabilidade de causar dano adicional massivo</string>																			
    <string name="fatal_damage">Dano CRÍT</string>																			
    <string name="fatal_damage_tips">Porcentagem de dano extra em crítico</string>																			
    <string name="dodge">Esquiva</string>																			
    <string name="dodge_tips">Ao esquivar com sucesso, evita completamente o dano atual</string>																			
    <string name="speed">Velocidade</string>																			
    <string name="speed_tips">Determina a prioridade de ação de heróis ou unidades, maior velocidade age primeiro</string>																			
    <string name="skill_tree">Tipo</string>																			
</resources>																			