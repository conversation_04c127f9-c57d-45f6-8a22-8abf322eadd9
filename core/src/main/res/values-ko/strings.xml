<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
    <string name="cast_skill">스킬 사용</string>																			
    <string name="gain_permanent_debuff">영구 약화 효과 획득</string>																			
    <string name="gain_permanent_buff">영구 강화 효과 획득</string>																			
    <string name="execute_failed">처형 시도 실패(대상 면역)</string>																			
    <string name="execute_not_work">처단 시도 실패(대상 생명력 &gt; 최대 생명력)</string>																			
    <string name="execute_done">처형 스킬 시전, 성공적으로 처형했습니다</string>																			
    <string name="gain_avoid_death">죽음 면역 획득</string>																			
    <string name="avoid_death_failed">면역 획득(상대 무시 효과)</string>																			
    <string name="direct_win">시전 시 전투 승리를 즉시 획득합니다</string>																			
    <string name="gain_enhancement">스킬 강화 획득</string>																			
    <string name="enhancement_skip">강화 대상 스킬 미습득</string>																			
    <string name="damage_record">피해 기록: </string>																			
    <string name="attacker_with_comma">공격측:</string>																			
    <string name="defender_with_comma">방어측:</string>																			
    <string name="initial_damage_with_comma">초기 피해:</string>																			
    <string name="fatal_with_comma">치명타:</string>																			
    <string name="fatal_damage_with_comma">치명타 피해:</string>																			
    <string name="race_damage_with_comma">타입 증폭:</string>																			
    <string name="normal_attack_with_comma">일반 공격 증폭</string>																			
    <string name="skill_damage_with_comma">기타 피해 증가:</string>																			
    <string name="attacker_damage_inc">공격측 피해 증감:</string>																			
    <string name="attacker_damage_inc_all">공격측 전체 피해 증감:</string>																			
    <string name="pierce_attacker">관통(공격측):</string>																			
    <string name="damage_value">피해량:</string>																			
    <string name="defender_init_defense">방어측 기본 방어</string>																			
    <string name="defender_real_defense">방어측 실제 방어력</string>																			
    <string name="defender_reduce_damage">방어 감쇄 효과(방어측):</string>																			
    <string name="defender_reduce_value">피해 감소(방어측):</string>																			
    <string name="defender_race_reduce">방어측 병종 피해 감소:</string>																			
    <string name="defender_all_reduce">방어측 전체 피해 감소:</string>																			
    <string name="defender_immune">방어측 면역:</string>																			
    <string name="defender_holy_shield">방어측 성스러운 방패:</string>																			
    <string name="defender_dodge">회피:</string>																			
    <string name="single_damage">단일 피해 상한:</string>																			
    <string name="final_damage">최종 피해:</string>																			
    <string name="single_damage_protect">단일 피해 보호:</string>																			
    <string name="yes">예</string>																			
    <string name="no">아니오</string>																			
    <string name="skill_typ1">물리</string>																			
    <string name="skill_type2">기계</string>																			
    <string name="skill_type3">화염 속성</string>																			
    <string name="skill_type4">수계</string>																			
    <string name="skill_type5">대지 속성</string>																			
    <string name="ones_turn">의 턴:</string>																			
    <string name="over_turn">제한 턴 내 적 처치 실패로 전투 패배</string>																			
    <string name="release">시전</string>																			
    <string name="gain">획득</string>																			
    <string name="you_release">당신이 시전</string>																			
    <string name="let">사용</string>																			
    <string name="lost">손실</string>																			
    <string name="control_immune">제어 면역</string>																			
    <string name="trigger_multi_cast">사기 상승 발동</string>																			
    <string name="forbid_heal_tip">【공포】 효과로 생명력 회복 불가</string>																			
    <string name="frenzy_tips">【광폭】효과 영향으로 대상을 무작위 선택합니다</string>																			
    <string name="disarm_tips">【휘감기】 효과를 받아 일반 공격을 발동할 수 없습니다.</string>																			
    <string name="silent_tips">【석화】효과로 인해 액티브 스킬 사용 불가</string>																			
    <string name="palsy_tips">【실명】 효과로 발동 스킬 사용 불가</string>																			
    <string name="ban_skill_tip1">【봉인】효과 영향을 받아</string>																			
    <string name="ban_skill_tip2">】효과로 인해 해당 스킬 사용 불가</string>																			
    <string name="heal">회복</string>																			
    <string name="layer">중첩</string>																			
    <string name="unstack">중첩 불가</string>																			
    <string name="infinite">무한</string>																			
    <string name="rage">광폭화</string>																			
    <string name="unbreakable">무적</string>																			
    <string name="prop1">무용</string>																			
    <string name="prop2">전략</string>																			
    <string name="prop3">통솔력</string>																			
    <string name="prop4">내정</string>																			
    <string name="prop5">매력</string>																			
    <string name="prop6">공란</string>																			
    <string name="holy_shield">성스러운 방패</string>																			
    <string name="race1">근접</string>																			
    <string name="race2">원거리</string>																			
    <string name="race3">비행</string>																			
    <string name="race4">주문 시전</string>																			
    <string name="race5">영웅</string>																			
    <string name="group1">성</string>																			
    <string name="group2">성벽</string>																			
    <string name="group3">탑</string>																			
    <string name="group4">지옥</string>																			
    <string name="group5">묘지</string>																			
    <string name="group6">지하</string>																			
    <string name="group7">거점</string>																			
    <string name="group8">요새</string>																			
    <string name="group9">원소</string>																			
    <string name="group10">항구</string>																			
    <string name="group11">공장</string>																			
    <string name="group19">없음</string>																			
    <string name="dispelled">해제됨</string>																			
    <string name="cant_dispel">디버프 해제 면역</string>																			
    <string name="cant_dispel2">이로운 효과 제거 면역, 제거 불가</string>																			
    <string name="defeated_the_enemy">적 처치 성공</string>																			
    <string name="level">레벨</string>																			
    <string name="level1">레벨</string>																			
    <string name="death">사망</string>																			
    <string name="continuous_damage">지속 피해</string>																			
    <string name="continued_treatment">지속 치유</string>																			
    <string name="drink">흡수</string>																			
    <string name="i_achieved_positive_results">강화 효과 획득</string>																			
    <string name="i_achieved_negative_effects">디버프 획득</string>																			
    <string name="dispel_the_opponent_buff">상대의 강화 효과 제거</string>																			
    <string name="dispel_your_own_buff">자신 강화 효과 해제</string>																			
    <string name="damage_self">자신에게 피해</string>																			
    <string name="cure_yourself">자신의 체력 회복</string>																			
    <string name="free_sb_from_death">죽음 면역 발동</string>																			
    <string name="release_a_skill">스킬 시전</string>																			
    <string name="got_damage">피해 받음</string>																			
    <string name="got_hurt">피해 입힘</string>																			
    <string name="successful_block">회피 성공</string>																			
    <string name="start_of_the_round">턴 시작</string>																			
    <string name="end_of_the_round">턴 종료</string>																			
    <string name="call_upon_a_servant">소환수</string>																			
    <string name="no_dispelling_of_target_buffs">제거 가능 대상 효과 없음</string>																			
    <string name="minion_already_exists">기존 소환물 존재 시 추가 소환 불가</string>																			
    <string name="no_position_for_minion">빈 자리 없음</string>																			
    <string name="summoned">소환됨</string>																			
    <string name="designated_skill_strike_rate_increase">치명타 확률 증가</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">사용 횟수 증가</string>																			
    <string name="immunity_to_blocking_for_specified_skills">회피 무시</string>																			
    <string name="designated_skills_ignore_stance">실드 무시</string>																			
    <string name="designated_skills_ignore_invincibility">무적/면역 무시</string>																			
    <string name="designated_skills_ignore_taunts">도발 무시</string>																			
    <string name="cannot_be_dispersed">이로운/약화 효과 제거 불가</string>																			
    <string name="increased_damage_dealt_by_designated_skills">피해 증가</string>																			
    <string name="designated_skills_deal_less_damage">피해량 감소</string>																			
    <string name="designated_skill_cd_increase">쿨타임 증가</string>																			
    <string name="designated_skill_cd_decrease">재사용 대기시간 감소</string>																			
    <string name="increased_number_of_rounds">버프/디버프 지속 턴 증가</string>																			
    <string name="decreased_number_of_rounds">강화/약화 지속 턴 감소</string>																			
    <string name="increased_probability_of_releasing_designated_skills">시전 확률 증가</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">발동 확률 감소</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">턴당 시전 횟수 제한 증가</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">턴당 사용 제한 감소</string>																			
    <string name="increase_in_the_number_of_restricted_releases">전투당 시전 횟수 제한 증가</string>																			
    <string name="reduced_limit_on_number_of_releases">전투 전체 제한 사용 횟수 감소</string>																			
    <string name="increased_blood_absorption_rate_of_skills">흡수량 증가</string>																			
    <string name="designated_skills_ignore_enemies">적 방어력 무시</string>																			
    <string name="skill_damage_type_changes_to">피해 유형 변경</string>																			
    <string name="designated_skills_vs_race">대상 유형</string>																			
    <string name="damage_increase">피해 증가</string>																			
    <string name="damage_decrease">피해량 감소</string>																			
    <string name="gain_split_effect">【스플래시】 획득</string>																			
    <string name="fatal">치명타</string>																			
    <string name="damage1">물리</string>																			
    <string name="damage2">기계</string>																			
    <string name="damage3">화염 속성</string>																			
    <string name="damage4">수계</string>																			
    <string name="damage5">대지 속성</string>																			
    <string name="defense1">방어</string>																			
    <string name="defense2">저항</string>																			
    <string name="defense_name1">방어</string>																			
    <string name="defense_name2">기계저항</string>																			
    <string name="defense_name3">화계저항</string>																			
    <string name="defense_name4">물저항</string>																			
    <string name="defense_name5">토계저항</string>																			
    <string name="attack">공격</string>																			
    <string name="attack_tips">각 유형 피해 강도 결정</string>																			
    <string name="defense1_tips">물리 피해 시 감쇄 효과 발동</string>																			
    <string name="defense2_tips">기계 속성 피해를 받을 때 감소 효과 발동</string>																			
    <string name="defense3_tips">화염 속성 피해 감소</string>																			
    <string name="defense4_tips">수계 피해 감쇄 발동</string>																			
    <string name="defense5_tips">대지 속성 피해 감소</string>																			
    <string name="hp">생명력</string>																			
    <string name="hp_tips">생명력이 0이 되면 영웅 또는 유닛이 전사합니다</string>																			
    <string name="fatal_rate">치명타</string>																			
    <string name="fatal_rate_tips">추가 대량 피해 발생 확률</string>																			
    <string name="fatal_damage">치명타 피해</string>																			
    <string name="fatal_damage_tips">치명타 추가 피해율</string>																			
    <string name="dodge">회피</string>																			
    <string name="dodge_tips">회피 성공 시 해당 피해를 완전히 방어합니다</string>																			
    <string name="speed">속도</string>																			
    <string name="speed_tips">행동 우선순위 결정 수치</string>																			
    <string name="skill_tree">계</string>																			
</resources>																			