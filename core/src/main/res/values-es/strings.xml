<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
    <string name="cast_skill">Habilidad lanzada</string>																			
    <string name="gain_permanent_debuff">Obtuvo debilidad permanente</string>																			
    <string name="gain_permanent_buff">Obtuvo beneficio permanente</string>																			
    <string name="execute_failed">Ejecución falló por inmunidad mortal</string>																			
    <string name="execute_not_work">Ejecución falló: vida del enemigo supera tu máximo</string>																			
    <string name="execute_done">Ejecutó golpe final, eliminando a</string>																			
    <string name="gain_avoid_death">Obtuvo inmunidad mortal</string>																			
    <string name="avoid_death_failed">Inmunidad mortal ignorada por enemigo</string>																			
    <string name="direct_win">Al ser liberado, obtienes victoria inmediata</string>																			
    <string name="gain_enhancement">Habilidad mejorada</string>																			
    <string name="enhancement_skip">Objetivo no ha aprendido la habilidad</string>																			
    <string name="damage_record"><PERSON><PERSON> de <PERSON>: </string>																			
    <string name="attacker_with_comma">Atacante:</string>																			
    <string name="defender_with_comma">Defensor:</string>																			
    <string name="initial_damage_with_comma">Daño inicial:</string>																			
    <string name="fatal_with_comma">Crítico:</string>																			
    <string name="fatal_damage_with_comma">D.C.:</string>																			
    <string name="race_damage_with_comma">Daño aumentado por tipo:</string>																			
    <string name="normal_attack_with_comma">Daño de ataque normal aumentado:</string>																			
    <string name="skill_damage_with_comma">Otros aumentos de daño:</string>																			
    <string name="attacker_damage_inc">Modificador de daño (atacante):</string>																			
    <string name="attacker_damage_inc_all">Modificador total de daño (atacante):</string>																			
    <string name="pierce_attacker">Penetración (Atacante):</string>																			
    <string name="damage_value">Valor de daño:</string>																			
    <string name="defender_init_defense">Defensa base del defensor</string>																			
    <string name="defender_real_defense">Defensa real del defensor</string>																			
    <string name="defender_reduce_damage">Reducción defensiva (Defensor):</string>																			
    <string name="defender_reduce_value">Reducción de daño (defensor):</string>																			
    <string name="defender_race_reduce">Reducción de daño de tropas del defensor:</string>																			
    <string name="defender_all_reduce">Reducción de todo daño del defensor:</string>																			
    <string name="defender_immune">Inmunidad del defensor:</string>																			
    <string name="defender_holy_shield">Escudo Sagrado del defensor:</string>																			
    <string name="defender_dodge">Evasión:</string>																			
    <string name="single_damage">Límite de daño por ataque:</string>																			
    <string name="final_damage">Daño final:</string>																			
    <string name="single_damage_protect">Protección de daño único:</string>																			
    <string name="yes">Sí</string>																			
    <string name="no">No</string>																			
    <string name="skill_typ1">Físico</string>																			
    <string name="skill_type2">Aero</string>																			
    <string name="skill_type3">Fuego</string>																			
    <string name="skill_type4">Hidro</string>																			
    <string name="skill_type5">Tierra</string>																			
    <string name="ones_turn">Turno de:</string>																			
    <string name="over_turn">Derrota: no eliminaste al enemigo a tiempo</string>																			
    <string name="release">Liberar</string>																			
    <string name="gain">Obtener</string>																			
    <string name="you_release">Has lanzado</string>																			
    <string name="let">Aplicar</string>																			
    <string name="lost">Pérdida</string>																			
    <string name="control_immune">Inmune a controles</string>																			
    <string name="trigger_multi_cast">Activado moral alto</string>																			
    <string name="forbid_heal_tip">Sufre 【Terror】, no puede curarse.</string>																			
    <string name="frenzy_tips">Bajo efecto [Berserk], selecciona objetivos aleatoriamente.</string>																			
    <string name="disarm_tips">Sufre 【Enredo】, no puede atacar.</string>																			
    <string name="silent_tips">Petrificado, no puede usar habilidades activas.</string>																			
    <string name="palsy_tips">Sufre 【Ceguera】, no puede usar habilidades.</string>																			
    <string name="ban_skill_tip1">Bajo efecto [Sellado</string>																			
    <string name="ban_skill_tip2">] efecto, habilidad bloqueada.</string>																			
    <string name="heal">Curación</string>																			
    <string name="layer">Capas</string>																			
    <string name="unstack">No acumulable</string>																			
    <string name="infinite">Infinito</string>																			
    <string name="rage">Furia</string>																			
    <string name="unbreakable">Invencible</string>																			
    <string name="prop1">Valentía</string>																			
    <string name="prop2">Estrategia</string>																			
    <string name="prop3">Liderazgo</string>																			
    <string name="prop4">Administración</string>																			
    <string name="prop5">Carisma</string>																			
    <string name="prop6">Vacío</string>																			
    <string name="holy_shield">Escudo Sagrado</string>																			
    <string name="race1">Cercano</string>																			
    <string name="race2">Distancia</string>																			
    <string name="race3">Vuelo</string>																			
    <string name="race4">Conjuro</string>																			
    <string name="race5">Héroe</string>																			
    <string name="group1">Castillo</string>																			
    <string name="group2">Muralla</string>																			
    <string name="group3">Torreón</string>																			
    <string name="group4">Infierno</string>																			
    <string name="group5">Cementerio</string>																			
    <string name="group6">Subterráneo</string>																			
    <string name="group7">Bastión</string>																			
    <string name="group8">Fortaleza</string>																			
    <string name="group9">Elemento</string>																			
    <string name="group10">Puerto</string>																			
    <string name="group11">Fábrica</string>																			
    <string name="group19">Ninguno</string>																			
    <string name="dispelled">Disipado</string>																			
    <string name="cant_dispel">Inmune a disipar debuffs, no se puede disipar</string>																			
    <string name="cant_dispel2">Inmune a disipación de beneficios, no disipable</string>																			
    <string name="defeated_the_enemy">¡Enemigo eliminado!</string>																			
    <string name="level">Nivel</string>																			
    <string name="level1">Nivel</string>																			
    <string name="death">Muerte</string>																			
    <string name="continuous_damage">Daño continuo</string>																			
    <string name="continued_treatment">Cura continua</string>																			
    <string name="drink">Absorción</string>																			
    <string name="i_achieved_positive_results">Obtuve beneficio</string>																			
    <string name="i_achieved_negative_effects">Recibí debuff</string>																			
    <string name="dispel_the_opponent_buff">Elimina beneficios enemigos</string>																			
    <string name="dispel_your_own_buff">Disipar propias mejoras</string>																			
    <string name="damage_self">Autodaño</string>																			
    <string name="cure_yourself">Cura vida propia</string>																			
    <string name="free_sb_from_death">Inmunidad mortal al activar</string>																			
    <string name="release_a_skill">Activar habilidad</string>																			
    <string name="got_damage">Daño recibido</string>																			
    <string name="got_hurt">Infligir daño</string>																			
    <string name="successful_block">Esquivado exitoso</string>																			
    <string name="start_of_the_round">Inicio de turno</string>																			
    <string name="end_of_the_round">Fin de turno</string>																			
    <string name="call_upon_a_servant">Criatura invocada</string>																			
    <string name="no_dispelling_of_target_buffs">Sin efectos disipables</string>																			
    <string name="minion_already_exists">Invocación fallida: ya existe criatura</string>																			
    <string name="no_position_for_minion">Sin espacio disponible</string>																			
    <string name="summoned">Ha invocado</string>																			
    <string name="designated_skill_strike_rate_increase">Prob. de Golpe Crítico</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">Número de usos aumentado</string>																			
    <string name="immunity_to_blocking_for_specified_skills">Inmune a Esquiva</string>																			
    <string name="designated_skills_ignore_stance">Ignora escudos</string>																			
    <string name="designated_skills_ignore_invincibility">Ignora invencible/inmunidad mortal</string>																			
    <string name="designated_skills_ignore_taunts">Ignora provocación</string>																			
    <string name="cannot_be_dispersed">Beneficios/debuffs indispables</string>																			
    <string name="increased_damage_dealt_by_designated_skills">Daño aumentado</string>																			
    <string name="designated_skills_deal_less_damage">Reducción de daño</string>																			
    <string name="designated_skill_cd_increase">Aumenta enfriamiento</string>																			
    <string name="designated_skill_cd_decrease">Reducción de enfriamiento</string>																			
    <string name="increased_number_of_rounds">Duración de mejoras/debuffs aumentada</string>																			
    <string name="decreased_number_of_rounds">Duración de efectos reducida</string>																			
    <string name="increased_probability_of_releasing_designated_skills">Probabilidad de lanzamiento aumentada</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">Disminuye probabilidad de activación</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">Límite de usos por turno aumentado</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">Límite de usos por turno reducido</string>																			
    <string name="increase_in_the_number_of_restricted_releases">Límite de usos en batalla aumentado</string>																			
    <string name="reduced_limit_on_number_of_releases">Reduce límite de usos por batalla</string>																			
    <string name="increased_blood_absorption_rate_of_skills">Aumento de absorción</string>																			
    <string name="designated_skills_ignore_enemies">Ignora defensa enemiga</string>																			
    <string name="skill_damage_type_changes_to">Tipo de daño cambiado a</string>																			
    <string name="designated_skills_vs_race">Tipo objetivo</string>																			
    <string name="damage_increase">Daño aumentado</string>																			
    <string name="damage_decrease">Reducción de daño</string>																			
    <string name="gain_split_effect">Obtener 【Salpicadura】</string>																			
    <string name="fatal">CRÍT</string>																			
    <string name="damage1">Físico</string>																			
    <string name="damage2">Aero</string>																			
    <string name="damage3">Fuego</string>																			
    <string name="damage4">Hidro</string>																			
    <string name="damage5">Tierra</string>																			
    <string name="defense1">Defensa</string>																			
    <string name="defense2">Resistencia</string>																			
    <string name="defense_name1">Defensa</string>																			
    <string name="defense_name2">Res. Ae</string>																			
    <string name="defense_name3">Res. Fu</string>																			
    <string name="defense_name4">Res. Ag</string>																			
    <string name="defense_name5">Res. Ti</string>																			
    <string name="attack">Ataque</string>																			
    <string name="attack_tips">Determina la potencia de daño</string>																			
    <string name="defense1_tips">Reducción de daño físico recibido</string>																			
    <string name="defense2_tips">Reduce daño de aire recibido</string>																			
    <string name="defense3_tips">Reduce daño de fuego recibido</string>																			
    <string name="defense4_tips">Reducción de daño hidro recibido</string>																			
    <string name="defense5_tips">Reduce daño de tierra recibido</string>																			
    <string name="hp">Vida</string>																			
    <string name="hp_tips">Cuando la vida llega a cero, el héroe o tropa caerá</string>																			
    <string name="fatal_rate">CRÍT</string>																			
    <string name="fatal_rate_tips">Probabilidad de daño adicional masivo</string>																			
    <string name="fatal_damage">D.C.</string>																			
    <string name="fatal_damage_tips">Daño adicional por crítico</string>																			
    <string name="dodge">ESQ</string>																			
    <string name="dodge_tips">Esquiva completa evita todo el daño</string>																			
    <string name="speed">Velocidad</string>																			
    <string name="speed_tips">Determina prioridad de acción según velocidad</string>																			
    <string name="skill_tree">Elemento</string>																			
</resources>																			