<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
  <string name="cast_skill">Released skills</string>																			
  <string name="gain_permanent_debuff">Gained a permanent debuff</string>																			
  <string name="gain_permanent_buff">Gained a permanent benefit</string>																			
  <string name="execute_failed">The kill was released, but the target was spared from death, so the kill was ineffective.</string>																			
  <string name="execute_not_work">You have released a kill, but the opponent\'s current HP is greater than your maximum HP, so the kill is invalid.</string>																			
  <string name="execute_done">Released the kill and successfully killed</string>																			
  <string name="gain_avoid_death">Gained immunity from death</string>																			
  <string name="avoid_death_failed">The death exemption was granted, but the opponent ignored it, so the death exemption is invalid.</string>																			
  <string name="direct_win">Released, you win the battle directly</string>																			
  <string name="gain_enhancement">Gained skill enhancement</string>																			
  <string name="enhancement_skip">However, the enhanced target skill is not learned</string>																			
  <string name="damage_record">Damage Record:</string>																			
  <string name="attacker_with_comma">Attacker:</string>																			
  <string name="defender_with_comma">Defender:</string>																			
  <string name="initial_damage_with_comma">Initial damage:</string>																			
  <string name="fatal_with_comma">CRI:</string>																			
  <string name="fatal_damage_with_comma">CRD:</string>																			
  <string name="race_damage_with_comma">Type of Damage:</string>																			
  <string name="normal_attack_with_comma">Normal Attack Damage:</string>																			
  <string name="skill_damage_with_comma">Other Damage Increases:</string>																			
  <string name="attacker_damage_inc">Damage increase/decrease (Attacker):</string>																			
  <string name="attacker_damage_inc_all">All damage increases and decreases (Attacker):</string>																			
  <string name="pierce_attacker">Penetration (Attacker):</string>																			
  <string name="damage_value">Damage:</string>																			
  <string name="defender_init_defense">Initial DEF of defender</string>																			
  <string name="defender_real_defense">Actual DEF of the defender</string>																			
  <string name="defender_reduce_damage">Defensive Damage Reduction Effect (Defender):</string>																			
  <string name="defender_reduce_value">Damage reduction (defender):</string>																			
  <string name="defender_race_reduce">Defense troops damage reduction:</string>																			
  <string name="defender_all_reduce">All damage reduction for the defender:</string>																			
  <string name="defender_immune">Defender Immunity:</string>																			
  <string name="defender_holy_shield">Defender\'s Shield:</string>																			
  <string name="defender_dodge">DOD:</string>																			
  <string name="single_damage">Single damage limit:</string>																			
  <string name="final_damage">Final Damage:</string>																			
  <string name="single_damage_protect">Single Damage Protection:</string>																			
  <string name="yes">Yes</string>																			
  <string name="no">No</string>																			
  <string name="skill_typ1">Physics</string>																			
  <string name="skill_type2">Air</string>																			
  <string name="skill_type3">Fire</string>																			
  <string name="skill_type4">Water</string>																			
  <string name="skill_type5">Earth</string>																			
  <string name="ones_turn">Round:</string>																			
  <string name="over_turn">You failed to defeat the opponent within the specified rounds, and the battle failed.</string>																			
  <string name="release">Release</string>																			
  <string name="gain">Get</string>																			
  <string name="you_release">You release</string>																			
  <string name="let">Make</string>																			
  <string name="lost">Loss</string>																			
  <string name="control_immune">Immune to control, cannot be</string>																			
  <string name="trigger_multi_cast">Triggered a morale boost</string>																			
  <string name="forbid_heal_tip">Affected by [Fright], unable to recover HP.</string>																			
  <string name="frenzy_tips">Affected by [Frenzy], randomly select a target.</string>																			
  <string name="disarm_tips">Affected by [Entangle], unable to launch normal attacks.</string>																			
  <string name="silent_tips">Affected by [Petrification], unable to release active skills.</string>																			
  <string name="palsy_tips">Affected by [Blindness], unable to release triggering skills.</string>																			
  <string name="ban_skill_tip1">Seal</string>																			
  <string name="ban_skill_tip2">】 effect, the skill cannot be released.</string>																			
  <string name="heal">Recover</string>																			
  <string name="layer">layer</string>																			
  <string name="unstack">Not stackable</string>																			
  <string name="infinite">Unlimited</string>																			
  <string name="rage">Violent</string>																			
  <string name="unbreakable">Invincible</string>																			
  <string name="prop1">Valor</string>																			
  <string name="prop2">Wisdom</string>																			
  <string name="prop3">Commander</string>																			
  <string name="prop4">Internal affairs</string>																			
  <string name="prop5">Charm</string>																			
  <string name="prop6">Empty one</string>																			
  <string name="holy_shield">Aegis</string>																			
  <string name="race1">Melee</string>																			
  <string name="race2">Ranged</string>																			
  <string name="race3">Fly</string>																			
  <string name="race4">Spell</string>																			
  <string name="race5">Hero</string>																			
  <string name="group1">Castle</string>																			
  <string name="group2">Barrier</string>																			
  <string name="group3">Tower</string>																			
  <string name="group4">Hell</string>																			
  <string name="group5">Cemetery</string>																			
  <string name="group6">Underground</string>																			
  <string name="group7">Stronghold</string>																			
  <string name="group8">Fortress</string>																			
  <string name="group9">Element</string>																			
    <string name="group10">Cove Town</string>																			
    <string name="group11">Factory</string>																			
    <string name="group19">None</string>																			
  <string name="dispelled">Dispelled</string>																			
  <string name="cant_dispel">Immune to debuff dispels, cannot be dispelled</string>																			
  <string name="cant_dispel2">Immune buff dispel, cannot be dispelled</string>																			
  <string name="defeated_the_enemy">You defeated the enemy</string>																			
  <string name="level">Level</string>																			
  <string name="level1">Level</string>																			
  <string name="death">Die</string>																			
  <string name="continuous_damage">Continuous Damage</string>																			
  <string name="continued_treatment">Ongoing treatment</string>																			
  <string name="drink">Draw</string>																			
  <string name="i_achieved_positive_results">I gain</string>																			
  <string name="i_achieved_negative_effects">I get debuffs</string>																			
  <string name="dispel_the_opponent_buff">Dispel the enemy\'s buff</string>																			
  <string name="dispel_your_own_buff">Dispel self-buff</string>																			
  <string name="damage_self">Hurt yourself</string>																			
  <string name="cure_yourself">Restore your HP</string>																			
  <string name="free_sb_from_death">Release from death</string>																			
  <string name="release_a_skill">Release Skills</string>																			
  <string name="got_damage">Getting damage</string>																			
  <string name="got_hurt">Cause damage</string>																			
  <string name="successful_block">Successfully dodge</string>																			
  <string name="start_of_the_round">Round starts</string>																			
  <string name="end_of_the_round">End of Round</string>																			
  <string name="call_upon_a_servant">Summoning Creatures</string>																			
  <string name="no_dispelling_of_target_buffs">No target dispelling effect</string>																			
  <string name="minion_already_exists">The summoned creature already exists and cannot be summoned again.</string>																			
  <string name="no_position_for_minion">No vacancies</string>																			
  <string name="summoned">Summoned</string>																			
  <string name="designated_skill_strike_rate_increase">CRI Increase</string>																			
  <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">Increased release times</string>																			
  <string name="immunity_to_blocking_for_specified_skills">Immune to dodge</string>																			
  <string name="designated_skills_ignore_stance">Ignore Shields</string>																			
  <string name="designated_skills_ignore_invincibility">Ignore invincibility/immunity</string>																			
  <string name="designated_skills_ignore_taunts">Ignore taunts</string>																			
  <string name="cannot_be_dispersed">Buffs/debuffs cannot be dispelled</string>																			
  <string name="increased_damage_dealt_by_designated_skills">Damage increased</string>																			
  <string name="designated_skills_deal_less_damage">Damage reduction</string>																			
  <string name="designated_skill_cd_increase">Cooling improvement</string>																			
  <string name="designated_skill_cd_decrease">Cooling down</string>																			
  <string name="increased_number_of_rounds">Increased buff/debuff duration</string>																			
  <string name="decreased_number_of_rounds">The number of rounds that buffs/debuffs last is reduced</string>																			
  <string name="increased_probability_of_releasing_designated_skills">Release probability increased</string>																			
  <string name="reduced_probability_of_releasing_designated_skills">Reduced release probability</string>																			
  <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">Increase the number of releases per round</string>																			
  <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">Reduced the number of releases per round</string>																			
  <string name="increase_in_the_number_of_restricted_releases">Increased the number of times the battle limit is released</string>																			
  <string name="reduced_limit_on_number_of_releases">The number of times the battle limit is released is reduced</string>																			
  <string name="increased_blood_absorption_rate_of_skills">Absorb and improve</string>																			
  <string name="designated_skills_ignore_enemies">Ignore enemy DEF</string>																			
  <string name="skill_damage_type_changes_to">Damage type changed to</string>																			
  <string name="designated_skills_vs_race">Pair Type</string>																			
  <string name="damage_increase">Damage increased</string>																			
  <string name="damage_decrease">Damage reduction</string>																			
  <string name="gain_split_effect">Get [Splash]</string>																			
  <string name="fatal">CRI</string>																			
  <string name="damage1">Physics</string>																			
  <string name="damage2">Air</string>																			
  <string name="damage3">Fire</string>																			
  <string name="damage4">Water</string>																			
  <string name="damage5">Earth</string>																			
  <string name="defense1">DEF</string>																			
  <string name="defense2">RES</string>																			
  <string name="defense_name1">DEF</string>																			
  <string name="defense_name2">AR</string>																			
  <string name="defense_name3">FR</string>																			
  <string name="defense_name4">WR</string>																			
  <string name="defense_name5">ER</string>																			
  <string name="attack">ATK</string>																			
  <string name="attack_tips">Determines the intensity of each type of damage</string>																			
  <string name="defense1_tips">Reduces physical damage</string>																			
  <string name="defense2_tips">Reduces damage when receiving air damage</string>																			
  <string name="defense3_tips">Reduces damage when receiving fire damage</string>																			
  <string name="defense4_tips">Reduces damage when receiving water damage</string>																			
  <string name="defense5_tips">Reduces damage when receiving earth damage</string>																			
  <string name="hp">HP</string>																			
  <string name="hp_tips">When the HP reaches zero, the hero or soldier will die.</string>																			
  <string name="fatal_rate">CRI</string>																			
  <string name="fatal_rate_tips">Chance of causing additional large amounts of damage</string>																			
  <string name="fatal_damage">CRD</string>																			
  <string name="fatal_damage_tips">The extra damage ratio caused when a critical hit is triggered</string>																			
  <string name="dodge">DOD</string>																			
  <string name="dodge_tips">If you dodge successfully, the damage will be completely avoided.</string>																			
  <string name="speed">SPE</string>																			
  <string name="speed_tips">Determines the priority of hero or unit action. The higher the speed, the earlier the action.</string>																			
  <string name="skill_tree">System</string>																			
</resources>																			