plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlinx-serialization'
    id 'kotlin-kapt'
}

android {
    compileSdk 35

    defaultConfig {
        minSdk 21
        targetSdk 35
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_19
        targetCompatibility JavaVersion.VERSION_19
    }
    kotlinOptions {
        jvmTarget = '19'
    }
    namespace 'com.moyu.core'
}

dependencies {
    // coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
    implementation("app.softwork:kotlinx-uuid-core:0.0.12")
}