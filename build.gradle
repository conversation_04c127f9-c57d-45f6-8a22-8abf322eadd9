// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        mavenCentral()
        maven { url 'https://repo1.maven.org/maven2/' }
        maven {
            url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        }
        maven {
            url = uri("https://artifact.bytedance.com/repository/pangle/")
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
        maven {
            url "https://repos.xiaomi.com/maven"
            credentials {
                username 'mi-gamesdk'
                password 'AKCp8mYeLuhuaGj6bK1XK7t2w4CsPuGwg6GpQdZ9cat7K59y5sD7Tx3dHjJcFrBGj3TQ4vi7g'
            }
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.25'
        classpath "org.jetbrains.kotlin:kotlin-serialization:1.9.25"
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}