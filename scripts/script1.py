# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os 
from os import path
from PIL import Image
import re

from skimage import io
imagePath = r"/Users/<USER>/Desktop/test"

for parent, _, fileNames in os.walk(imagePath):
   for fileName in fileNames:
        realFilePath = path.join(imagePath, fileName)
        try:
            im = Image.open(realFilePath)
            im.load()
            size = im.size
            width, height = size
            # if (re.match(r"role_\d+", fileName) != None):
            #      print(fileName)
            #      print(re.match(r"role_\d+", fileName).group())
    
               
            targetWidth = 512
            targetHeight = 600          
            if (width > targetWidth):
                print("宽度超了 %s %d %d" %(fileName, width, height))
                rate = targetWidth / width
                target_height = int(height * rate)     # 直接将高调整为目标尺寸
                target_width = int(width * rate) # 宽以相同的比例放缩
                print("缩小为%d,%d" % (target_width, target_height))
                im = im.resize((target_width, target_height), Image.ANTIALIAS) # 将高和宽放缩
                im.save(realFilePath)
            elif (height > targetHeight):
                print("高度超了 %s %d %d" %(fileName, width, height))
                rate = targetHeight / height
                target_height = int(height * rate)    # 直接将高调整为目标尺寸
                target_width = int(width * rate) # 宽以相同的比例放缩
                print("缩小为%d,%d" % (target_width, target_height))
                im = im.resize((target_width, target_height), Image.ANTIALIAS) # 将高和宽放缩
                im.save(realFilePath)
        except:
            print("1")