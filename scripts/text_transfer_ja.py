# -*- coding: utf-8 -*-
import os
import json
import pathlib
import re
from collections import defaultdict
from xml.etree import ElementTree as ET

from openai import OpenAI

# DeepSeek R1 配置
DEEPSEEK_API_KEY = "***********************************"
BATCH_SIZE = 50  # 根据API限制调整

# 路径配置
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))
DIR_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/assets/")
STRING_PATHS = [
    pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values/strings.xml"),
    pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/oversea/res/values/strings.xml"),
    pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values/strings.xml")
]

# 初始化客户端
client = OpenAI(
    api_key=DEEPSEEK_API_KEY,
    base_url="https://api.deepseek.com/v1"
)

def is_translatable(part: str) -> bool:
    """简单判断是否包含中文，并打印包含中文的字符串"""
    has_chinese = bool(re.search(r'[\u4e00-\u9fff]', part))
    if has_chinese:
        print("包含中文:", part)
    return has_chinese


def system_prompt_for(lang: str) -> str:
    """返回不同目标语言的 system_prompt"""


    prompts = {
        'en': 'You are a professional game localization expert, translate Chinese to natural English.',
        'ja': 'あなたはプロのゲームローカライゼーションの専門家です。以下の中国語を自然な日本語に翻訳してください。',
        'ko': '당신은 전문 게임 현지화 전문가입니다. 아래 중국어 문장을 자연스러운 한국어로 번역해 주세요.',
        'zh-tw': '您是專業遊戲本地化專家，請將以下中文翻譯成自然的繁體中文。',
        # 新增东南亚语言 :cite[2]:cite[5]
        'id': 'Ahli lokalisasi game profesional, terjemahkan bahasa Tionghoa ke bahasa Indonesia alami',  # 印尼语
        'vi': 'Chuyên gia bản địa hóa game chuyên nghiệp, dịch tiếng Trung sang tiếng Việt tự nhiên',  # 越南语
        'th': 'ผู้เชี่ยวชาญด้านการแปลเกมมืออาชีพ แปลภาษาจีนเป็นภาษาไทยธรรมชาติ',  # 泰语

        # 其他主流语言扩展
        'ms': 'Pakar lokalisasi permainan profesional, terjemahkan Cina ke Melayu semula jadi',  # 马来语:cite[5]
        'fil': 'Propesyonal na game localization expert, isalin ang Chinese sa natural na Filipino',  # 菲律宾语
        'hi': 'पेशेवर गेम स्थानीयकरण विशेषज्ञ, चीनी को प्राकृतिक हिंदी में अनुवाद करें' , # 印地语
        # 新增伊比利亚及拉丁美洲语言
        'es': 'Traductor profesional de localización de juegos, convierta el chino a español natural',  # 西班牙语
        'pt': 'Especialista em localização de jogos profissional, traduza chinês para português natural',  # 葡萄牙语
    }
    return prompts.get(lang, prompts['en'])


def translate_batch(texts, lang):
    LANG_DISPLAY = {
     'id': '印尼语',
     'vi': '越南语',
     'th': '泰语',
     # ...
    }
    """
    使用官方SDK的批处理方法。
    要求模型以纯JSON数组的形式返回翻译结果，与输入的 texts 一一对应。
    """
    if not texts:
        return {}


    # 1) 构建系统提示
    system_prompt = system_prompt_for(lang)

    # 2) 构建 user 提示，要求严格 JSON 数组输出
    #    为了防止翻译混入额外文本，这里做了强约束
    user_prompt = (
       f"请将下面的多行游戏配置和文案表中出现的中文文本翻译成 **{LANG_DISPLAY.get(lang,'目标语言')}**，"
        "如果是兵种装备宝物名字，参考《魔法门之英雄无敌III：埃拉西亚的光复》（英语：Heroes of Might and Magic III: The Restoration of Erathia）,"
        "不要包含额外说明或换行，只输出一个数组，数组元素的数量必须和输入一致，"
        "示例：\n[\"译文1\",\"译文2\", ...]\n\n"
        "同时请尽量简洁精准，不要使用冗余词句，严格保留类似换行符，通配符%1$d %2$s。\n\n"
        f"需要翻译的文本列表(共 {len(texts)} 行):\n"
        + json.dumps(texts, ensure_ascii=False)
        + "\n\n注意：只能返回 JSON 数组，不要输出其他。"
    )

    # 3) 发送请求
    response = None
    try:
        response = client.chat.completions.create(
            model="deepseek-chat",  # 指定R1模型:deepseek-reasoner V3:deepseek-chat
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user",   "content": user_prompt}
            ],
            temperature=0.3,
            top_p=0.95
        )
    except Exception as e:
        print(f"❌ 翻译接口请求失败: {e}")
        return {}

    if not response or not response.choices:
        print("❌ 翻译接口返回空响应。")
        return {}

    # 4) 解析返回文本
    raw_text = response.choices[0].message.content.strip()
    print(f"\n[DEBUG] AI原始返回: {raw_text}\n")

    # 尝试用 json.loads 解析
    try:
        translated_list = json.loads(raw_text)
    except json.JSONDecodeError as e:
        print("❌ 翻译返回内容不是合法 JSON:", e)
        return {}

    if not isinstance(translated_list, list):
        print("❌ 翻译返回的JSON结构不是列表:", translated_list)
        return {}

    # 5) 校验长度是否和 texts 一致
    if len(translated_list) != len(texts):
        print(f"❌ 返回的翻译数量({len(translated_list)})与输入数量({len(texts)})不一致。")
        return {}

    # 6) 组装为 {原文: 译文} 的映射
    results = {}
    for orig, trans in zip(texts, translated_list):
        # 由于是游戏内翻译，一般不期望含有制表符
        if isinstance(trans, str):
            trans = trans.replace('\t', ' ')
        results[orig] = trans

    return results

def translate_text(text_parts, dest):
    """
    将文本去重后，分批做JSON化翻译，合并最终结果返回。
    """
    unique_texts = list(set(text_parts))
    translations = {}

    # 分批翻译
    for i in range(0, len(unique_texts), BATCH_SIZE):
        batch = unique_texts[i:i+BATCH_SIZE]
        batch_trans = translate_batch(batch, dest)
        translations.update(batch_trans)

    return translations

def process_files_in_directory(directory, dest):
    """
    1. 扫描目录下所有文本文件，收集可翻译的行列位置。
    2. 分批翻译，并得到 translations = {原中文: 译文}
    3. 将所有翻译更新一次性写回对应文件（只写一次，避免乱序）。
    """
    file_structure = defaultdict(list)

    # 收集所有可翻译文本及其所在位置
    for root, _, files in os.walk(directory):
        for file in files:
            # 跳过不需要的文件
            if file.endswith((".DS_Store", "pool.txt", "common.txt")):
                continue
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line_idx, line in enumerate(lines):
                # 跳过空行
                if not line.strip():
                    continue
                parts = line.split('\t')
                for part_idx, part in enumerate(parts):
                    if is_translatable(part):
                        file_structure[part].append((file_path, line_idx, part_idx))

    # 如果没有可翻译内容
    if not file_structure:
        print("没有检测到可翻译的中文文本")
        return

    # 执行翻译
    texts = list(file_structure.keys())
    translations = translate_text(texts, dest)

    # 记录写回需求：file_changes[path][line_idx][part_idx] = 翻译后字符串
    file_changes = defaultdict(lambda: defaultdict(dict))
    for text, locations in file_structure.items():
        translated = translations.get(text, text)  # 若翻译失败就用原文
        for path, line_idx, part_idx in locations:
            file_changes[path][line_idx][part_idx] = translated

    # 统一写回
    for path, line_map in file_changes.items():
        with open(path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for line_idx, part_dict in line_map.items():
            parts = lines[line_idx].split('\t')
            for part_idx, new_text in part_dict.items():
                if part_idx < len(parts):
                    # 再保险一下，防止翻译中意外出现 \t
                    new_text = new_text.replace('\t', ' ')
                    parts[part_idx] = new_text
            lines[line_idx] = '\t'.join(parts)

        with open(path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

    print("翻译写回完成！")

def process_string_files(dest):
    for path in STRING_PATHS:
        if not path.exists():
            continue

        tree = ET.parse(path)
        root = tree.getroot()
        texts = []
        elements = []

        for elem in root.findall('string'):
            if elem.text and is_translatable(elem.text):
                texts.append(elem.text)
                elements.append(elem)

        if texts:
            translations = translate_text(texts, dest)
            for elem, orig in zip(elements, texts):
                elem.text = translations.get(orig, orig)
            # 写回
            tree.write(path, encoding='utf-8', xml_declaration=True)

if __name__ == "__main__":
    target_lang = 'id'  # 可选: en/ja/ko/zh-tw
    process_files_in_directory(DIR_PATH, target_lang)
    process_string_files(target_lang)
